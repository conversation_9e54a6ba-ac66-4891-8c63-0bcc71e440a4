/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(home)/page";
exports.ids = ["app/(home)/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(home)%2Fpage&page=%2F(home)%2Fpage&appPaths=%2F(home)%2Fpage&pagePath=private-next-app-dir%2F(home)%2Fpage.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(home)%2Fpage&page=%2F(home)%2Fpage&appPaths=%2F(home)%2Fpage&pagePath=private-next-app-dir%2F(home)%2Fpage.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(home)/layout.tsx */ \"(rsc)/./src/app/(home)/layout.tsx\"));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\nconst page8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(home)/page.tsx */ \"(rsc)/./src/app/(home)/page.tsx\"));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(home)',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page8, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [module5, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\"],\n'forbidden': [module6, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module7, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [module0, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module1, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\"],\n'not-found': [module2, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module3, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module4, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(home)/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(home)%2Fpage&page=%2F(home)%2Fpage&appPaths=%2F(home)%2Fpage&pagePath=private-next-app-dir%2F(home)%2Fpage.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CAlertSection%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AlertSection%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CCallUs%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CAlertSection%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AlertSection%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CCallUs%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@nextui-org/button/dist/index.mjs */ \"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/specific/AlertSection/index.tsx */ \"(rsc)/./src/components/specific/AlertSection/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/specific/CallUs/index.tsx */ \"(rsc)/./src/components/specific/CallUs/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CAlertSection%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AlertSection%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CCallUs%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@nextui-org/button/dist/index.mjs */ \"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MG5leHR1aS1vcmclNUMlNUNidXR0b24lNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE1BQXdLO0FBQ3hLO0FBQ0EsZ05BQXNMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQG5leHR1aS1vcmdcXFxcYnV0dG9uXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRzpcXFxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXFxcYXBwICgyKVxcXFxhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ClientProviders.tsx */ \"(rsc)/./src/components/global/ClientProviders.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ErrorBoundary.tsx */ \"(rsc)/./src/components/global/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(home)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(home)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(home)/layout.tsx */ \"(rsc)/./src/app/(home)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Qyhob21lKSU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRzpcXFxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXFxcYXBwICgyKVxcXFxhcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFwoaG9tZSlcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(home)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQTZHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkc6XFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcYXBwICgyKVxcYXBwXFxmcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/(home)/layout.tsx":
/*!***********************************!*\
  !*** ./src/app/(home)/layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserContext: () => (/* binding */ UserContext),
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const UserContext = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call UserContext() from the server but UserContext is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx",
"UserContext",
);/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/(home)/page.tsx":
/*!*********************************!*\
  !*** ./src/app/(home)/page.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_specific_AlertSection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/specific/AlertSection */ \"(rsc)/./src/components/specific/AlertSection/index.tsx\");\n/* harmony import */ var _components_specific_CallUs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/specific/CallUs */ \"(rsc)/./src/components/specific/CallUs/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nextui-org/button */ \"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Siren_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Siren!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/siren.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Siren_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Siren!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n\n\nasync function Home(props) {\n    // Sample data to use as fallback if API calls fail\n    const sampleAlerts = [\n        {\n            id: \"1\",\n            user_first_name: \"أحمد\",\n            user_last_name: \"السيد\",\n            location: \"القدس - عين علي، شارع مدرسة الثورية - فلسطين\",\n            created_at: new Date().toISOString(),\n            image: \"/placeholder.svg?height=200&width=200\"\n        },\n        {\n            id: \"2\",\n            user_first_name: \"محمد\",\n            user_last_name: \"إبراهيم\",\n            location: \"رام الله - شارع الإرسال - فلسطين\",\n            created_at: new Date().toISOString(),\n            image: \"/placeholder.svg?height=200&width=200\"\n        },\n        {\n            id: \"3\",\n            user_first_name: \"سارة\",\n            user_last_name: \"خالد\",\n            location: \"بيت لحم - شارع المهد - فلسطين\",\n            created_at: new Date().toISOString(),\n            image: \"/placeholder.svg?height=200&width=200\"\n        }\n    ];\n    let offerHelpReqs = {\n        results: []\n    };\n    let medicalReqs = {\n        results: []\n    };\n    let dangerReqs = {\n        results: []\n    };\n    try {\n        // Safely fetch data with proper error handling\n        const fetchData = async (url)=>{\n            try {\n                const response = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.fetcher)(url, null, \"GET\");\n                if (!response.ok) {\n                    throw new Error(`API request failed with status ${response.status}`);\n                }\n                const text = await response.text();\n                // Check if the response is valid JSON before parsing\n                try {\n                    return text ? JSON.parse(text) : {\n                        results: []\n                    };\n                } catch (e) {\n                    console.error(\"Invalid JSON response:\", text.substring(0, 100));\n                    return {\n                        results: []\n                    };\n                }\n            } catch (error) {\n                console.error(`Error fetching ${url}:`, error);\n                return {\n                    results: []\n                };\n            }\n        };\n        // Fetch all data in parallel\n        const [offerData, medicalData, dangerData] = await Promise.all([\n            fetchData(\"/emergency/?emergency_type=O\"),\n            fetchData(\"/emergency/?emergency_type=M\"),\n            fetchData(\"/emergency/?emergency_type=D\")\n        ]);\n        offerHelpReqs = offerData;\n        medicalReqs = medicalData;\n        dangerReqs = dangerData;\n    } catch (error) {\n        console.error(\"Failed to fetch emergency requests:\", error);\n        // Use sample data as fallback\n        offerHelpReqs = {\n            results: sampleAlerts\n        };\n        medicalReqs = {\n            results: sampleAlerts\n        };\n        dangerReqs = {\n            results: sampleAlerts\n        };\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    id: \"send-emergency\",\n                    className: \"max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Siren_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"absolute top-3 left-3\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"أرسل تنبيهًا للطوارئ\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            as: (next_link__WEBPACK_IMPORTED_MODULE_4___default()),\n                            href: \"/add-application\",\n                            endContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Siren_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 25\n                            }, void 0),\n                            className: \"mx-auto\",\n                            color: \"danger\",\n                            children: \"أرسل إشعار للطوارئ\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"recieved-emergency\",\n                className: \"mt-[65vh]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-center\",\n                        children: \"إشعارات الطوارئ المستلمة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_specific_AlertSection__WEBPACK_IMPORTED_MODULE_1__.AlertSection, {\n                        data: offerHelpReqs.results,\n                        heading: \"طلبات التنبيه حول الإغاثة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_specific_AlertSection__WEBPACK_IMPORTED_MODULE_1__.AlertSection, {\n                        data: medicalReqs.results,\n                        heading: \"طلبات التنبيه حول الصحة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_specific_AlertSection__WEBPACK_IMPORTED_MODULE_1__.AlertSection, {\n                        data: dangerReqs.results,\n                        heading: \"طلبات التنبيه حول الخطر\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                lineNumber: 109,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_specific_CallUs__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\page.tsx\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/(home)/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"090f88dbe170\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA5MGY4OGRiZTE3MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/Tajawal-Regular.ttf\",\"weight\":\"400\"},{\"path\":\"./fonts/Tajawal-Bold.ttf\",\"weight\":\"700\"},{\"path\":\"./fonts/Tajawal-ExtraBold.ttf\",\"weight\":\"800\"},{\"path\":\"./fonts/Tajawal-Black.ttf\",\"weight\":\"900\"}],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":[{\\\"path\\\":\\\"./fonts/Tajawal-Regular.ttf\\\",\\\"weight\\\":\\\"400\\\"},{\\\"path\\\":\\\"./fonts/Tajawal-Bold.ttf\\\",\\\"weight\\\":\\\"700\\\"},{\\\"path\\\":\\\"./fonts/Tajawal-ExtraBold.ttf\\\",\\\"weight\\\":\\\"800\\\"},{\\\"path\\\":\\\"./fonts/Tajawal-Black.ttf\\\",\\\"weight\\\":\\\"900\\\"}],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_global_ClientProviders__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/global/ClientProviders */ \"(rsc)/./src/components/global/ClientProviders.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_global_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/global/ErrorBoundary */ \"(rsc)/./src/components/global/ErrorBoundary.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Palastine Emergency\",\n    description: \"Comming for help when you need us\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_ClientProviders__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                    richColors: true,\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextui-org/button */ \"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-6xl font-bold mb-4\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-semibold mb-6\",\n                children: \"الصفحة غير موجودة\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-600 max-w-md\",\n                children: \"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها.\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                href: \"/\",\n                color: \"primary\",\n                children: \"العودة للصفحة الرئيسية\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEyQztBQUNmO0FBRWIsU0FBU0U7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBMEI7Ozs7OzswQkFDeEMsOERBQUNFO2dCQUFHRixXQUFVOzBCQUE4Qjs7Ozs7OzBCQUM1Qyw4REFBQ0c7Z0JBQUVILFdBQVU7MEJBQThCOzs7Ozs7MEJBQzNDLDhEQUFDSixzREFBTUE7Z0JBQUNRLElBQUlQLGtEQUFJQTtnQkFBRVEsTUFBSztnQkFBSUMsT0FBTTswQkFBVTs7Ozs7Ozs7Ozs7O0FBS2pEIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxub3QtZm91bmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAbmV4dHVpLW9yZy9idXR0b25cIlxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNnhsIGZvbnQtYm9sZCBtYi00XCI+NDA0PC9oMT5cbiAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTZcIj7Yp9mE2LXZgdit2Kkg2LrZitixINmF2YjYrNmI2K/YqTwvaDI+XG4gICAgICA8cCBjbGFzc05hbWU9XCJtYi04IHRleHQtZ3JheS02MDAgbWF4LXctbWRcIj7Yudiw2LHYp9mL2Iwg2KfZhNi12YHYrdipINin2YTYqtmKINiq2KjYrdirINi52YbZh9inINi62YrYsSDZhdmI2KzZiNiv2Kkg2KPZiCDYqtmFINmG2YLZhNmH2Kcg2KPZiCDYrdiw2YHZh9inLjwvcD5cbiAgICAgIDxCdXR0b24gYXM9e0xpbmt9IGhyZWY9XCIvXCIgY29sb3I9XCJwcmltYXJ5XCI+XG4gICAgICAgINin2YTYudmI2K/YqSDZhNmE2LXZgdit2Kkg2KfZhNix2KbZitiz2YrYqVxuICAgICAgPC9CdXR0b24+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJMaW5rIiwiTm90Rm91bmQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImgyIiwicCIsImFzIiwiaHJlZiIsImNvbG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/global/ClientProviders.tsx":
/*!***************************************************!*\
  !*** ./src/components/global/ClientProviders.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/global/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/global/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/specific/AlertSection/index.tsx":
/*!********************************************************!*\
  !*** ./src/components/specific/AlertSection/index.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AlertSection: () => (/* binding */ AlertSection)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const AlertSection = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call AlertSection() from the server but AlertSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx",
"AlertSection",
);

/***/ }),

/***/ "(rsc)/./src/components/specific/CallUs/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/specific/CallUs/index.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   fetcher: () => (/* binding */ fetcher)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nconst API_URL = \"http://localhost:8000\";\nasync function fetcher(endpoint, data, method = \"GET\", token) {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    if (token) {\n        headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    const options = {\n        method,\n        headers,\n        next: {\n            revalidate: 60\n        }\n    };\n    if (data && method !== \"GET\") {\n        options.body = JSON.stringify(data);\n    }\n    try {\n        const url = `${API_URL}${endpoint}`;\n        console.log(`Fetching: ${url}`);\n        const response = await fetch(url, options);\n        // Log non-OK responses for debugging\n        if (!response.ok) {\n            console.warn(`API request failed: ${url} returned status ${response.status}`);\n        }\n        return response;\n    } catch (error) {\n        console.error(\"API request failed:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CAlertSection%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AlertSection%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CCallUs%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CAlertSection%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AlertSection%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CCallUs%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@nextui-org/button/dist/index.mjs */ \"(ssr)/./node_modules/@nextui-org/button/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/specific/AlertSection/index.tsx */ \"(ssr)/./src/components/specific/AlertSection/index.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/specific/CallUs/index.tsx */ \"(ssr)/./src/components/specific/CallUs/index.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CAlertSection%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22AlertSection%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cspecific%5C%5CCallUs%5C%5Cindex.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@nextui-org/button/dist/index.mjs */ \"(ssr)/./node_modules/@nextui-org/button/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MG5leHR1aS1vcmclNUMlNUNidXR0b24lNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE1BQXdLO0FBQ3hLO0FBQ0EsZ05BQXNMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQG5leHR1aS1vcmdcXFxcYnV0dG9uXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRzpcXFxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXFxcYXBwICgyKVxcXFxhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ClientProviders.tsx */ \"(ssr)/./src/components/global/ClientProviders.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ErrorBoundary.tsx */ \"(ssr)/./src/components/global/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(home)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(home)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/(home)/layout.tsx */ \"(ssr)/./src/app/(home)/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Qyhob21lKSU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGtLQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRzpcXFxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXFxcYXBwICgyKVxcXFxhcHBcXFxcZnJvbnRlbmRcXFxcc3JjXFxcXGFwcFxcXFwoaG9tZSlcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5C(home)%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQTZHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/(home)/layout.tsx":
/*!***********************************!*\
  !*** ./src/app/(home)/layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserContext: () => (/* binding */ UserContext),\n/* harmony export */   \"default\": () => (/* binding */ HomeLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_global_Footer__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/global/Footer */ \"(ssr)/./src/components/global/Footer.tsx\");\n/* harmony import */ var _components_global_Header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/global/Header */ \"(ssr)/./src/components/global/Header.tsx\");\n/* harmony import */ var _components_specific_Chat__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/specific/Chat */ \"(ssr)/./src/components/specific/Chat/index.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/react-cookie/esm/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ UserContext,default auto */ \n\n\n\n\n\n\n\n\n\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_5__.createContext)(null);\nfunction ErrorHandler() {\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useSearchParams)();\n    const error = params?.get(\"error\");\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"ErrorHandler.useEffect\": ()=>{\n            if (error === \"not-logged-in\") {\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"يجب عليك تسجيل الدخول للوصول إلى هذه الصفحة\");\n            }\n        }\n    }[\"ErrorHandler.useEffect\"], [\n        error\n    ]);\n    return null;\n}\nfunction HomeLayout({ children }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(true);\n    const [isValid, setIsValid] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_5__.useState)(null);\n    const [cookies] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_8__.useCookies)([\n        \"access\",\n        \"refresh\"\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_5__.useEffect)({\n        \"HomeLayout.useEffect\": ()=>{\n            const verifyToken = {\n                \"HomeLayout.useEffect.verifyToken\": async ()=>{\n                    try {\n                        if (!cookies.access) {\n                            setIsValid(false);\n                            setIsLoading(false);\n                            return;\n                        }\n                        const res = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.fetcher)(\"/auth/jwt/verify/\", {\n                            token: cookies.access\n                        }, \"POST\");\n                        if (res.status === 200) {\n                            setIsValid(true);\n                            const userRes = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.fetcher)(\"/users/me/\", null, \"GET\", cookies.access);\n                            const userData = await userRes.json();\n                            setUser(userData);\n                        } else {\n                            setIsValid(false);\n                        }\n                    } catch (error) {\n                        console.error(\"Error verifying token:\", error);\n                        setIsValid(false);\n                    } finally{\n                        setIsLoading(false);\n                    }\n                }\n            }[\"HomeLayout.useEffect.verifyToken\"];\n            verifyToken();\n        }\n    }[\"HomeLayout.useEffect\"], [\n        cookies.access\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            isValid,\n            isLoading,\n            user\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col min-h-screen relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Header__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                    className: \"flex-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_5__.Suspense, {\n                            fallback: null,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorHandler, {}, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 11\n                        }, this),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_Footer__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_specific_Chat__WEBPACK_IMPORTED_MODULE_3__.Chat, {}, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\layout.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwLyhob21lKS9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRWdEO0FBQ0E7QUFDRTtBQUNaO0FBQ0w7QUFDaUI7QUFFUztBQUNqQjtBQUNYO0FBa0J4QixNQUFNVyw0QkFBY0wsb0RBQWFBLENBQXFCLE1BQU07QUFFbkUsU0FBU007SUFDUCxNQUFNQyxTQUFTUixnRUFBZUE7SUFDOUIsTUFBTVMsUUFBUUQsUUFBUUUsSUFBSTtJQUUxQlIsZ0RBQVNBO2tDQUFDO1lBQ1IsSUFBSU8sVUFBVSxpQkFBaUI7Z0JBQzdCSix5Q0FBS0EsQ0FBQ0ksS0FBSyxDQUFDO1lBQ2Q7UUFDRjtpQ0FBRztRQUFDQTtLQUFNO0lBRVYsT0FBTztBQUNUO0FBRWUsU0FBU0UsV0FBVyxFQUFFQyxRQUFRLEVBQVM7SUFDcEQsTUFBTSxDQUFDQyxXQUFXQyxhQUFhLEdBQUdYLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ1ksU0FBU0MsV0FBVyxHQUFHYiwrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUNjLE1BQU1DLFFBQVEsR0FBR2YsK0NBQVFBLENBQWM7SUFDOUMsTUFBTSxDQUFDZ0IsUUFBUSxHQUFHZix3REFBVUEsQ0FBQztRQUFDO1FBQVU7S0FBVTtJQUVsREYsZ0RBQVNBO2dDQUFDO1lBQ1IsTUFBTWtCO29EQUFjO29CQUNsQixJQUFJO3dCQUNGLElBQUksQ0FBQ0QsUUFBUUUsTUFBTSxFQUFFOzRCQUNuQkwsV0FBVzs0QkFDWEYsYUFBYTs0QkFDYjt3QkFDRjt3QkFFQSxNQUFNUSxNQUFNLE1BQU14QixtREFBT0EsQ0FDdkIscUJBQ0E7NEJBQ0V5QixPQUFPSixRQUFRRSxNQUFNO3dCQUN2QixHQUNBO3dCQUdGLElBQUlDLElBQUlFLE1BQU0sS0FBSyxLQUFLOzRCQUN0QlIsV0FBVzs0QkFDWCxNQUFNUyxVQUFVLE1BQU0zQixtREFBT0EsQ0FDM0IsY0FDQSxNQUNBLE9BQ0FxQixRQUFRRSxNQUFNOzRCQUVoQixNQUFNSyxXQUFXLE1BQU1ELFFBQVFFLElBQUk7NEJBQ25DVCxRQUFRUTt3QkFDVixPQUFPOzRCQUNMVixXQUFXO3dCQUNiO29CQUNGLEVBQUUsT0FBT1AsT0FBTzt3QkFDZG1CLFFBQVFuQixLQUFLLENBQUMsMEJBQTBCQTt3QkFDeENPLFdBQVc7b0JBQ2IsU0FBVTt3QkFDUkYsYUFBYTtvQkFDZjtnQkFDRjs7WUFFQU07UUFDRjsrQkFBRztRQUFDRCxRQUFRRSxNQUFNO0tBQUM7SUFFbkIscUJBQ0UsOERBQUNmLFlBQVl1QixRQUFRO1FBQUNDLE9BQU87WUFBRWY7WUFBU0Y7WUFBV0k7UUFBSztrQkFDdEQsNEVBQUNjO1lBQUlDLFdBQVU7OzhCQUNiLDhEQUFDcEMsaUVBQU1BOzs7Ozs4QkFDUCw4REFBQ3FDO29CQUFLRCxXQUFVOztzQ0FDZCw4REFBQ2pDLDJDQUFRQTs0QkFBQ21DLFVBQVU7c0NBQ2xCLDRFQUFDM0I7Ozs7Ozs7Ozs7d0JBRUZLOzs7Ozs7OzhCQUVILDhEQUFDakIsaUVBQU1BOzs7Ozs4QkFDUCw4REFBQ0UsMkRBQUlBOzs7Ozs7Ozs7Ozs7Ozs7O0FBSWIiLCJzb3VyY2VzIjpbIkc6XFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcYXBwICgyKVxcYXBwXFxmcm9udGVuZFxcc3JjXFxhcHBcXChob21lKVxcbGF5b3V0LnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcblxuaW1wb3J0IEZvb3RlciBmcm9tIFwiQC9jb21wb25lbnRzL2dsb2JhbC9Gb290ZXJcIjtcbmltcG9ydCBIZWFkZXIgZnJvbSBcIkAvY29tcG9uZW50cy9nbG9iYWwvSGVhZGVyXCI7XG5pbXBvcnQgeyBDaGF0IH0gZnJvbSBcIkAvY29tcG9uZW50cy9zcGVjaWZpYy9DaGF0XCI7XG5pbXBvcnQgeyBmZXRjaGVyIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5pbXBvcnQgeyBTdXNwZW5zZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VDb29raWVzIH0gZnJvbSBcInJlYWN0LWNvb2tpZVwiO1xuaW1wb3J0IHsgdG9hc3QgfSBmcm9tIFwic29ubmVyXCI7XG5cbnR5cGUgUHJvcHMgPSB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfTtcblxudHlwZSBVc2VyQ29udGV4dCA9IHtcbiAgaXNWYWxpZDogYm9vbGVhbjtcbiAgaXNMb2FkaW5nOiBib29sZWFuO1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbn07XG5cbmV4cG9ydCB0eXBlIFVzZXIgPSB7XG4gIGlkOiBudW1iZXI7XG4gIGVtYWlsOiBzdHJpbmc7XG4gIGZpcnN0X25hbWU6IHN0cmluZztcbiAgbGFzdF9uYW1lOiBzdHJpbmc7XG4gIGlzX2FkbWluOiBib29sZWFuO1xufTtcblxuZXhwb3J0IGNvbnN0IFVzZXJDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVc2VyQ29udGV4dCB8IG51bGw+KG51bGwpO1xuXG5mdW5jdGlvbiBFcnJvckhhbmRsZXIoKSB7XG4gIGNvbnN0IHBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xuICBjb25zdCBlcnJvciA9IHBhcmFtcz8uZ2V0KFwiZXJyb3JcIik7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoZXJyb3IgPT09IFwibm90LWxvZ2dlZC1pblwiKSB7XG4gICAgICB0b2FzdC5lcnJvcihcItmK2KzYqCDYudmE2YrZgyDYqtiz2KzZitmEINin2YTYr9iu2YjZhCDZhNmE2YjYtdmI2YQg2KXZhNmJINmH2LDZhyDYp9mE2LXZgdit2KlcIik7XG4gICAgfVxuICB9LCBbZXJyb3JdKTtcblxuICByZXR1cm4gbnVsbDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZUxheW91dCh7IGNoaWxkcmVuIH06IFByb3BzKSB7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcbiAgY29uc3QgW2lzVmFsaWQsIHNldElzVmFsaWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtjb29raWVzXSA9IHVzZUNvb2tpZXMoW1wiYWNjZXNzXCIsIFwicmVmcmVzaFwiXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCB2ZXJpZnlUb2tlbiA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGlmICghY29va2llcy5hY2Nlc3MpIHtcbiAgICAgICAgICBzZXRJc1ZhbGlkKGZhbHNlKTtcbiAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGZldGNoZXIoXG4gICAgICAgICAgXCIvYXV0aC9qd3QvdmVyaWZ5L1wiLFxuICAgICAgICAgIHtcbiAgICAgICAgICAgIHRva2VuOiBjb29raWVzLmFjY2VzcyxcbiAgICAgICAgICB9LFxuICAgICAgICAgIFwiUE9TVFwiXG4gICAgICAgICk7XG5cbiAgICAgICAgaWYgKHJlcy5zdGF0dXMgPT09IDIwMCkge1xuICAgICAgICAgIHNldElzVmFsaWQodHJ1ZSk7XG4gICAgICAgICAgY29uc3QgdXNlclJlcyA9IGF3YWl0IGZldGNoZXIoXG4gICAgICAgICAgICBcIi91c2Vycy9tZS9cIixcbiAgICAgICAgICAgIG51bGwsXG4gICAgICAgICAgICBcIkdFVFwiLFxuICAgICAgICAgICAgY29va2llcy5hY2Nlc3NcbiAgICAgICAgICApO1xuICAgICAgICAgIGNvbnN0IHVzZXJEYXRhID0gYXdhaXQgdXNlclJlcy5qc29uKCk7XG4gICAgICAgICAgc2V0VXNlcih1c2VyRGF0YSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgc2V0SXNWYWxpZChmYWxzZSk7XG4gICAgICAgIH1cbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB2ZXJpZnlpbmcgdG9rZW46XCIsIGVycm9yKTtcbiAgICAgICAgc2V0SXNWYWxpZChmYWxzZSk7XG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgfVxuICAgIH07XG5cbiAgICB2ZXJpZnlUb2tlbigpO1xuICB9LCBbY29va2llcy5hY2Nlc3NdKTtcblxuICByZXR1cm4gKFxuICAgIDxVc2VyQ29udGV4dC5Qcm92aWRlciB2YWx1ZT17eyBpc1ZhbGlkLCBpc0xvYWRpbmcsIHVzZXIgfX0+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgbWluLWgtc2NyZWVuIHJlbGF0aXZlXCI+XG4gICAgICAgIDxIZWFkZXIgLz5cbiAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgPFN1c3BlbnNlIGZhbGxiYWNrPXtudWxsfT5cbiAgICAgICAgICAgIDxFcnJvckhhbmRsZXIgLz5cbiAgICAgICAgICA8L1N1c3BlbnNlPlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9tYWluPlxuICAgICAgICA8Rm9vdGVyIC8+XG4gICAgICAgIDxDaGF0IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L1VzZXJDb250ZXh0LlByb3ZpZGVyPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkZvb3RlciIsIkhlYWRlciIsIkNoYXQiLCJmZXRjaGVyIiwiU3VzcGVuc2UiLCJ1c2VTZWFyY2hQYXJhbXMiLCJjcmVhdGVDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJ1c2VDb29raWVzIiwidG9hc3QiLCJVc2VyQ29udGV4dCIsIkVycm9ySGFuZGxlciIsInBhcmFtcyIsImVycm9yIiwiZ2V0IiwiSG9tZUxheW91dCIsImNoaWxkcmVuIiwiaXNMb2FkaW5nIiwic2V0SXNMb2FkaW5nIiwiaXNWYWxpZCIsInNldElzVmFsaWQiLCJ1c2VyIiwic2V0VXNlciIsImNvb2tpZXMiLCJ2ZXJpZnlUb2tlbiIsImFjY2VzcyIsInJlcyIsInRva2VuIiwic3RhdHVzIiwidXNlclJlcyIsInVzZXJEYXRhIiwianNvbiIsImNvbnNvbGUiLCJQcm92aWRlciIsInZhbHVlIiwiZGl2IiwiY2xhc3NOYW1lIiwibWFpbiIsImZhbGxiYWNrIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/(home)/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextui-org/button */ \"(ssr)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error(\"Application error:\", error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"حدث خطأ غير متوقع\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-6 text-gray-600\",\n                children: \"نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                        color: \"primary\",\n                        onClick: ()=>reset(),\n                        children: \"إعادة المحاولة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                        variant: \"bordered\",\n                        onClick: ()=>window.location.href = \"/\",\n                        children: \"العودة للصفحة الرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2Vycm9yLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRTJDO0FBQ1Y7QUFFbEIsU0FBU0UsTUFBTSxFQUM1QkMsS0FBSyxFQUNMQyxLQUFLLEVBSU47SUFDQ0gsZ0RBQVNBOzJCQUFDO1lBQ1JJLFFBQVFGLEtBQUssQ0FBQyxzQkFBc0JBO1FBQ3RDOzBCQUFHO1FBQUNBO0tBQU07SUFFVixxQkFDRSw4REFBQ0c7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFHRCxXQUFVOzBCQUEwQjs7Ozs7OzBCQUN4Qyw4REFBQ0U7Z0JBQUVGLFdBQVU7MEJBQXFCOzs7Ozs7MEJBQ2xDLDhEQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNQLDhEQUFNQTt3QkFBQ1UsT0FBTTt3QkFBVUMsU0FBUyxJQUFNUDtrQ0FBUzs7Ozs7O2tDQUdoRCw4REFBQ0osOERBQU1BO3dCQUFDWSxTQUFRO3dCQUFXRCxTQUFTLElBQU9FLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO2tDQUFNOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNaEYiLCJzb3VyY2VzIjpbIkc6XFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcYXBwICgyKVxcYXBwXFxmcm9udGVuZFxcc3JjXFxhcHBcXGVycm9yLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiQG5leHR1aS1vcmcvYnV0dG9uXCJcbmltcG9ydCB7IHVzZUVmZmVjdCB9IGZyb20gXCJyZWFjdFwiXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVycm9yKHtcbiAgZXJyb3IsXG4gIHJlc2V0LFxufToge1xuICBlcnJvcjogRXJyb3IgJiB7IGRpZ2VzdD86IHN0cmluZyB9XG4gIHJlc2V0OiAoKSA9PiB2b2lkXG59KSB7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY29uc29sZS5lcnJvcihcIkFwcGxpY2F0aW9uIGVycm9yOlwiLCBlcnJvcilcbiAgfSwgW2Vycm9yXSlcblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCBtYi00XCI+2K3Yr9irINiu2LfYoyDYutmK2LEg2YXYqtmI2YLYuTwvaDI+XG4gICAgICA8cCBjbGFzc05hbWU9XCJtYi02IHRleHQtZ3JheS02MDBcIj7Zhti52KrYsNixINi52YYg2YfYsNinINin2YTYrti32KMuINmK2LHYrNmJINin2YTZhdit2KfZiNmE2Kkg2YXYsdipINij2K7YsdmJINij2Ygg2KfZhNi52YjYr9ipINil2YTZiSDYp9mE2LXZgdit2Kkg2KfZhNix2KbZitiz2YrYqS48L3A+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTRcIj5cbiAgICAgICAgPEJ1dHRvbiBjb2xvcj1cInByaW1hcnlcIiBvbkNsaWNrPXsoKSA9PiByZXNldCgpfT5cbiAgICAgICAgICDYpdi52KfYr9ipINin2YTZhdit2KfZiNmE2KlcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDxCdXR0b24gdmFyaWFudD1cImJvcmRlcmVkXCIgb25DbGljaz17KCkgPT4gKHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gXCIvXCIpfT5cbiAgICAgICAgICDYp9mE2LnZiNiv2Kkg2YTZhNi12YHYrdipINin2YTYsdim2YrYs9mK2KlcbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkJ1dHRvbiIsInVzZUVmZmVjdCIsIkVycm9yIiwiZXJyb3IiLCJyZXNldCIsImNvbnNvbGUiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJjb2xvciIsIm9uQ2xpY2siLCJ2YXJpYW50Iiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/global/ClientProviders.tsx":
/*!***************************************************!*\
  !*** ./src/components/global/ClientProviders.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=NextUIProvider!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/system/dist/chunk-MNMJVVXA.mjs\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/react-cookie/esm/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ClientProviders({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_cookie__WEBPACK_IMPORTED_MODULE_1__.CookiesProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__.NextUIProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9nbG9iYWwvQ2xpZW50UHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUQ7QUFDSjtBQU9oQyxTQUFTRSxnQkFBZ0IsRUFBRUMsUUFBUSxFQUFTO0lBQ3pELHFCQUNFLDhEQUFDRix5REFBZUE7a0JBQ2QsNEVBQUNELGtHQUFjQTtzQkFBRUc7Ozs7Ozs7Ozs7O0FBR3ZCIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcZ2xvYmFsXFxDbGllbnRQcm92aWRlcnMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBOZXh0VUlQcm92aWRlciB9IGZyb20gXCJAbmV4dHVpLW9yZy9yZWFjdFwiO1xuaW1wb3J0IHsgQ29va2llc1Byb3ZpZGVyIH0gZnJvbSBcInJlYWN0LWNvb2tpZVwiO1xuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxDb29raWVzUHJvdmlkZXI+XG4gICAgICA8TmV4dFVJUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dFVJUHJvdmlkZXI+XG4gICAgPC9Db29raWVzUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTmV4dFVJUHJvdmlkZXIiLCJDb29raWVzUHJvdmlkZXIiLCJDbGllbnRQcm92aWRlcnMiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/global/ClientProviders.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/global/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/global/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextui-org/button */ \"(ssr)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ErrorBoundary({ children }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ErrorBoundary.useEffect\": ()=>{\n            const handleError = {\n                \"ErrorBoundary.useEffect.handleError\": (event)=>{\n                    console.error(\"Error caught by error boundary:\", event.error);\n                    setHasError(true);\n                    // Prevent the error from propagating\n                    event.preventDefault();\n                }\n            }[\"ErrorBoundary.useEffect.handleError\"];\n            window.addEventListener(\"error\", handleError);\n            return ({\n                \"ErrorBoundary.useEffect\": ()=>{\n                    window.removeEventListener(\"error\", handleError);\n                }\n            })[\"ErrorBoundary.useEffect\"];\n        }\n    }[\"ErrorBoundary.useEffect\"], []);\n    if (hasError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-[50vh] p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"حدث خطأ غير متوقع\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6 text-gray-600\",\n                    children: \"نعتذر عن هذا الخطأ. يرجى تحديث الصفحة أو العودة إلى الصفحة الرئيسية.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                            color: \"primary\",\n                            onClick: ()=>window.location.reload(),\n                            children: \"تحديث الصفحة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                            variant: \"bordered\",\n                            onClick: ()=>window.location.href = \"/\",\n                            children: \"العودة للصفحة الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9nbG9iYWwvRXJyb3JCb3VuZGFyeS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUkyQztBQUNBO0FBTTVCLFNBQVNHLGNBQWMsRUFBRUMsUUFBUSxFQUFzQjtJQUNwRSxNQUFNLENBQUNDLFVBQVVDLFlBQVksR0FBR0osK0NBQVFBLENBQUM7SUFFekNELGdEQUFTQTttQ0FBQztZQUNSLE1BQU1NO3VEQUFjLENBQUNDO29CQUNuQkMsUUFBUUMsS0FBSyxDQUFDLG1DQUFtQ0YsTUFBTUUsS0FBSztvQkFDNURKLFlBQVk7b0JBQ1oscUNBQXFDO29CQUNyQ0UsTUFBTUcsY0FBYztnQkFDdEI7O1lBRUFDLE9BQU9DLGdCQUFnQixDQUFDLFNBQVNOO1lBRWpDOzJDQUFPO29CQUNMSyxPQUFPRSxtQkFBbUIsQ0FBQyxTQUFTUDtnQkFDdEM7O1FBQ0Y7a0NBQUcsRUFBRTtJQUVMLElBQUlGLFVBQVU7UUFDWixxQkFDRSw4REFBQ1U7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNDO29CQUFHRCxXQUFVOzhCQUEwQjs7Ozs7OzhCQUN4Qyw4REFBQ0U7b0JBQUVGLFdBQVU7OEJBQXFCOzs7Ozs7OEJBQ2xDLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNoQiw4REFBTUE7NEJBQUNtQixPQUFNOzRCQUFVQyxTQUFTLElBQU1SLE9BQU9TLFFBQVEsQ0FBQ0MsTUFBTTtzQ0FBSTs7Ozs7O3NDQUdqRSw4REFBQ3RCLDhEQUFNQTs0QkFBQ3VCLFNBQVE7NEJBQVdILFNBQVMsSUFBT1IsT0FBT1MsUUFBUSxDQUFDRyxJQUFJLEdBQUc7c0NBQU07Ozs7Ozs7Ozs7Ozs7Ozs7OztJQU1oRjtJQUVBLHFCQUFPO2tCQUFHcEI7O0FBQ1oiLCJzb3VyY2VzIjpbIkc6XFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcYXBwICgyKVxcYXBwXFxmcm9udGVuZFxcc3JjXFxjb21wb25lbnRzXFxnbG9iYWxcXEVycm9yQm91bmRhcnkudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB0eXBlIFJlYWN0IGZyb20gXCJyZWFjdFwiXG5cbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAbmV4dHVpLW9yZy9idXR0b25cIlxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiXG5cbnR5cGUgRXJyb3JCb3VuZGFyeVByb3BzID0ge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEVycm9yQm91bmRhcnkoeyBjaGlsZHJlbiB9OiBFcnJvckJvdW5kYXJ5UHJvcHMpIHtcbiAgY29uc3QgW2hhc0Vycm9yLCBzZXRIYXNFcnJvcl0gPSB1c2VTdGF0ZShmYWxzZSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IGhhbmRsZUVycm9yID0gKGV2ZW50OiBFcnJvckV2ZW50KSA9PiB7XG4gICAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgY2F1Z2h0IGJ5IGVycm9yIGJvdW5kYXJ5OlwiLCBldmVudC5lcnJvcilcbiAgICAgIHNldEhhc0Vycm9yKHRydWUpXG4gICAgICAvLyBQcmV2ZW50IHRoZSBlcnJvciBmcm9tIHByb3BhZ2F0aW5nXG4gICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpXG4gICAgfVxuXG4gICAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoXCJlcnJvclwiLCBoYW5kbGVFcnJvcilcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcihcImVycm9yXCIsIGhhbmRsZUVycm9yKVxuICAgIH1cbiAgfSwgW10pXG5cbiAgaWYgKGhhc0Vycm9yKSB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtWzUwdmhdIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIG1iLTRcIj7Yrdiv2Ksg2K7Yt9ijINi62YrYsSDZhdiq2YjZgti5PC9oMj5cbiAgICAgICAgPHAgY2xhc3NOYW1lPVwibWItNiB0ZXh0LWdyYXktNjAwXCI+2YbYudiq2LDYsSDYudmGINmH2LDYpyDYp9mE2K7Yt9ijLiDZitix2KzZiSDYqtit2K/ZitirINin2YTYtdmB2K3YqSDYo9mIINin2YTYudmI2K/YqSDYpdmE2Ykg2KfZhNi12YHYrdipINin2YTYsdim2YrYs9mK2KkuPC9wPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTRcIj5cbiAgICAgICAgICA8QnV0dG9uIGNvbG9yPVwicHJpbWFyeVwiIG9uQ2xpY2s9eygpID0+IHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKX0+XG4gICAgICAgICAgICDYqtit2K/ZitirINin2YTYtdmB2K3YqVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImJvcmRlcmVkXCIgb25DbGljaz17KCkgPT4gKHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gXCIvXCIpfT5cbiAgICAgICAgICAgINin2YTYudmI2K/YqSDZhNmE2LXZgdit2Kkg2KfZhNix2KbZitiz2YrYqVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiA8PntjaGlsZHJlbn08Lz5cbn1cbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJ1c2VFZmZlY3QiLCJ1c2VTdGF0ZSIsIkVycm9yQm91bmRhcnkiLCJjaGlsZHJlbiIsImhhc0Vycm9yIiwic2V0SGFzRXJyb3IiLCJoYW5kbGVFcnJvciIsImV2ZW50IiwiY29uc29sZSIsImVycm9yIiwicHJldmVudERlZmF1bHQiLCJ3aW5kb3ciLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImRpdiIsImNsYXNzTmFtZSIsImgyIiwicCIsImNvbG9yIiwib25DbGljayIsImxvY2F0aW9uIiwicmVsb2FkIiwidmFyaWFudCIsImhyZWYiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/components/global/ErrorBoundary.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/global/Footer.tsx":
/*!******************************************!*\
  !*** ./src/components/global/Footer.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Footer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./src/lib/data.ts\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/facebook.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/twitter.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/linkedin.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/youtube.js\");\n/* harmony import */ var _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Facebook,Instagram,Linkedin,Twitter,Youtube!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/instagram.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\n\nfunction Footer() {\n    const socialLinks = [\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"#\"\n        },\n        {\n            icon: _barrel_optimize_names_Facebook_Instagram_Linkedin_Twitter_Youtube_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"#\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n        className: \"bg-gray-100\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col lg:flex-row justify-between items-center gap-6 mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-bold text-blue-600\",\n                                    children: \"نداء الوطن\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"صوتك في الطوارئ\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[3rem] w-px bg-primary-700 hidden xl:block\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8 max-w-3xl mx-auto text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        'منصة \"',\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-bold text-primary-400\",\n                                            children: \"نداء الوطن\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                            lineNumber: 28,\n                                            columnNumber: 21\n                                        }, this),\n                                        '\" هي منصة تواصل للطوارئ تتيح للمستخدمين في فلسطين إرسال واستقبال الإشعارات الطوارئ، سواء كانت تتعلق بمساعدة طبية أو أمن طلب مساعدة طبية أو الإبلاغ عن مخاطر تهددهم'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                    lineNumber: 27,\n                                    columnNumber: 13\n                                }, this),\n                                \"sp\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-[3rem] w-px bg-primary-700 hidden xl:block\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid gap-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"وسائل التواصل الاجتماعي \"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-4\",\n                                    children: socialLinks.map((social, index)=>{\n                                        const Icon = social.icon;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: social.href,\n                                            className: \"w-8 h-8 flex items-center justify-center rounded-full bg-white text-blue-500 hover:bg-blue-50 transition-colors\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                                lineNumber: 48,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                            lineNumber: 43,\n                                            columnNumber: 19\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row justify-between items-center pt-6 border-t border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"flex flex-wrap justify-center gap-4\",\n                                children: _lib_data__WEBPACK_IMPORTED_MODULE_1__.links.map((link, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: link.link,\n                                            className: \"text-sm text-gray-600 hover:text-blue-500\",\n                                            children: link.text\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                            lineNumber: 62,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 mb-4 md:mb-0\",\n                            children: \"جميع حقوق النشر محفوظة لدى نداء الوطن \\xa9 2025\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Footer.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/global/Footer.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/global/Header.tsx":
/*!******************************************!*\
  !*** ./src/components/global/Header.tsx ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Header)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _app_home_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/app/(home)/layout */ \"(ssr)/./src/app/(home)/layout.tsx\");\n/* harmony import */ var _lib_data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/data */ \"(ssr)/./src/lib/data.ts\");\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nextui-org/button */ \"(ssr)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var _nextui_org_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @nextui-org/link */ \"(ssr)/./node_modules/@nextui-org/link/dist/chunk-FGDGYNYV.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/react-cookie/esm/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Header(props) {\n    const [_, __, removeCookies] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_4__.useCookies)();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(_app_home_layout__WEBPACK_IMPORTED_MODULE_1__.UserContext);\n    const user = context?.user;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"sticky m-8 px-6 py-4 flex justify-between backdrop-blur-sm rounded-xl z-[999]\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_link__WEBPACK_IMPORTED_MODULE_5__.link_default, {\n                href: \"/\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"font-extrabold\",\n                    children: \"نداء الوطن\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-3\",\n                children: _lib_data__WEBPACK_IMPORTED_MODULE_2__.links.map(({ link, text })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_link__WEBPACK_IMPORTED_MODULE_5__.link_default, {\n                        href: link,\n                        className: \"text-black hover:text-black/75\",\n                        children: text\n                    }, link, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            context?.isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                children: \"جارى التحميل\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this) : user && context.isValid ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-3\",\n                        children: [\n                            \"مرحبا \",\n                            user.first_name + \" \" + user.last_name\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_6__.button_default, {\n                        as: _nextui_org_link__WEBPACK_IMPORTED_MODULE_5__.link_default,\n                        href: \"/\",\n                        variant: \"bordered\",\n                        color: \"danger\",\n                        onPress: ()=>{\n                            removeCookies(\"access\");\n                            removeCookies(\"refresh\");\n                        },\n                        children: \"تسجيل الخروج\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                lineNumber: 33,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_6__.button_default, {\n                as: _nextui_org_link__WEBPACK_IMPORTED_MODULE_5__.link_default,\n                href: \"/auth\",\n                variant: \"bordered\",\n                color: \"primary\",\n                children: \"تسجيل الدخول\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n                lineNumber: 49,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\Header.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/global/Header.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/specific/AlertItemDetails/page.tsx":
/*!***********************************************************!*\
  !*** ./src/components/specific/AlertItemDetails/page.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertItemDetails: () => (/* binding */ AlertItemDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,Chip,Divider,Image,Spinner!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/spinner/dist/chunk-TDOFO53L.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,Chip,Divider,Image,Spinner!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/chip/dist/chunk-4WFLSIHH.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,Chip,Divider,Image,Spinner!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/card/dist/chunk-46NETW2U.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,Chip,Divider,Image,Spinner!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/card/dist/chunk-5ALFRFZW.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,Chip,Divider,Image,Spinner!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/divider/dist/chunk-44JHHBS2.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,Chip,Divider,Image,Spinner!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/image/dist/chunk-VKW4DPLJ.mjs\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Heart,MapPin,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Heart,MapPin,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Heart,MapPin,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/triangle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Heart,MapPin,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Heart,MapPin,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Clock,Heart,MapPin,Shield,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AlertItemDetails auto */ \n\n\n\n\n// Utility functions for formatting and styling\nconst getEmergencyTypeLabel = (type)=>{\n    switch(type){\n        case \"O\":\n            return \"طلب مساعدة\";\n        case \"M\":\n            return \"طبية\";\n        case \"D\":\n            return \"خطر\";\n        default:\n            return \"غير محدد\";\n    }\n};\nconst getEmergencyTypeColor = (type)=>{\n    switch(type){\n        case \"O\":\n            return \"primary\";\n        case \"M\":\n            return \"success\";\n        case \"D\":\n            return \"danger\";\n        default:\n            return \"default\";\n    }\n};\nconst getEmergencyTypeIcon = (type)=>{\n    switch(type){\n        case \"O\":\n            return _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        case \"M\":\n            return _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        case \"D\":\n            return _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        default:\n            return _barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n    }\n};\nconst formatDate = (dateString)=>{\n    const date = new Date(dateString);\n    return new Intl.DateTimeFormat('ar-EG', {\n        year: 'numeric',\n        month: 'long',\n        day: 'numeric',\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: true\n    }).format(date);\n};\nfunction AlertItemDetails({ id }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"AlertItemDetails.useEffect\": ()=>{\n            setIsLoading(true);\n            setError(null);\n            (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.fetcher)(`/emergency/${id}/`, null, \"GET\").then({\n                \"AlertItemDetails.useEffect\": (res)=>{\n                    if (!res.ok) {\n                        throw new Error(`HTTP error! status: ${res.status}`);\n                    }\n                    return res.json();\n                }\n            }[\"AlertItemDetails.useEffect\"]).then({\n                \"AlertItemDetails.useEffect\": (data)=>{\n                    setData(data);\n                }\n            }[\"AlertItemDetails.useEffect\"]).catch({\n                \"AlertItemDetails.useEffect\": (err)=>{\n                    console.error(\"Error fetching alert details:\", err);\n                    setError(\"حدث خطأ أثناء تحميل تفاصيل الإشعار\");\n                }\n            }[\"AlertItemDetails.useEffect\"]).finally({\n                \"AlertItemDetails.useEffect\": ()=>{\n                    setIsLoading(false);\n                }\n            }[\"AlertItemDetails.useEffect\"]);\n        }\n    }[\"AlertItemDetails.useEffect\"], [\n        id\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center py-12 space-y-4\",\n            dir: \"rtl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_6__.spinner_default, {\n                    size: \"lg\",\n                    color: \"primary\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600\",\n                    children: \"جارى تحميل تفاصيل الإشعار...\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center py-12 space-y-4\",\n            dir: \"rtl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-16 h-16 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-red-600 text-center\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n            lineNumber: 102,\n            columnNumber: 7\n        }, this);\n    }\n    if (!data) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center py-12 space-y-4\",\n            dir: \"rtl\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-16 h-16 text-gray-400\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 text-center\",\n                    children: \"لم يتم العثور على تفاصيل الإشعار\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    const EmergencyIcon = getEmergencyTypeIcon(data.emergency_type);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-4xl mx-auto p-6 space-y-6\",\n        dir: \"rtl\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmergencyIcon, {\n                                className: \"w-8 h-8 text-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold text-gray-800\",\n                                children: \"تفاصيل إشعار الطوارئ\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_7__.chip_default, {\n                        color: getEmergencyTypeColor(data.emergency_type),\n                        size: \"lg\",\n                        variant: \"flat\",\n                        className: \"text-lg px-4 py-2\",\n                        children: getEmergencyTypeLabel(data.emergency_type)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.card_default, {\n                        className: \"shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_9__.card_body_default, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"معلومات المبلغ\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"الاسم:\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: [\n                                                    data.user_first_name,\n                                                    \" \",\n                                                    data.user_last_name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.card_default, {\n                        className: \"shadow-md\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_9__.card_body_default, {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"معلومات الموقع والوقت\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"الموقع:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                    lineNumber: 166,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium text-left\",\n                                                    children: data.location\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                    lineNumber: 167,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_12__.divider_default, {}, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"وقت الإبلاغ:\"\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                    lineNumber: 171,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-left\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Clock_Heart_MapPin_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                            lineNumber: 173,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: formatDate(data.created_at)\n                                                        }, void 0, false, {\n                                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                            lineNumber: 174,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.card_default, {\n                className: \"shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_9__.card_body_default, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: \"وصف الحالة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-700 leading-relaxed text-justify bg-gray-50 p-4 rounded-lg\",\n                            children: data.description\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                            lineNumber: 186,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                lineNumber: 183,\n                columnNumber: 7\n            }, this),\n            data.images && data.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_8__.card_default, {\n                className: \"shadow-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_9__.card_body_default, {\n                    className: \"p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-lg font-semibold mb-4\",\n                            children: [\n                                \"الصور المرفقة (\",\n                                data.images.length,\n                                \")\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\",\n                            children: data.images.map(({ id, src }, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_Chip_Divider_Image_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_14__.image_default, {\n                                            src: src || \"/placeholder.svg?height=240&width=240\",\n                                            alt: `صورة ${index + 1}`,\n                                            width: 300,\n                                            height: 200,\n                                            className: \"object-cover rounded-lg shadow-sm hover:shadow-md transition-shadow cursor-pointer\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-white opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                children: \"انقر للتكبير\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, id, true, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n                lineNumber: 194,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertItemDetails\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/specific/AlertItemDetails/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/specific/AlertSection/index.tsx":
/*!********************************************************!*\
  !*** ./src/components/specific/AlertSection/index.tsx ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertSection: () => (/* binding */ AlertSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_animated_modal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/animated-modal */ \"(ssr)/./src/components/ui/animated-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_ScrollShadow_nextui_org_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,ScrollShadow!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/scroll-shadow/dist/chunk-NCVCYSZZ.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_ScrollShadow_nextui_org_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,ScrollShadow!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/card/dist/chunk-46NETW2U.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_ScrollShadow_nextui_org_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody,ScrollShadow!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/card/dist/chunk-5ALFRFZW.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,Info!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _AlertItemDetails_page__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../AlertItemDetails/page */ \"(ssr)/./src/components/specific/AlertItemDetails/page.tsx\");\n/* __next_internal_client_entry_do_not_use__ AlertSection auto */ \n\n\n\n\n\nfunction AlertSection({ data = [], heading }) {\n    // Ensure data is an array\n    const safeData = Array.isArray(data) ? data : [];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-[1200px] mx-auto px-4 py-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/previous\",\n                            className: \"text-blue-500 hover:text-blue-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-bold\",\n                            children: heading\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                lineNumber: 29,\n                columnNumber: 7\n            }, this),\n            safeData.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center w-full h-32\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500\",\n                    children: \"لا توجد تنبيهات حالياً\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                lineNumber: 39,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_ScrollShadow_nextui_org_react__WEBPACK_IMPORTED_MODULE_5__.scroll_shadow_default, {\n                orientation: \"horizontal\",\n                className: \"flex gap-4 w-full overflow-x-auto pb-4\",\n                children: safeData.map((alert)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_ScrollShadow_nextui_org_react__WEBPACK_IMPORTED_MODULE_6__.card_default, {\n                        className: \"flex-none w-[300px] border border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_ScrollShadow_nextui_org_react__WEBPACK_IMPORTED_MODULE_7__.card_body_default, {\n                            className: \"gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-sm text-start\",\n                                                    children: alert.user_first_name + \" \" + alert.user_last_name\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                                    lineNumber: 55,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: alert.location\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                            lineNumber: 54,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_Info_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"w-5 h-5 text-blue-500 mt-1 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-end gap-2 border-t-1 pt-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_modal__WEBPACK_IMPORTED_MODULE_1__.Modal, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_modal__WEBPACK_IMPORTED_MODULE_1__.ModalTrigger, {\n                                                className: \"bg-blue-600 text-sm text-white hover:opacity-75 transition\",\n                                                children: \"عرض التفاصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                                lineNumber: 64,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_modal__WEBPACK_IMPORTED_MODULE_1__.ModalBody, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_animated_modal__WEBPACK_IMPORTED_MODULE_1__.ModalContent, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AlertItemDetails_page__WEBPACK_IMPORTED_MODULE_3__.AlertItemDetails, {\n                                                        id: alert.id\n                                                    }, void 0, false, {\n                                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                                        lineNumber: 69,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                                lineNumber: 67,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, `modal-${alert.id}`, true, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 15\n                        }, this)\n                    }, alert.id, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\AlertSection\\\\index.tsx\",\n        lineNumber: 28,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/specific/AlertSection/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/specific/CallUs/index.tsx":
/*!**************************************************!*\
  !*** ./src/components/specific/CallUs/index.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CallUs)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/card/dist/chunk-46NETW2U.mjs\");\n/* harmony import */ var _barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Card,CardBody!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/card/dist/chunk-5ALFRFZW.mjs\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Mail,MapPin,Phone!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction CallUs({}) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 bg-white\",\n        id: \"call-us\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold text-center mb-8\",\n                    children: \"اتصل بنا\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_1__.card_default, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__.card_body_default, {\n                                className: \"flex flex-col items-center text-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold\",\n                                        children: \"الهاتف\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 16,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-sm text-default-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                dir: \"ltr\",\n                                                children: \"+970 2384501 / +970 2384501\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                                lineNumber: 18,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                dir: \"ltr\",\n                                                children: \"+9702384501 / +970 2384501\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                                lineNumber: 19,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 17,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                lineNumber: 14,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_1__.card_default, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__.card_body_default, {\n                                className: \"flex flex-col items-center text-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold\",\n                                        children: \"البريد الكتروني\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 27,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-1 text-sm text-default-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"AssistanceFormat.Com.Eg\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                                lineNumber: 29,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"AssistanceFormat.Com.Eg\"\n                                            }, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                                lineNumber: 30,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_1__.card_default, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Card_CardBody_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__.card_body_default, {\n                                className: \"flex flex-col items-center text-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Mail_MapPin_Phone_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-6 h-6 text-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-bold\",\n                                        children: \"العنوان الرئيسي\"\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-default-500\",\n                                        children: [\n                                            \"القدس - شارع مدينة\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                                lineNumber: 41,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العربية - فلسطين\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n            lineNumber: 10,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/specific/CallUs/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/specific/Chat/index.tsx":
/*!************************************************!*\
  !*** ./src/components/specific/Chat/index.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Chat: () => (/* binding */ Chat)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var _schemas_messages__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/schemas/messages */ \"(ssr)/./src/schemas/messages.ts\");\n/* harmony import */ var _hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @hookform/resolvers/zod */ \"(ssr)/./node_modules/@hookform/resolvers/zod/dist/zod.mjs\");\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nextui-org/button */ \"(ssr)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var _nextui_org_input__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @nextui-org/input */ \"(ssr)/./node_modules/@nextui-org/input/dist/chunk-JZOL6GD7.mjs\");\n/* harmony import */ var _barrel_optimize_names_MessageCircleMore_Minimize2Icon_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircleMore,Minimize2Icon,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/minimize-2.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircleMore_Minimize2Icon_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircleMore,Minimize2Icon,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_MessageCircleMore_Minimize2Icon_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircleMore,Minimize2Icon,Send!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/message-circle-more.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/react-cookie/esm/index.mjs\");\n/* harmony import */ var react_hook_form__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-hook-form */ \"(ssr)/./node_modules/react-hook-form/dist/index.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ Chat auto */ \n\n\n\n\n\n\n\n\n\nconst mockData = [\n    {\n        id: 1,\n        userId: 1,\n        message: \"مرحباً\"\n    },\n    {\n        id: 2,\n        userId: 2,\n        message: \"أهلاً\"\n    },\n    {\n        id: 3,\n        userId: 1,\n        message: \"كيف حالك؟\"\n    },\n    {\n        id: 4,\n        userId: 2,\n        message: \"أنا بخير، شكراً! ماذا عنك؟\"\n    },\n    {\n        id: 5,\n        userId: 1,\n        message: \"أنا بخير أيضاً. أعمل على مشروع حالياً.\"\n    },\n    {\n        id: 6,\n        userId: 2,\n        message: \"هذا رائع! ما نوع المشروع؟\"\n    },\n    {\n        id: 7,\n        userId: 1,\n        message: \"إنه تطبيق ويب لإدارة المهام.\"\n    },\n    {\n        id: 8,\n        userId: 2,\n        message: \"يبدو مثيراً للاهتمام! هل تستخدم React في ذلك؟\"\n    },\n    {\n        id: 9,\n        userId: 1,\n        message: \"نعم، أستخدم React و Node.js. أحب العمل بهما.\"\n    },\n    {\n        id: 10,\n        userId: 2,\n        message: \"وأنا كذلك! أستخدمهما في معظم مشاريعي أيضاً.\"\n    },\n    {\n        id: 11,\n        userId: 1,\n        message: \"هل لديك أي نصائح لتحسين الأداء في React؟\"\n    },\n    {\n        id: 12,\n        userId: 2,\n        message: \"بالطبع! استخدم React.memo و React.useMemo للحسابات الثقيلة.\"\n    },\n    {\n        id: 13,\n        userId: 1,\n        message: \"شكراً! يبدو ذلك مفيداً. سأجربه.\"\n    },\n    {\n        id: 14,\n        userId: 2,\n        message: \"على الرحب والسعة. أخبرني إذا احتجت إلى أي مساعدة.\"\n    },\n    {\n        id: 15,\n        userId: 1,\n        message: \"سأفعل. شكراً لدعمك!\"\n    },\n    {\n        id: 16,\n        userId: 2,\n        message: \"لا مشكلة! ما التقنيات الأخرى التي تستخدمها؟\"\n    },\n    {\n        id: 17,\n        userId: 1,\n        message: \"أستخدم أيضاً PostgreSQL لإدارة قاعدة البيانات.\"\n    },\n    {\n        id: 18,\n        userId: 2,\n        message: \"اختيار رائع! PostgreSQL قوي جداً للبيانات العلاقية.\"\n    },\n    {\n        id: 19,\n        userId: 1,\n        message: \"بالتأكيد! تعلمت الكثير أثناء العمل على هذا المشروع.\"\n    },\n    {\n        id: 20,\n        userId: 2,\n        message: \"هذه أفضل ميزة في بناء المشاريع. تتعلم الكثير!\"\n    }\n];\nfunction Chat({}) {\n    const currentUserId = 1;\n    const [cookies] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_5__.useCookies)();\n    const bottomRef = (0,react__WEBPACK_IMPORTED_MODULE_4__.useRef)(null);\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(false);\n    const access = cookies[\"access\"];\n    const { control, handleSubmit, formState: { errors, isSubmitting }, reset } = (0,react_hook_form__WEBPACK_IMPORTED_MODULE_6__.useForm)({\n        resolver: (0,_hookform_resolvers_zod__WEBPACK_IMPORTED_MODULE_3__.zodResolver)(_schemas_messages__WEBPACK_IMPORTED_MODULE_2__.messageChat),\n        defaultValues: {\n            message: \"\"\n        }\n    });\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_4__.useState)(mockData);\n    const scrollToBottom = ()=>{\n        bottomRef.current?.scrollIntoView({\n            behavior: \"smooth\"\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Chat.useEffect\": ()=>{\n            if (access) {\n                const fetchMessages = {\n                    \"Chat.useEffect.fetchMessages\": async ()=>{\n                        try {\n                            const res = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.fetcher)(\"/chats/messages/\", null, \"GET\", access);\n                            const data = await res.json();\n                            // Uncomment when API is ready\n                            // setMessages(data[\"results\"]);\n                            scrollToBottom();\n                        } catch (error) {\n                            console.error(\"Failed to fetch messages:\", error);\n                        }\n                    }\n                }[\"Chat.useEffect.fetchMessages\"];\n                fetchMessages();\n            }\n        }\n    }[\"Chat.useEffect\"], [\n        access\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)({\n        \"Chat.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"Chat.useEffect\"], [\n        messages\n    ]);\n    const onSubmit = (data)=>{\n        setMessages((prev)=>[\n                ...prev,\n                {\n                    id: Date.now(),\n                    userId: currentUserId,\n                    message: data.message\n                }\n            ]);\n        reset();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: open ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-[500px] w-[400px] bg-slate-200 fixed bottom-0 right-0 rounded-xl overflow-clip flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                    className: \"flex justify-between items-center bg-blue-600 text-white py-3 px-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"font-semibold text-lg\",\n                            children: \"خدمة العملاء\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_7__.button_default, {\n                            className: \"bg-black/75 p-1 rounded-full min-w-8\",\n                            onPress: ()=>setOpen(false),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircleMore_Minimize2Icon_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                className: \"text-white size-4\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-[23.5rem] overflow-y-auto\",\n                    children: messages?.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid place-content-center h-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"لا يوجد لديك رسائل\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col gap-2 pt-2 justify-end\",\n                        children: [\n                            messages.map((msg)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex flex-col mb-2\", msg.userId === currentUserId ? \"items-start\" : \"items-end\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"max-w-[250px] px-4 py-2 rounded-xl text-white text-sm\", msg.userId === currentUserId ? \"bg-red-500 rounded-tl-none\" : \"bg-black/25 rounded-tr-none\"),\n                                        children: msg.message\n                                    }, void 0, false, {\n                                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 21\n                                    }, this)\n                                }, msg.id, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 19\n                                }, this)),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: bottomRef\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 17\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    className: \"flex gap-2 px-2 border-t-1 pt-3 pb-2\",\n                    onSubmit: handleSubmit(onSubmit),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hook_form__WEBPACK_IMPORTED_MODULE_6__.Controller, {\n                            name: \"message\",\n                            control: control,\n                            render: ({ field })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_input__WEBPACK_IMPORTED_MODULE_9__.input_default, {\n                                    ...field,\n                                    placeholder: \"أدخل رسالتك\",\n                                    isInvalid: !!errors.message,\n                                    errorMessage: errors.message?.message\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, void 0)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_7__.button_default, {\n                            type: \"submit\",\n                            disabled: isSubmitting,\n                            className: \"min-w-8 bg-blue-600\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircleMore_Minimize2Icon_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"text-white\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                                lineNumber: 198,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n            lineNumber: 150,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_7__.button_default, {\n            onPress: ()=>setOpen(true),\n            className: \"fixed top-[92vh] right-2 w-16 h-16 px-0 bg-blue-600 text-white rounded-full text-xl font-bold grid place-content-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircleMore_Minimize2Icon_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"size-8\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n                lineNumber: 207,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\Chat\\\\index.tsx\",\n            lineNumber: 203,\n            columnNumber: 9\n        }, this)\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/specific/Chat/index.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/animated-modal.tsx":
/*!**********************************************!*\
  !*** ./src/components/ui/animated-modal.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Modal: () => (/* binding */ Modal),\n/* harmony export */   ModalBody: () => (/* binding */ ModalBody),\n/* harmony export */   ModalContent: () => (/* binding */ ModalContent),\n/* harmony export */   ModalFooter: () => (/* binding */ ModalFooter),\n/* harmony export */   ModalProvider: () => (/* binding */ ModalProvider),\n/* harmony export */   ModalTrigger: () => (/* binding */ ModalTrigger),\n/* harmony export */   useModal: () => (/* binding */ useModal),\n/* harmony export */   useOutsideClick: () => (/* binding */ useOutsideClick)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ ModalProvider,useModal,Modal,ModalTrigger,ModalBody,ModalContent,ModalFooter,useOutsideClick auto */ \n\n\n\nconst ModalContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\nconst ModalProvider = ({ children })=>{\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalContext.Provider, {\n        value: {\n            open,\n            setOpen\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 17,\n        columnNumber: 10\n    }, undefined);\n};\nconst useModal = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(ModalContext);\n    if (!context) {\n        throw new Error(\"useModal must be used within a ModalProvider\");\n    }\n    return context;\n};\nfunction Modal({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ModalProvider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 29,\n        columnNumber: 10\n    }, this);\n}\nconst ModalTrigger = ({ children, className })=>{\n    const { setOpen } = useModal();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"px-4 py-2 rounded-md text-black dark:text-white text-center relative overflow-hidden\", className),\n        onClick: ()=>setOpen(true),\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, undefined);\n};\nconst ModalBody = ({ children, className })=>{\n    const { open } = useModal();\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"ModalBody.useEffect\": ()=>{\n            if (open) {\n                document.body.style.overflow = \"hidden\";\n            } else {\n                document.body.style.overflow = \"auto\";\n            }\n        }\n    }[\"ModalBody.useEffect\"], [\n        open\n    ]);\n    const modalRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const { setOpen } = useModal();\n    useOutsideClick(modalRef, {\n        \"ModalBody.useOutsideClick\": ()=>setOpen(false)\n    }[\"ModalBody.useOutsideClick\"]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n        children: open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n            initial: {\n                opacity: 0\n            },\n            animate: {\n                opacity: 1,\n                backdropFilter: \"blur(10px)\"\n            },\n            exit: {\n                opacity: 0,\n                backdropFilter: \"blur(0px)\"\n            },\n            className: \"fixed [perspective:800px] [transform-style:preserve-3d] inset-0 h-full w-full  flex items-center justify-center z-[99999]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Overlay, {}, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n                    ref: modalRef,\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"min-h-[50%] max-h-[90%] w-[95%] md:max-w-[80%] lg:max-w-[70%] xl:max-w-[60%] bg-white dark:bg-neutral-950 border border-transparent dark:border-neutral-800 md:rounded-2xl relative z-50 flex flex-col overflow-hidden\", className),\n                    initial: {\n                        opacity: 0,\n                        scale: 0.5,\n                        rotateX: 40,\n                        y: 40\n                    },\n                    animate: {\n                        opacity: 1,\n                        scale: 1,\n                        rotateX: 0,\n                        y: 0\n                    },\n                    exit: {\n                        opacity: 0,\n                        scale: 0.8,\n                        rotateX: 10\n                    },\n                    transition: {\n                        type: \"spring\",\n                        stiffness: 260,\n                        damping: 15\n                    },\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CloseIcon, {}, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 13\n                        }, undefined),\n                        children\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n            lineNumber: 74,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, undefined);\n};\nconst ModalContent = ({ children, className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex flex-col flex-1 p-4 md:p-6 overflow-y-auto\", className),\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 135,\n        columnNumber: 10\n    }, undefined);\n};\nconst ModalFooter = ({ children, className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"flex justify-end p-4 bg-gray-100 dark:bg-neutral-900\", className),\n        children: children\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 145,\n        columnNumber: 10\n    }, undefined);\n};\nconst Overlay = ({ className })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.motion.div, {\n        initial: {\n            opacity: 0\n        },\n        animate: {\n            opacity: 1,\n            backdropFilter: \"blur(10px)\"\n        },\n        exit: {\n            opacity: 0,\n            backdropFilter: \"blur(0px)\"\n        },\n        className: `fixed inset-0 h-full w-full bg-black bg-opacity-50 z-50 ${className}`\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 150,\n        columnNumber: 5\n    }, undefined);\n};\nconst CloseIcon = ()=>{\n    const { setOpen } = useModal();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        onClick: ()=>setOpen(false),\n        className: \"absolute top-4 right-4 group\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n            xmlns: \"http://www.w3.org/2000/svg\",\n            width: \"24\",\n            height: \"24\",\n            viewBox: \"0 0 24 24\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            strokeWidth: \"2\",\n            strokeLinecap: \"round\",\n            strokeLinejoin: \"round\",\n            className: \"text-black dark:text-white h-4 w-4 group-hover:scale-125 group-hover:rotate-3 transition duration-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    stroke: \"none\",\n                    d: \"M0 0h24v24H0z\",\n                    fill: \"none\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M18 6l-12 12\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n                    lineNumber: 184,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                    d: \"M6 6l12 12\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n                    lineNumber: 185,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n            lineNumber: 171,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\ui\\\\animated-modal.tsx\",\n        lineNumber: 170,\n        columnNumber: 5\n    }, undefined);\n};\n// Hook to detect clicks outside of a component.\n// Add it in a separate file, I've added here for simplicity\nconst useOutsideClick = (ref, callback)=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"useOutsideClick.useEffect\": ()=>{\n            const listener = {\n                \"useOutsideClick.useEffect.listener\": (event)=>{\n                    // DO NOTHING if the element being clicked is the target element or their children\n                    if (!ref.current || ref.current.contains(event.target)) {\n                        return;\n                    }\n                    callback(event);\n                }\n            }[\"useOutsideClick.useEffect.listener\"];\n            document.addEventListener(\"mousedown\", listener);\n            document.addEventListener(\"touchstart\", listener);\n            return ({\n                \"useOutsideClick.useEffect\": ()=>{\n                    document.removeEventListener(\"mousedown\", listener);\n                    document.removeEventListener(\"touchstart\", listener);\n                }\n            })[\"useOutsideClick.useEffect\"];\n        }\n    }[\"useOutsideClick.useEffect\"], [\n        ref,\n        callback\n    ]);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/animated-modal.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/data.ts":
/*!*************************!*\
  !*** ./src/lib/data.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   links: () => (/* binding */ links)\n/* harmony export */ });\nconst links = [\n    {\n        link: \"/\",\n        text: \"الصفحة الرئيسية\"\n    },\n    {\n        link: \"/#send-emergency\",\n        text: \"اشعار طوارئ\"\n    },\n    {\n        link: \"/#recieved-emergency\",\n        text: \"الاشعارات المستلمه\"\n    },\n    {\n        link: \"/previous\",\n        text: \"الإشعارات السابقة\"\n    },\n    {\n        link: \"/#call-us\",\n        text: \"اتصل بنا\"\n    }\n];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2RhdGEudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPLE1BQU1BLFFBQVE7SUFDbkI7UUFBRUMsTUFBTTtRQUFLQyxNQUFNO0lBQWtCO0lBQ3JDO1FBQUVELE1BQU07UUFBb0JDLE1BQU07SUFBYztJQUNoRDtRQUFFRCxNQUFNO1FBQXdCQyxNQUFNO0lBQXFCO0lBQzNEO1FBQUVELE1BQU07UUFBYUMsTUFBTTtJQUFvQjtJQUMvQztRQUFFRCxNQUFNO1FBQWFDLE1BQU07SUFBVztDQUN2QyIsInNvdXJjZXMiOlsiRzpcXEdyYWR1YXRpb24gcHJvamVjdCAyMDI1XFxhcHAgKDIpXFxhcHBcXGZyb250ZW5kXFxzcmNcXGxpYlxcZGF0YS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgbGlua3MgPSBbXG4gIHsgbGluazogXCIvXCIsIHRleHQ6IFwi2KfZhNi12YHYrdipINin2YTYsdim2YrYs9mK2KlcIiB9LFxuICB7IGxpbms6IFwiLyNzZW5kLWVtZXJnZW5jeVwiLCB0ZXh0OiBcItin2LTYudin2LEg2LfZiNin2LHYplwiIH0sXG4gIHsgbGluazogXCIvI3JlY2lldmVkLWVtZXJnZW5jeVwiLCB0ZXh0OiBcItin2YTYp9i02LnYp9ix2KfYqiDYp9mE2YXYs9iq2YTZhdmHXCIgfSxcbiAgeyBsaW5rOiBcIi9wcmV2aW91c1wiLCB0ZXh0OiBcItin2YTYpdi02LnYp9ix2KfYqiDYp9mE2LPYp9io2YLYqVwiIH0sXG4gIHsgbGluazogXCIvI2NhbGwtdXNcIiwgdGV4dDogXCLYp9iq2LXZhCDYqNmG2KdcIiB9LFxuXVxuIl0sIm5hbWVzIjpbImxpbmtzIiwibGluayIsInRleHQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/data.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   fetcher: () => (/* binding */ fetcher)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nconst API_URL = \"http://localhost:8000\";\nasync function fetcher(endpoint, data, method = \"GET\", token) {\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    if (token) {\n        headers[\"Authorization\"] = `Bearer ${token}`;\n    }\n    const options = {\n        method,\n        headers,\n        next: {\n            revalidate: 60\n        }\n    };\n    if (data && method !== \"GET\") {\n        options.body = JSON.stringify(data);\n    }\n    try {\n        const url = `${API_URL}${endpoint}`;\n        console.log(`Fetching: ${url}`);\n        const response = await fetch(url, options);\n        // Log non-OK responses for debugging\n        if (!response.ok) {\n            console.warn(`API request failed: ${url} returned status ${response.status}`);\n        }\n        return response;\n    } catch (error) {\n        console.error(\"API request failed:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/schemas/messages.ts":
/*!*********************************!*\
  !*** ./src/schemas/messages.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   messageChat: () => (/* binding */ messageChat)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(ssr)/./node_modules/zod/lib/index.mjs\");\n\nconst messageChat = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    message: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, \"يرجى إدخال رسالة\")\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvc2NoZW1hcy9tZXNzYWdlcy50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUF3QjtBQUVqQixNQUFNQyxjQUFjRCx1Q0FBUSxDQUFDO0lBQ2xDRyxTQUFTSCx1Q0FBUSxHQUFHSyxHQUFHLENBQUMsR0FBRztBQUM3QixHQUFFIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcc2NoZW1hc1xcbWVzc2FnZXMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgeiBmcm9tIFwiem9kXCJcblxuZXhwb3J0IGNvbnN0IG1lc3NhZ2VDaGF0ID0gei5vYmplY3Qoe1xuICBtZXNzYWdlOiB6LnN0cmluZygpLm1pbigxLCBcItmK2LHYrNmJINil2K/Yrtin2YQg2LHYs9in2YTYqVwiKSxcbn0pXG5cbmV4cG9ydCB0eXBlIE1lc3NhZ2VDaGF0VHlwZSA9IHouaW5mZXI8dHlwZW9mIG1lc3NhZ2VDaGF0PlxuIl0sIm5hbWVzIjpbInoiLCJtZXNzYWdlQ2hhdCIsIm9iamVjdCIsIm1lc3NhZ2UiLCJzdHJpbmciLCJtaW4iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/schemas/messages.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@nextui-org","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@react-aria","vendor-chunks/tailwind-variants","vendor-chunks/sonner","vendor-chunks/react-cookie","vendor-chunks/universal-cookie","vendor-chunks/clsx","vendor-chunks/tailwind-merge","vendor-chunks/zod","vendor-chunks/react-hook-form","vendor-chunks/@react-stately","vendor-chunks/lucide-react","vendor-chunks/@hookform"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(home)%2Fpage&page=%2F(home)%2Fpage&appPaths=%2F(home)%2Fpage&pagePath=private-next-app-dir%2F(home)%2Fpage.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();