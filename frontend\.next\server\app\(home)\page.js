(()=>{var e={};e.id=813,e.ids=[813],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";async function a(e,t,r="GET",s){let l={"Content-Type":"application/json"};s&&(l.Authorization=`Bearer ${s}`);let o={method:r,headers:l,next:{revalidate:60}};t&&"GET"!==r&&(o.body=JSON.stringify(t));try{let t=`http://localhost:8000${e}`;console.log(`Fetching: ${t}`);let r=await fetch(t,o);return r.ok||console.warn(`API request failed: ${t} returned status ${r.status}`),r}catch(e){throw console.error("API request failed:",e),e}}r.d(t,{G:()=>a})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20187:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,pages:()=>d,routeModule:()=>h,tree:()=>c});var a=r(65239),s=r(48088),l=r(88170),o=r.n(l),n=r(30893),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let c={children:["",{children:["(home)",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,87163)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(home)/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},23650:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\specific\\\\CallUs\\\\index.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41566:(e,t,r)=>{"use strict";r.d(t,{Q:()=>a});var a=(0,r(72926).tv)({base:[],variants:{orientation:{vertical:["overflow-y-auto","data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],horizontal:["overflow-x-auto","data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}})},43048:(e,t,r)=>{"use strict";r.d(t,{AlertSection:()=>m});var a=r(60687),s=r(71356),l=r(79293),o=r(63257),n=r(86760),i=r(62688);let c=(0,i.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),d=(0,i.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var p=r(85814),h=r.n(p),u=r(52326);function m({data:e=[],heading:t}){let r=Array.isArray(e)?e:[];return(0,a.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,a.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(h(),{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,a.jsx)(c,{className:"w-5 h-5"})}),(0,a.jsx)("h2",{className:"text-xl font-bold",children:t})]})}),0===r.length?(0,a.jsx)("div",{className:"flex items-center justify-center w-full h-32",children:(0,a.jsx)("p",{className:"text-gray-500",children:"لا توجد تنبيهات حالياً"})}):(0,a.jsx)(l.H,{orientation:"horizontal",className:"flex gap-4 w-full overflow-x-auto pb-4",children:r.map(e=>(0,a.jsx)(o.Z,{className:"flex-none w-[300px] border border-gray-200",children:(0,a.jsxs)(n.U,{className:"gap-4",children:[(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-sm text-start",children:e.user_first_name+" "+e.user_last_name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.location})]}),(0,a.jsx)(d,{className:"w-5 h-5 text-blue-500 mt-1 flex-shrink-0"})]}),(0,a.jsx)("div",{className:"flex justify-end gap-2 border-t-1 pt-4",children:(0,a.jsxs)(s.aF,{children:[(0,a.jsx)(s.g6,{className:"bg-blue-600 text-sm text-white hover:opacity-75 transition",children:"عرض التفاصيل"}),(0,a.jsx)(s.cw,{children:(0,a.jsx)(s.$m,{children:(0,a.jsx)(u.A,{id:e.id})})})]},`modal-${e.id}`)})]})},e.id))})]})}},58694:(e,t,r)=>{Promise.resolve().then(r.bind(r,10529)),Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,43048)),Promise.resolve().then(r.bind(r,73271))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66942:(e,t,r)=>{Promise.resolve().then(r.bind(r,11075)),Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,72873)),Promise.resolve().then(r.bind(r,23650))},72873:(e,t,r)=>{"use strict";r.d(t,{AlertSection:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call AlertSection() from the server but AlertSection is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx","AlertSection")},73271:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var a=r(60687),s=r(63257),l=r(86760),o=r(62688);let n=(0,o.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),i=(0,o.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var c=r(97992);function d({}){return(0,a.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,a.jsxs)("div",{className:"container mx-auto px-4",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsx)(s.Z,{children:(0,a.jsxs)(l.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,a.jsx)(n,{className:"w-6 h-6 text-blue-500"}),(0,a.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,a.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,a.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,a.jsx)(s.Z,{children:(0,a.jsxs)(l.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,a.jsx)(i,{className:"w-6 h-6 text-blue-500"}),(0,a.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,a.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,a.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,a.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,a.jsx)(s.Z,{children:(0,a.jsxs)(l.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,a.jsx)(c.A,{className:"w-6 h-6 text-blue-500"}),(0,a.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,a.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,a.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}},79293:(e,t,r)=>{"use strict";r.d(t,{H:()=>d});var a=r(26109),s=r(41566),l=r(54514),o=r(82432),n=r(43210),i=r(60687),c=(0,a.Rf)((e,t)=>{let{Component:r,children:c,getBaseProps:d}=function(e){var t;let[r,i]=(0,a.rE)(e,s.Q.variantKeys),{ref:c,as:d,children:p,className:h,style:u,size:m=40,offset:x=0,visibility:f="auto",isEnabled:v=!0,onVisibilityChange:g,...b}=r,j=(0,l.zD)(c);!function(e={}){let{domRef:t,isEnabled:r=!0,overflowCheck:a="vertical",visibility:s="auto",offset:l=0,onVisibilityChange:i,updateDeps:c=[]}=e,d=(0,n.useRef)(s);(0,n.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!r)return;let n=(t,r,a,l,n)=>{if("auto"===s){let t=`${l}${(0,o.ZH)(n)}Scroll`;r&&a?(e.dataset[t]="true",e.removeAttribute(`data-${l}-scroll`),e.removeAttribute(`data-${n}-scroll`)):(e.dataset[`${l}Scroll`]=r.toString(),e.dataset[`${n}Scroll`]=a.toString(),e.removeAttribute(`data-${l}-${n}-scroll`))}else{let e=r&&a?"both":r?l:a?n:"none";e!==d.current&&(null==i||i(e),d.current=e)}},c=()=>{for(let{type:t,prefix:r,suffix:s}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(a===t||"both"===a){let a="vertical"===t?e.scrollTop>l:e.scrollLeft>l,o="vertical"===t?e.scrollTop+e.clientHeight+l<e.scrollHeight:e.scrollLeft+e.clientWidth+l<e.scrollWidth;n(t,a,o,r,s)}},p=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute(`data-${t}-scroll`)})};return c(),e.addEventListener("scroll",c),"auto"!==s&&(p(),"both"===s?(e.dataset.topBottomScroll=String("vertical"===a),e.dataset.leftRightScroll=String("horizontal"===a)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset[`${t}Scroll`]=String(s===t)}))),()=>{e.removeEventListener("scroll",c),p()}},[...c,r,s,a,i,t])}({domRef:j,offset:x,visibility:f,isEnabled:v,onVisibilityChange:g,updateDeps:[p],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let y=(0,n.useMemo)(()=>(0,s.Q)({...i,className:h}),[(0,o.t6)(i),h]);return{Component:d||"div",styles:y,domRef:j,children:p,getBaseProps:(t={})=>{var r;return{ref:j,className:y,"data-orientation":null!=(r=e.orientation)?r:"vertical",style:{"--scroll-shadow-size":`${m}px`,...u,...t.style},...b,...t}}}}({...e,ref:t});return(0,i.jsx)(r,{...d(),children:c})});c.displayName="NextUI.ScrollShadow";var d=c},79551:e=>{"use strict";e.exports=require("url")},87163:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var a=r(37413),s=r(72873),l=r(23650),o=r(10974),n=r(11075),i=r(61120);let c=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),d=(...e)=>e.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim();var p={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,i.forwardRef)(({color:e="currentColor",size:t=24,strokeWidth:r=2,absoluteStrokeWidth:a,className:s="",children:l,iconNode:o,...n},c)=>(0,i.createElement)("svg",{ref:c,...p,width:t,height:t,stroke:e,strokeWidth:a?24*Number(r)/Number(t):r,className:d("lucide",s),...n},[...o.map(([e,t])=>(0,i.createElement)(e,t)),...Array.isArray(l)?l:[l]])),u=(e,t)=>{let r=(0,i.forwardRef)(({className:r,...a},s)=>(0,i.createElement)(h,{ref:s,iconNode:t,className:d(`lucide-${c(e)}`,r),...a}));return r.displayName=`${e}`,r},m=u("Siren",[["path",{d:"M7 18v-6a5 5 0 1 1 10 0v6",key:"pcx96s"}],["path",{d:"M5 21a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1v-1a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2z",key:"1b4s83"}],["path",{d:"M21 12h1",key:"jtio3y"}],["path",{d:"M18.5 4.5 18 5",key:"g5sp9y"}],["path",{d:"M2 12h1",key:"1uaihz"}],["path",{d:"M12 2v1",key:"11qlp1"}],["path",{d:"m4.929 4.929.707.707",key:"1i51kw"}],["path",{d:"M12 12v6",key:"3ahymv"}]]),x=u("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var f=r(4536),v=r.n(f);async function g(e){let t=[{id:"1",user_first_name:"أحمد",user_last_name:"السيد",location:"القدس - عين علي، شارع مدرسة الثورية - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200"},{id:"2",user_first_name:"محمد",user_last_name:"إبراهيم",location:"رام الله - شارع الإرسال - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200"},{id:"3",user_first_name:"سارة",user_last_name:"خالد",location:"بيت لحم - شارع المهد - فلسطين",created_at:new Date().toISOString(),image:"/placeholder.svg?height=200&width=200"}],r={results:[]},i={results:[]},c={results:[]};try{let e=async e=>{try{let t=await (0,o.G)(e,null,"GET");if(!t.ok)throw Error(`API request failed with status ${t.status}`);let r=await t.text();try{return r?JSON.parse(r):{results:[]}}catch(e){return console.error("Invalid JSON response:",r.substring(0,100)),{results:[]}}}catch(t){return console.error(`Error fetching ${e}:`,t),{results:[]}}},[t,a,s]=await Promise.all([e("/emergency/?emergency_type=O"),e("/emergency/?emergency_type=M"),e("/emergency/?emergency_type=D")]);r=t,i=a,c=s}catch(e){console.error("Failed to fetch emergency requests:",e),r={results:t},i={results:t},c={results:t}}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"absolute w-full h-[75vh] top-0 pt-[35vh] bg-[url(/image_6.png)]",children:(0,a.jsxs)("div",{id:"send-emergency",className:"max-w-[30rem] drop-shadow-[#008524] bg-[#EEEEEE] mx-auto mb-24 py-8 px-6 rounded-xl flex flex-col items-center gap-3 relative bg-cover bg-center",children:[(0,a.jsx)(m,{className:"absolute top-3 left-3"}),(0,a.jsx)("h3",{children:"أرسل تنبيهًا للطوارئ"}),(0,a.jsx)("p",{children:"حدد إشعار الطوارئ ثم قم بملئ الحقول المطلوبة ومن ثم أرسل الطلب مباشرة وسيتم التوجة الى موقعكم في أسرع وقت ممكن."}),(0,a.jsx)(n.Button,{as:v(),href:"/add-application",endContent:(0,a.jsx)(x,{}),className:"mx-auto",color:"danger",children:"أرسل إشعار للطوارئ"})]})}),(0,a.jsxs)("section",{id:"recieved-emergency",className:"mt-[65vh]",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-center",children:"إشعارات الطوارئ المستلمة"}),(0,a.jsx)(s.AlertSection,{data:r.results,heading:"طلبات التنبيه حول الإغاثة"}),(0,a.jsx)(s.AlertSection,{data:i.results,heading:"طلبات التنبيه حول الصحة"}),(0,a.jsx)(s.AlertSection,{data:c.results,heading:"طلبات التنبيه حول الخطر"})]}),(0,a.jsx)(l.default,{})]})}}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,172,443,966,416,513,317,876,159],()=>r(20187));module.exports=a})();