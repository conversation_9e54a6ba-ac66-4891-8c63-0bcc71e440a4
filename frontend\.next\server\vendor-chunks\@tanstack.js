"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack";
exports.ids = ["vendor-chunks/@tanstack"];
exports.modules = {

/***/ "(ssr)/./node_modules/@tanstack/react-virtual/dist/esm/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/@tanstack/react-virtual/dist/esm/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Virtualizer: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.Virtualizer),\n/* harmony export */   approxEqual: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.approxEqual),\n/* harmony export */   debounce: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.debounce),\n/* harmony export */   defaultKeyExtractor: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.defaultKeyExtractor),\n/* harmony export */   defaultRangeExtractor: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.defaultRangeExtractor),\n/* harmony export */   elementScroll: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.elementScroll),\n/* harmony export */   measureElement: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.measureElement),\n/* harmony export */   memo: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.memo),\n/* harmony export */   notUndefined: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.notUndefined),\n/* harmony export */   observeElementOffset: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeElementOffset),\n/* harmony export */   observeElementRect: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeElementRect),\n/* harmony export */   observeWindowOffset: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeWindowOffset),\n/* harmony export */   observeWindowRect: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeWindowRect),\n/* harmony export */   useVirtualizer: () => (/* binding */ useVirtualizer),\n/* harmony export */   useWindowVirtualizer: () => (/* binding */ useWindowVirtualizer),\n/* harmony export */   windowScroll: () => (/* reexport safe */ _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.windowScroll)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/virtual-core */ \"(ssr)/./node_modules/@tanstack/virtual-core/dist/esm/index.js\");\n\n\n\n\nconst useIsomorphicLayoutEffect = typeof document !== \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\nfunction useVirtualizerBase(options) {\n  const rerender = react__WEBPACK_IMPORTED_MODULE_0__.useReducer(() => ({}), {})[1];\n  const resolvedOptions = {\n    ...options,\n    onChange: (instance2, sync) => {\n      var _a;\n      if (sync) {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(rerender);\n      } else {\n        rerender();\n      }\n      (_a = options.onChange) == null ? void 0 : _a.call(options, instance2, sync);\n    }\n  };\n  const [instance] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\n    () => new _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.Virtualizer(resolvedOptions)\n  );\n  instance.setOptions(resolvedOptions);\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount();\n  }, []);\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate();\n  });\n  return instance;\n}\nfunction useVirtualizer(options) {\n  return useVirtualizerBase({\n    observeElementRect: _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeElementRect,\n    observeElementOffset: _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeElementOffset,\n    scrollToFn: _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.elementScroll,\n    ...options\n  });\n}\nfunction useWindowVirtualizer(options) {\n  return useVirtualizerBase({\n    getScrollElement: () => typeof document !== \"undefined\" ? window : null,\n    observeElementRect: _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeWindowRect,\n    observeElementOffset: _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.observeWindowOffset,\n    scrollToFn: _tanstack_virtual_core__WEBPACK_IMPORTED_MODULE_2__.windowScroll,\n    initialOffset: () => typeof document !== \"undefined\" ? window.scrollY : 0,\n    ...options\n  });\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHRhbnN0YWNrL3JlYWN0LXZpcnR1YWwvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErQjtBQUNPO0FBQzhIO0FBQzdIO0FBQ3ZDLG9FQUFvRSxrREFBcUIsR0FBRyw0Q0FBZTtBQUMzRztBQUNBLG1CQUFtQiw2Q0FBZ0IsVUFBVSxLQUFLO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLG9EQUFTO0FBQ2pCLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDJDQUFjO0FBQ25DLGNBQWMsK0RBQVc7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQjtBQUN0Qix3QkFBd0I7QUFDeEIsZ0JBQWdCLGlFQUFhO0FBQzdCO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLHFFQUFpQjtBQUN6QywwQkFBMEIsdUVBQW1CO0FBQzdDLGdCQUFnQixnRUFBWTtBQUM1QjtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBSUU7QUFDRiIsInNvdXJjZXMiOlsiRzpcXEdyYWR1YXRpb24gcHJvamVjdCAyMDI1XFxhcHAgKDIpXFxhcHBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXEB0YW5zdGFja1xccmVhY3QtdmlydHVhbFxcZGlzdFxcZXNtXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IGZsdXNoU3luYyB9IGZyb20gXCJyZWFjdC1kb21cIjtcbmltcG9ydCB7IG9ic2VydmVFbGVtZW50UmVjdCwgb2JzZXJ2ZUVsZW1lbnRPZmZzZXQsIGVsZW1lbnRTY3JvbGwsIG9ic2VydmVXaW5kb3dSZWN0LCBvYnNlcnZlV2luZG93T2Zmc2V0LCB3aW5kb3dTY3JvbGwsIFZpcnR1YWxpemVyIH0gZnJvbSBcIkB0YW5zdGFjay92aXJ0dWFsLWNvcmVcIjtcbmV4cG9ydCAqIGZyb20gXCJAdGFuc3RhY2svdmlydHVhbC1jb3JlXCI7XG5jb25zdCB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0ID0gdHlwZW9mIGRvY3VtZW50ICE9PSBcInVuZGVmaW5lZFwiID8gUmVhY3QudXNlTGF5b3V0RWZmZWN0IDogUmVhY3QudXNlRWZmZWN0O1xuZnVuY3Rpb24gdXNlVmlydHVhbGl6ZXJCYXNlKG9wdGlvbnMpIHtcbiAgY29uc3QgcmVyZW5kZXIgPSBSZWFjdC51c2VSZWR1Y2VyKCgpID0+ICh7fSksIHt9KVsxXTtcbiAgY29uc3QgcmVzb2x2ZWRPcHRpb25zID0ge1xuICAgIC4uLm9wdGlvbnMsXG4gICAgb25DaGFuZ2U6IChpbnN0YW5jZTIsIHN5bmMpID0+IHtcbiAgICAgIHZhciBfYTtcbiAgICAgIGlmIChzeW5jKSB7XG4gICAgICAgIGZsdXNoU3luYyhyZXJlbmRlcik7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXJlbmRlcigpO1xuICAgICAgfVxuICAgICAgKF9hID0gb3B0aW9ucy5vbkNoYW5nZSkgPT0gbnVsbCA/IHZvaWQgMCA6IF9hLmNhbGwob3B0aW9ucywgaW5zdGFuY2UyLCBzeW5jKTtcbiAgICB9XG4gIH07XG4gIGNvbnN0IFtpbnN0YW5jZV0gPSBSZWFjdC51c2VTdGF0ZShcbiAgICAoKSA9PiBuZXcgVmlydHVhbGl6ZXIocmVzb2x2ZWRPcHRpb25zKVxuICApO1xuICBpbnN0YW5jZS5zZXRPcHRpb25zKHJlc29sdmVkT3B0aW9ucyk7XG4gIHVzZUlzb21vcnBoaWNMYXlvdXRFZmZlY3QoKCkgPT4ge1xuICAgIHJldHVybiBpbnN0YW5jZS5fZGlkTW91bnQoKTtcbiAgfSwgW10pO1xuICB1c2VJc29tb3JwaGljTGF5b3V0RWZmZWN0KCgpID0+IHtcbiAgICByZXR1cm4gaW5zdGFuY2UuX3dpbGxVcGRhdGUoKTtcbiAgfSk7XG4gIHJldHVybiBpbnN0YW5jZTtcbn1cbmZ1bmN0aW9uIHVzZVZpcnR1YWxpemVyKG9wdGlvbnMpIHtcbiAgcmV0dXJuIHVzZVZpcnR1YWxpemVyQmFzZSh7XG4gICAgb2JzZXJ2ZUVsZW1lbnRSZWN0LFxuICAgIG9ic2VydmVFbGVtZW50T2Zmc2V0LFxuICAgIHNjcm9sbFRvRm46IGVsZW1lbnRTY3JvbGwsXG4gICAgLi4ub3B0aW9uc1xuICB9KTtcbn1cbmZ1bmN0aW9uIHVzZVdpbmRvd1ZpcnR1YWxpemVyKG9wdGlvbnMpIHtcbiAgcmV0dXJuIHVzZVZpcnR1YWxpemVyQmFzZSh7XG4gICAgZ2V0U2Nyb2xsRWxlbWVudDogKCkgPT4gdHlwZW9mIGRvY3VtZW50ICE9PSBcInVuZGVmaW5lZFwiID8gd2luZG93IDogbnVsbCxcbiAgICBvYnNlcnZlRWxlbWVudFJlY3Q6IG9ic2VydmVXaW5kb3dSZWN0LFxuICAgIG9ic2VydmVFbGVtZW50T2Zmc2V0OiBvYnNlcnZlV2luZG93T2Zmc2V0LFxuICAgIHNjcm9sbFRvRm46IHdpbmRvd1Njcm9sbCxcbiAgICBpbml0aWFsT2Zmc2V0OiAoKSA9PiB0eXBlb2YgZG9jdW1lbnQgIT09IFwidW5kZWZpbmVkXCIgPyB3aW5kb3cuc2Nyb2xsWSA6IDAsXG4gICAgLi4ub3B0aW9uc1xuICB9KTtcbn1cbmV4cG9ydCB7XG4gIHVzZVZpcnR1YWxpemVyLFxuICB1c2VXaW5kb3dWaXJ0dWFsaXplclxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcFxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/react-virtual/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/virtual-core/dist/esm/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/virtual-core/dist/esm/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Virtualizer: () => (/* binding */ Virtualizer),\n/* harmony export */   approxEqual: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.approxEqual),\n/* harmony export */   debounce: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.debounce),\n/* harmony export */   defaultKeyExtractor: () => (/* binding */ defaultKeyExtractor),\n/* harmony export */   defaultRangeExtractor: () => (/* binding */ defaultRangeExtractor),\n/* harmony export */   elementScroll: () => (/* binding */ elementScroll),\n/* harmony export */   measureElement: () => (/* binding */ measureElement),\n/* harmony export */   memo: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.memo),\n/* harmony export */   notUndefined: () => (/* reexport safe */ _utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined),\n/* harmony export */   observeElementOffset: () => (/* binding */ observeElementOffset),\n/* harmony export */   observeElementRect: () => (/* binding */ observeElementRect),\n/* harmony export */   observeWindowOffset: () => (/* binding */ observeWindowOffset),\n/* harmony export */   observeWindowRect: () => (/* binding */ observeWindowRect),\n/* harmony export */   windowScroll: () => (/* binding */ windowScroll)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/@tanstack/virtual-core/dist/esm/utils.js\");\n\nconst defaultKeyExtractor = (index) => index;\nconst defaultRangeExtractor = (range) => {\n  const start = Math.max(range.startIndex - range.overscan, 0);\n  const end = Math.min(range.endIndex + range.overscan, range.count - 1);\n  const arr = [];\n  for (let i = start; i <= end; i++) {\n    arr.push(i);\n  }\n  return arr;\n};\nconst observeElementRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  const handler = (rect) => {\n    const { width, height } = rect;\n    cb({ width: Math.round(width), height: Math.round(height) });\n  };\n  handler(element.getBoundingClientRect());\n  if (!targetWindow.ResizeObserver) {\n    return () => {\n    };\n  }\n  const observer = new targetWindow.ResizeObserver((entries) => {\n    const entry = entries[0];\n    if (entry == null ? void 0 : entry.borderBoxSize) {\n      const box = entry.borderBoxSize[0];\n      if (box) {\n        handler({ width: box.inlineSize, height: box.blockSize });\n        return;\n      }\n    }\n    handler(element.getBoundingClientRect());\n  });\n  observer.observe(element, { box: \"border-box\" });\n  return () => {\n    observer.unobserve(element);\n  };\n};\nconst addEventListenerOptions = {\n  passive: true\n};\nconst observeWindowRect = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const handler = () => {\n    cb({ width: element.innerWidth, height: element.innerHeight });\n  };\n  handler();\n  element.addEventListener(\"resize\", handler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"resize\", handler);\n  };\n};\nconst supportsScrollend = typeof window == \"undefined\" ? true : \"onscrollend\" in window;\nconst observeElementOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.debounce)(\n    targetWindow,\n    () => {\n      cb(offset, false);\n    },\n    instance.options.isScrollingResetDelay\n  );\n  const createHandler = (isScrolling) => () => {\n    const { horizontal, isRtl } = instance.options;\n    offset = horizontal ? element[\"scrollLeft\"] * (isRtl && -1 || 1) : element[\"scrollTop\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    element.removeEventListener(\"scrollend\", endHandler);\n  };\n};\nconst observeWindowOffset = (instance, cb) => {\n  const element = instance.scrollElement;\n  if (!element) {\n    return;\n  }\n  const targetWindow = instance.targetWindow;\n  if (!targetWindow) {\n    return;\n  }\n  let offset = 0;\n  const fallback = instance.options.useScrollendEvent && supportsScrollend ? () => void 0 : (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.debounce)(\n    targetWindow,\n    () => {\n      cb(offset, false);\n    },\n    instance.options.isScrollingResetDelay\n  );\n  const createHandler = (isScrolling) => () => {\n    offset = element[instance.options.horizontal ? \"scrollX\" : \"scrollY\"];\n    fallback();\n    cb(offset, isScrolling);\n  };\n  const handler = createHandler(true);\n  const endHandler = createHandler(false);\n  endHandler();\n  element.addEventListener(\"scroll\", handler, addEventListenerOptions);\n  element.addEventListener(\"scrollend\", endHandler, addEventListenerOptions);\n  return () => {\n    element.removeEventListener(\"scroll\", handler);\n    element.removeEventListener(\"scrollend\", endHandler);\n  };\n};\nconst measureElement = (element, entry, instance) => {\n  if (entry == null ? void 0 : entry.borderBoxSize) {\n    const box = entry.borderBoxSize[0];\n    if (box) {\n      const size = Math.round(\n        box[instance.options.horizontal ? \"inlineSize\" : \"blockSize\"]\n      );\n      return size;\n    }\n  }\n  return Math.round(\n    element.getBoundingClientRect()[instance.options.horizontal ? \"width\" : \"height\"]\n  );\n};\nconst windowScroll = (offset, {\n  adjustments = 0,\n  behavior\n}, instance) => {\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nconst elementScroll = (offset, {\n  adjustments = 0,\n  behavior\n}, instance) => {\n  var _a, _b;\n  const toOffset = offset + adjustments;\n  (_b = (_a = instance.scrollElement) == null ? void 0 : _a.scrollTo) == null ? void 0 : _b.call(_a, {\n    [instance.options.horizontal ? \"left\" : \"top\"]: toOffset,\n    behavior\n  });\n};\nclass Virtualizer {\n  constructor(opts) {\n    this.unsubs = [];\n    this.scrollElement = null;\n    this.targetWindow = null;\n    this.isScrolling = false;\n    this.scrollToIndexTimeoutId = null;\n    this.measurementsCache = [];\n    this.itemSizeCache = /* @__PURE__ */ new Map();\n    this.pendingMeasuredCacheIndexes = [];\n    this.scrollRect = null;\n    this.scrollOffset = null;\n    this.scrollDirection = null;\n    this.scrollAdjustments = 0;\n    this.elementsCache = /* @__PURE__ */ new Map();\n    this.observer = /* @__PURE__ */ (() => {\n      let _ro = null;\n      const get = () => {\n        if (_ro) {\n          return _ro;\n        }\n        if (!this.targetWindow || !this.targetWindow.ResizeObserver) {\n          return null;\n        }\n        return _ro = new this.targetWindow.ResizeObserver((entries) => {\n          entries.forEach((entry) => {\n            this._measureElement(entry.target, entry);\n          });\n        });\n      };\n      return {\n        disconnect: () => {\n          var _a;\n          (_a = get()) == null ? void 0 : _a.disconnect();\n          _ro = null;\n        },\n        observe: (target) => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.observe(target, { box: \"border-box\" });\n        },\n        unobserve: (target) => {\n          var _a;\n          return (_a = get()) == null ? void 0 : _a.unobserve(target);\n        }\n      };\n    })();\n    this.range = null;\n    this.setOptions = (opts2) => {\n      Object.entries(opts2).forEach(([key, value]) => {\n        if (typeof value === \"undefined\") delete opts2[key];\n      });\n      this.options = {\n        debug: false,\n        initialOffset: 0,\n        overscan: 1,\n        paddingStart: 0,\n        paddingEnd: 0,\n        scrollPaddingStart: 0,\n        scrollPaddingEnd: 0,\n        horizontal: false,\n        getItemKey: defaultKeyExtractor,\n        rangeExtractor: defaultRangeExtractor,\n        onChange: () => {\n        },\n        measureElement,\n        initialRect: { width: 0, height: 0 },\n        scrollMargin: 0,\n        gap: 0,\n        indexAttribute: \"data-index\",\n        initialMeasurementsCache: [],\n        lanes: 1,\n        isScrollingResetDelay: 150,\n        enabled: true,\n        isRtl: false,\n        useScrollendEvent: true,\n        ...opts2\n      };\n    };\n    this.notify = (sync) => {\n      var _a, _b;\n      (_b = (_a = this.options).onChange) == null ? void 0 : _b.call(_a, this, sync);\n    };\n    this.maybeNotify = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => {\n        this.calculateRange();\n        return [\n          this.isScrolling,\n          this.range ? this.range.startIndex : null,\n          this.range ? this.range.endIndex : null\n        ];\n      },\n      (isScrolling) => {\n        this.notify(isScrolling);\n      },\n      {\n        key:  true && \"maybeNotify\",\n        debug: () => this.options.debug,\n        initialDeps: [\n          this.isScrolling,\n          this.range ? this.range.startIndex : null,\n          this.range ? this.range.endIndex : null\n        ]\n      }\n    );\n    this.cleanup = () => {\n      this.unsubs.filter(Boolean).forEach((d) => d());\n      this.unsubs = [];\n      this.observer.disconnect();\n      this.scrollElement = null;\n      this.targetWindow = null;\n    };\n    this._didMount = () => {\n      return () => {\n        this.cleanup();\n      };\n    };\n    this._willUpdate = () => {\n      var _a;\n      const scrollElement = this.options.enabled ? this.options.getScrollElement() : null;\n      if (this.scrollElement !== scrollElement) {\n        this.cleanup();\n        if (!scrollElement) {\n          this.maybeNotify();\n          return;\n        }\n        this.scrollElement = scrollElement;\n        if (this.scrollElement && \"ownerDocument\" in this.scrollElement) {\n          this.targetWindow = this.scrollElement.ownerDocument.defaultView;\n        } else {\n          this.targetWindow = ((_a = this.scrollElement) == null ? void 0 : _a.window) ?? null;\n        }\n        this.elementsCache.forEach((cached) => {\n          this.observer.observe(cached);\n        });\n        this._scrollToOffset(this.getScrollOffset(), {\n          adjustments: void 0,\n          behavior: void 0\n        });\n        this.unsubs.push(\n          this.options.observeElementRect(this, (rect) => {\n            this.scrollRect = rect;\n            this.maybeNotify();\n          })\n        );\n        this.unsubs.push(\n          this.options.observeElementOffset(this, (offset, isScrolling) => {\n            this.scrollAdjustments = 0;\n            this.scrollDirection = isScrolling ? this.getScrollOffset() < offset ? \"forward\" : \"backward\" : null;\n            this.scrollOffset = offset;\n            this.isScrolling = isScrolling;\n            this.maybeNotify();\n          })\n        );\n      }\n    };\n    this.getSize = () => {\n      if (!this.options.enabled) {\n        this.scrollRect = null;\n        return 0;\n      }\n      this.scrollRect = this.scrollRect ?? this.options.initialRect;\n      return this.scrollRect[this.options.horizontal ? \"width\" : \"height\"];\n    };\n    this.getScrollOffset = () => {\n      if (!this.options.enabled) {\n        this.scrollOffset = null;\n        return 0;\n      }\n      this.scrollOffset = this.scrollOffset ?? (typeof this.options.initialOffset === \"function\" ? this.options.initialOffset() : this.options.initialOffset);\n      return this.scrollOffset;\n    };\n    this.getFurthestMeasurement = (measurements, index) => {\n      const furthestMeasurementsFound = /* @__PURE__ */ new Map();\n      const furthestMeasurements = /* @__PURE__ */ new Map();\n      for (let m = index - 1; m >= 0; m--) {\n        const measurement = measurements[m];\n        if (furthestMeasurementsFound.has(measurement.lane)) {\n          continue;\n        }\n        const previousFurthestMeasurement = furthestMeasurements.get(\n          measurement.lane\n        );\n        if (previousFurthestMeasurement == null || measurement.end > previousFurthestMeasurement.end) {\n          furthestMeasurements.set(measurement.lane, measurement);\n        } else if (measurement.end < previousFurthestMeasurement.end) {\n          furthestMeasurementsFound.set(measurement.lane, true);\n        }\n        if (furthestMeasurementsFound.size === this.options.lanes) {\n          break;\n        }\n      }\n      return furthestMeasurements.size === this.options.lanes ? Array.from(furthestMeasurements.values()).sort((a, b) => {\n        if (a.end === b.end) {\n          return a.index - b.index;\n        }\n        return a.end - b.end;\n      })[0] : void 0;\n    };\n    this.getMeasurementOptions = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [\n        this.options.count,\n        this.options.paddingStart,\n        this.options.scrollMargin,\n        this.options.getItemKey,\n        this.options.enabled\n      ],\n      (count, paddingStart, scrollMargin, getItemKey, enabled) => {\n        this.pendingMeasuredCacheIndexes = [];\n        return {\n          count,\n          paddingStart,\n          scrollMargin,\n          getItemKey,\n          enabled\n        };\n      },\n      {\n        key: false\n      }\n    );\n    this.getMeasurements = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [this.getMeasurementOptions(), this.itemSizeCache],\n      ({ count, paddingStart, scrollMargin, getItemKey, enabled }, itemSizeCache) => {\n        if (!enabled) {\n          this.measurementsCache = [];\n          this.itemSizeCache.clear();\n          return [];\n        }\n        if (this.measurementsCache.length === 0) {\n          this.measurementsCache = this.options.initialMeasurementsCache;\n          this.measurementsCache.forEach((item) => {\n            this.itemSizeCache.set(item.key, item.size);\n          });\n        }\n        const min = this.pendingMeasuredCacheIndexes.length > 0 ? Math.min(...this.pendingMeasuredCacheIndexes) : 0;\n        this.pendingMeasuredCacheIndexes = [];\n        const measurements = this.measurementsCache.slice(0, min);\n        for (let i = min; i < count; i++) {\n          const key = getItemKey(i);\n          const furthestMeasurement = this.options.lanes === 1 ? measurements[i - 1] : this.getFurthestMeasurement(measurements, i);\n          const start = furthestMeasurement ? furthestMeasurement.end + this.options.gap : paddingStart + scrollMargin;\n          const measuredSize = itemSizeCache.get(key);\n          const size = typeof measuredSize === \"number\" ? measuredSize : this.options.estimateSize(i);\n          const end = start + size;\n          const lane = furthestMeasurement ? furthestMeasurement.lane : i % this.options.lanes;\n          measurements[i] = {\n            index: i,\n            start,\n            size,\n            end,\n            key,\n            lane\n          };\n        }\n        this.measurementsCache = measurements;\n        return measurements;\n      },\n      {\n        key:  true && \"getMeasurements\",\n        debug: () => this.options.debug\n      }\n    );\n    this.calculateRange = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [this.getMeasurements(), this.getSize(), this.getScrollOffset()],\n      (measurements, outerSize, scrollOffset) => {\n        return this.range = measurements.length > 0 && outerSize > 0 ? calculateRange({\n          measurements,\n          outerSize,\n          scrollOffset\n        }) : null;\n      },\n      {\n        key:  true && \"calculateRange\",\n        debug: () => this.options.debug\n      }\n    );\n    this.getIndexes = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [\n        this.options.rangeExtractor,\n        this.calculateRange(),\n        this.options.overscan,\n        this.options.count\n      ],\n      (rangeExtractor, range, overscan, count) => {\n        return range === null ? [] : rangeExtractor({\n          startIndex: range.startIndex,\n          endIndex: range.endIndex,\n          overscan,\n          count\n        });\n      },\n      {\n        key:  true && \"getIndexes\",\n        debug: () => this.options.debug\n      }\n    );\n    this.indexFromElement = (node) => {\n      const attributeName = this.options.indexAttribute;\n      const indexStr = node.getAttribute(attributeName);\n      if (!indexStr) {\n        console.warn(\n          `Missing attribute name '${attributeName}={index}' on measured element.`\n        );\n        return -1;\n      }\n      return parseInt(indexStr, 10);\n    };\n    this._measureElement = (node, entry) => {\n      const index = this.indexFromElement(node);\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const key = item.key;\n      const prevNode = this.elementsCache.get(key);\n      if (prevNode !== node) {\n        if (prevNode) {\n          this.observer.unobserve(prevNode);\n        }\n        this.observer.observe(node);\n        this.elementsCache.set(key, node);\n      }\n      if (node.isConnected) {\n        this.resizeItem(index, this.options.measureElement(node, entry, this));\n      }\n    };\n    this.resizeItem = (index, size) => {\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return;\n      }\n      const itemSize = this.itemSizeCache.get(item.key) ?? item.size;\n      const delta = size - itemSize;\n      if (delta !== 0) {\n        if (this.shouldAdjustScrollPositionOnItemSizeChange !== void 0 ? this.shouldAdjustScrollPositionOnItemSizeChange(item, delta, this) : item.start < this.getScrollOffset() + this.scrollAdjustments) {\n          if ( true && this.options.debug) {\n            console.info(\"correction\", delta);\n          }\n          this._scrollToOffset(this.getScrollOffset(), {\n            adjustments: this.scrollAdjustments += delta,\n            behavior: void 0\n          });\n        }\n        this.pendingMeasuredCacheIndexes.push(item.index);\n        this.itemSizeCache = new Map(this.itemSizeCache.set(item.key, size));\n        this.notify(false);\n      }\n    };\n    this.measureElement = (node) => {\n      if (!node) {\n        this.elementsCache.forEach((cached, key) => {\n          if (!cached.isConnected) {\n            this.observer.unobserve(cached);\n            this.elementsCache.delete(key);\n          }\n        });\n        return;\n      }\n      this._measureElement(node, void 0);\n    };\n    this.getVirtualItems = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.memo)(\n      () => [this.getIndexes(), this.getMeasurements()],\n      (indexes, measurements) => {\n        const virtualItems = [];\n        for (let k = 0, len = indexes.length; k < len; k++) {\n          const i = indexes[k];\n          const measurement = measurements[i];\n          virtualItems.push(measurement);\n        }\n        return virtualItems;\n      },\n      {\n        key:  true && \"getVirtualItems\",\n        debug: () => this.options.debug\n      }\n    );\n    this.getVirtualItemForOffset = (offset) => {\n      const measurements = this.getMeasurements();\n      if (measurements.length === 0) {\n        return void 0;\n      }\n      return (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined)(\n        measurements[findNearestBinarySearch(\n          0,\n          measurements.length - 1,\n          (index) => (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined)(measurements[index]).start,\n          offset\n        )]\n      );\n    };\n    this.getOffsetForAlignment = (toOffset, align) => {\n      const size = this.getSize();\n      const scrollOffset = this.getScrollOffset();\n      if (align === \"auto\") {\n        if (toOffset >= scrollOffset + size) {\n          align = \"end\";\n        }\n      }\n      if (align === \"end\") {\n        toOffset -= size;\n      }\n      const scrollSizeProp = this.options.horizontal ? \"scrollWidth\" : \"scrollHeight\";\n      const scrollSize = this.scrollElement ? \"document\" in this.scrollElement ? this.scrollElement.document.documentElement[scrollSizeProp] : this.scrollElement[scrollSizeProp] : 0;\n      const maxOffset = scrollSize - size;\n      return Math.max(Math.min(maxOffset, toOffset), 0);\n    };\n    this.getOffsetForIndex = (index, align = \"auto\") => {\n      index = Math.max(0, Math.min(index, this.options.count - 1));\n      const item = this.measurementsCache[index];\n      if (!item) {\n        return void 0;\n      }\n      const size = this.getSize();\n      const scrollOffset = this.getScrollOffset();\n      if (align === \"auto\") {\n        if (item.end >= scrollOffset + size - this.options.scrollPaddingEnd) {\n          align = \"end\";\n        } else if (item.start <= scrollOffset + this.options.scrollPaddingStart) {\n          align = \"start\";\n        } else {\n          return [scrollOffset, align];\n        }\n      }\n      const centerOffset = item.start - this.options.scrollPaddingStart + (item.size - size) / 2;\n      switch (align) {\n        case \"center\":\n          return [this.getOffsetForAlignment(centerOffset, align), align];\n        case \"end\":\n          return [\n            this.getOffsetForAlignment(\n              item.end + this.options.scrollPaddingEnd,\n              align\n            ),\n            align\n          ];\n        default:\n          return [\n            this.getOffsetForAlignment(\n              item.start - this.options.scrollPaddingStart,\n              align\n            ),\n            align\n          ];\n      }\n    };\n    this.isDynamicMode = () => this.elementsCache.size > 0;\n    this.cancelScrollToIndex = () => {\n      if (this.scrollToIndexTimeoutId !== null && this.targetWindow) {\n        this.targetWindow.clearTimeout(this.scrollToIndexTimeoutId);\n        this.scrollToIndexTimeoutId = null;\n      }\n    };\n    this.scrollToOffset = (toOffset, { align = \"start\", behavior } = {}) => {\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\n          \"The `smooth` scroll behavior is not fully supported with dynamic size.\"\n        );\n      }\n      this._scrollToOffset(this.getOffsetForAlignment(toOffset, align), {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.scrollToIndex = (index, { align: initialAlign = \"auto\", behavior } = {}) => {\n      index = Math.max(0, Math.min(index, this.options.count - 1));\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\n          \"The `smooth` scroll behavior is not fully supported with dynamic size.\"\n        );\n      }\n      const offsetAndAlign = this.getOffsetForIndex(index, initialAlign);\n      if (!offsetAndAlign) return;\n      const [offset, align] = offsetAndAlign;\n      this._scrollToOffset(offset, { adjustments: void 0, behavior });\n      if (behavior !== \"smooth\" && this.isDynamicMode() && this.targetWindow) {\n        this.scrollToIndexTimeoutId = this.targetWindow.setTimeout(() => {\n          this.scrollToIndexTimeoutId = null;\n          const elementInDOM = this.elementsCache.has(\n            this.options.getItemKey(index)\n          );\n          if (elementInDOM) {\n            const [latestOffset] = (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.notUndefined)(\n              this.getOffsetForIndex(index, align)\n            );\n            if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.approxEqual)(latestOffset, this.getScrollOffset())) {\n              this.scrollToIndex(index, { align, behavior });\n            }\n          } else {\n            this.scrollToIndex(index, { align, behavior });\n          }\n        });\n      }\n    };\n    this.scrollBy = (delta, { behavior } = {}) => {\n      this.cancelScrollToIndex();\n      if (behavior === \"smooth\" && this.isDynamicMode()) {\n        console.warn(\n          \"The `smooth` scroll behavior is not fully supported with dynamic size.\"\n        );\n      }\n      this._scrollToOffset(this.getScrollOffset() + delta, {\n        adjustments: void 0,\n        behavior\n      });\n    };\n    this.getTotalSize = () => {\n      var _a;\n      const measurements = this.getMeasurements();\n      let end;\n      if (measurements.length === 0) {\n        end = this.options.paddingStart;\n      } else {\n        end = this.options.lanes === 1 ? ((_a = measurements[measurements.length - 1]) == null ? void 0 : _a.end) ?? 0 : Math.max(\n          ...measurements.slice(-this.options.lanes).map((m) => m.end)\n        );\n      }\n      return Math.max(\n        end - this.options.scrollMargin + this.options.paddingEnd,\n        0\n      );\n    };\n    this._scrollToOffset = (offset, {\n      adjustments,\n      behavior\n    }) => {\n      this.options.scrollToFn(offset, { behavior, adjustments }, this);\n    };\n    this.measure = () => {\n      this.itemSizeCache = /* @__PURE__ */ new Map();\n      this.notify(false);\n    };\n    this.setOptions(opts);\n  }\n}\nconst findNearestBinarySearch = (low, high, getCurrentValue, value) => {\n  while (low <= high) {\n    const middle = (low + high) / 2 | 0;\n    const currentValue = getCurrentValue(middle);\n    if (currentValue < value) {\n      low = middle + 1;\n    } else if (currentValue > value) {\n      high = middle - 1;\n    } else {\n      return middle;\n    }\n  }\n  if (low > 0) {\n    return low - 1;\n  } else {\n    return 0;\n  }\n};\nfunction calculateRange({\n  measurements,\n  outerSize,\n  scrollOffset\n}) {\n  const count = measurements.length - 1;\n  const getOffset = (index) => measurements[index].start;\n  const startIndex = findNearestBinarySearch(0, count, getOffset, scrollOffset);\n  let endIndex = startIndex;\n  while (endIndex < count && measurements[endIndex].end < scrollOffset + outerSize) {\n    endIndex++;\n  }\n  return { startIndex, endIndex };\n}\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/virtual-core/dist/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@tanstack/virtual-core/dist/esm/utils.js":
/*!***************************************************************!*\
  !*** ./node_modules/@tanstack/virtual-core/dist/esm/utils.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   approxEqual: () => (/* binding */ approxEqual),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   memo: () => (/* binding */ memo),\n/* harmony export */   notUndefined: () => (/* binding */ notUndefined)\n/* harmony export */ });\nfunction memo(getDeps, fn, opts) {\n  let deps = opts.initialDeps ?? [];\n  let result;\n  return () => {\n    var _a, _b, _c, _d;\n    let depTime;\n    if (opts.key && ((_a = opts.debug) == null ? void 0 : _a.call(opts))) depTime = Date.now();\n    const newDeps = getDeps();\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && ((_b = opts.debug) == null ? void 0 : _b.call(opts))) resultTime = Date.now();\n    result = fn(...newDeps);\n    if (opts.key && ((_c = opts.debug) == null ? void 0 : _c.call(opts))) {\n      const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n      const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n      const resultFpsPercentage = resultEndTime / 16;\n      const pad = (str, num) => {\n        str = String(str);\n        while (str.length < num) {\n          str = \" \" + str;\n        }\n        return str;\n      };\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n          0,\n          Math.min(120 - 120 * resultFpsPercentage, 120)\n        )}deg 100% 31%);`,\n        opts == null ? void 0 : opts.key\n      );\n    }\n    (_d = opts == null ? void 0 : opts.onChange) == null ? void 0 : _d.call(opts, result);\n    return result;\n  };\n}\nfunction notUndefined(value, msg) {\n  if (value === void 0) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : \"\"}`);\n  } else {\n    return value;\n  }\n}\nconst approxEqual = (a, b) => Math.abs(a - b) < 1;\nconst debounce = (targetWindow, fn, ms) => {\n  let timeoutId;\n  return function(...args) {\n    targetWindow.clearTimeout(timeoutId);\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms);\n  };\n};\n\n//# sourceMappingURL=utils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@tanstack/virtual-core/dist/esm/utils.js\n");

/***/ })

};
;