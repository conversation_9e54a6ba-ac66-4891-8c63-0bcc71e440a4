"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/universal-cookie";
exports.ids = ["vendor-chunks/universal-cookie"];
exports.modules = {

/***/ "(ssr)/./node_modules/universal-cookie/esm/index.mjs":
/*!*****************************************************!*\
  !*** ./node_modules/universal-cookie/esm/index.mjs ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Cookies)\n/* harmony export */ });\nvar dist = {};\n\nvar hasRequiredDist;\n\nfunction requireDist () {\n\tif (hasRequiredDist) return dist;\n\thasRequiredDist = 1;\n\tObject.defineProperty(dist, \"__esModule\", { value: true });\n\tdist.parse = parse;\n\tdist.serialize = serialize;\n\t/**\n\t * RegExp to match cookie-name in RFC 6265 sec 4.1.1\n\t * This refers out to the obsoleted definition of token in RFC 2616 sec 2.2\n\t * which has been replaced by the token definition in RFC 7230 appendix B.\n\t *\n\t * cookie-name       = token\n\t * token             = 1*tchar\n\t * tchar             = \"!\" / \"#\" / \"$\" / \"%\" / \"&\" / \"'\" /\n\t *                     \"*\" / \"+\" / \"-\" / \".\" / \"^\" / \"_\" /\n\t *                     \"`\" / \"|\" / \"~\" / DIGIT / ALPHA\n\t *\n\t * Note: Allowing more characters - https://github.com/jshttp/cookie/issues/191\n\t * Allow same range as cookie value, except `=`, which delimits end of name.\n\t */\n\tconst cookieNameRegExp = /^[\\u0021-\\u003A\\u003C\\u003E-\\u007E]+$/;\n\t/**\n\t * RegExp to match cookie-value in RFC 6265 sec 4.1.1\n\t *\n\t * cookie-value      = *cookie-octet / ( DQUOTE *cookie-octet DQUOTE )\n\t * cookie-octet      = %x21 / %x23-2B / %x2D-3A / %x3C-5B / %x5D-7E\n\t *                     ; US-ASCII characters excluding CTLs,\n\t *                     ; whitespace DQUOTE, comma, semicolon,\n\t *                     ; and backslash\n\t *\n\t * Allowing more characters: https://github.com/jshttp/cookie/issues/191\n\t * Comma, backslash, and DQUOTE are not part of the parsing algorithm.\n\t */\n\tconst cookieValueRegExp = /^[\\u0021-\\u003A\\u003C-\\u007E]*$/;\n\t/**\n\t * RegExp to match domain-value in RFC 6265 sec 4.1.1\n\t *\n\t * domain-value      = <subdomain>\n\t *                     ; defined in [RFC1034], Section 3.5, as\n\t *                     ; enhanced by [RFC1123], Section 2.1\n\t * <subdomain>       = <label> | <subdomain> \".\" <label>\n\t * <label>           = <let-dig> [ [ <ldh-str> ] <let-dig> ]\n\t *                     Labels must be 63 characters or less.\n\t *                     'let-dig' not 'letter' in the first char, per RFC1123\n\t * <ldh-str>         = <let-dig-hyp> | <let-dig-hyp> <ldh-str>\n\t * <let-dig-hyp>     = <let-dig> | \"-\"\n\t * <let-dig>         = <letter> | <digit>\n\t * <letter>          = any one of the 52 alphabetic characters A through Z in\n\t *                     upper case and a through z in lower case\n\t * <digit>           = any one of the ten digits 0 through 9\n\t *\n\t * Keep support for leading dot: https://github.com/jshttp/cookie/issues/173\n\t *\n\t * > (Note that a leading %x2E (\".\"), if present, is ignored even though that\n\t * character is not permitted, but a trailing %x2E (\".\"), if present, will\n\t * cause the user agent to ignore the attribute.)\n\t */\n\tconst domainValueRegExp = /^([.]?[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)([.][a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?)*$/i;\n\t/**\n\t * RegExp to match path-value in RFC 6265 sec 4.1.1\n\t *\n\t * path-value        = <any CHAR except CTLs or \";\">\n\t * CHAR              = %x01-7F\n\t *                     ; defined in RFC 5234 appendix B.1\n\t */\n\tconst pathValueRegExp = /^[\\u0020-\\u003A\\u003D-\\u007E]*$/;\n\tconst __toString = Object.prototype.toString;\n\tconst NullObject = /* @__PURE__ */ (() => {\n\t    const C = function () { };\n\t    C.prototype = Object.create(null);\n\t    return C;\n\t})();\n\t/**\n\t * Parse a cookie header.\n\t *\n\t * Parse the given cookie header string into an object\n\t * The object has the various cookies as keys(names) => values\n\t */\n\tfunction parse(str, options) {\n\t    const obj = new NullObject();\n\t    const len = str.length;\n\t    // RFC 6265 sec 4.1.1, RFC 2616 2.2 defines a cookie name consists of one char minimum, plus '='.\n\t    if (len < 2)\n\t        return obj;\n\t    const dec = options?.decode || decode;\n\t    let index = 0;\n\t    do {\n\t        const eqIdx = str.indexOf(\"=\", index);\n\t        if (eqIdx === -1)\n\t            break; // No more cookie pairs.\n\t        const colonIdx = str.indexOf(\";\", index);\n\t        const endIdx = colonIdx === -1 ? len : colonIdx;\n\t        if (eqIdx > endIdx) {\n\t            // backtrack on prior semicolon\n\t            index = str.lastIndexOf(\";\", eqIdx - 1) + 1;\n\t            continue;\n\t        }\n\t        const keyStartIdx = startIndex(str, index, eqIdx);\n\t        const keyEndIdx = endIndex(str, eqIdx, keyStartIdx);\n\t        const key = str.slice(keyStartIdx, keyEndIdx);\n\t        // only assign once\n\t        if (obj[key] === undefined) {\n\t            let valStartIdx = startIndex(str, eqIdx + 1, endIdx);\n\t            let valEndIdx = endIndex(str, endIdx, valStartIdx);\n\t            const value = dec(str.slice(valStartIdx, valEndIdx));\n\t            obj[key] = value;\n\t        }\n\t        index = endIdx + 1;\n\t    } while (index < len);\n\t    return obj;\n\t}\n\tfunction startIndex(str, index, max) {\n\t    do {\n\t        const code = str.charCodeAt(index);\n\t        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n\t            return index;\n\t    } while (++index < max);\n\t    return max;\n\t}\n\tfunction endIndex(str, index, min) {\n\t    while (index > min) {\n\t        const code = str.charCodeAt(--index);\n\t        if (code !== 0x20 /*   */ && code !== 0x09 /* \\t */)\n\t            return index + 1;\n\t    }\n\t    return min;\n\t}\n\t/**\n\t * Serialize data into a cookie header.\n\t *\n\t * Serialize a name value pair into a cookie string suitable for\n\t * http headers. An optional options object specifies cookie parameters.\n\t *\n\t * serialize('foo', 'bar', { httpOnly: true })\n\t *   => \"foo=bar; httpOnly\"\n\t */\n\tfunction serialize(name, val, options) {\n\t    const enc = options?.encode || encodeURIComponent;\n\t    if (!cookieNameRegExp.test(name)) {\n\t        throw new TypeError(`argument name is invalid: ${name}`);\n\t    }\n\t    const value = enc(val);\n\t    if (!cookieValueRegExp.test(value)) {\n\t        throw new TypeError(`argument val is invalid: ${val}`);\n\t    }\n\t    let str = name + \"=\" + value;\n\t    if (!options)\n\t        return str;\n\t    if (options.maxAge !== undefined) {\n\t        if (!Number.isInteger(options.maxAge)) {\n\t            throw new TypeError(`option maxAge is invalid: ${options.maxAge}`);\n\t        }\n\t        str += \"; Max-Age=\" + options.maxAge;\n\t    }\n\t    if (options.domain) {\n\t        if (!domainValueRegExp.test(options.domain)) {\n\t            throw new TypeError(`option domain is invalid: ${options.domain}`);\n\t        }\n\t        str += \"; Domain=\" + options.domain;\n\t    }\n\t    if (options.path) {\n\t        if (!pathValueRegExp.test(options.path)) {\n\t            throw new TypeError(`option path is invalid: ${options.path}`);\n\t        }\n\t        str += \"; Path=\" + options.path;\n\t    }\n\t    if (options.expires) {\n\t        if (!isDate(options.expires) ||\n\t            !Number.isFinite(options.expires.valueOf())) {\n\t            throw new TypeError(`option expires is invalid: ${options.expires}`);\n\t        }\n\t        str += \"; Expires=\" + options.expires.toUTCString();\n\t    }\n\t    if (options.httpOnly) {\n\t        str += \"; HttpOnly\";\n\t    }\n\t    if (options.secure) {\n\t        str += \"; Secure\";\n\t    }\n\t    if (options.partitioned) {\n\t        str += \"; Partitioned\";\n\t    }\n\t    if (options.priority) {\n\t        const priority = typeof options.priority === \"string\"\n\t            ? options.priority.toLowerCase()\n\t            : undefined;\n\t        switch (priority) {\n\t            case \"low\":\n\t                str += \"; Priority=Low\";\n\t                break;\n\t            case \"medium\":\n\t                str += \"; Priority=Medium\";\n\t                break;\n\t            case \"high\":\n\t                str += \"; Priority=High\";\n\t                break;\n\t            default:\n\t                throw new TypeError(`option priority is invalid: ${options.priority}`);\n\t        }\n\t    }\n\t    if (options.sameSite) {\n\t        const sameSite = typeof options.sameSite === \"string\"\n\t            ? options.sameSite.toLowerCase()\n\t            : options.sameSite;\n\t        switch (sameSite) {\n\t            case true:\n\t            case \"strict\":\n\t                str += \"; SameSite=Strict\";\n\t                break;\n\t            case \"lax\":\n\t                str += \"; SameSite=Lax\";\n\t                break;\n\t            case \"none\":\n\t                str += \"; SameSite=None\";\n\t                break;\n\t            default:\n\t                throw new TypeError(`option sameSite is invalid: ${options.sameSite}`);\n\t        }\n\t    }\n\t    return str;\n\t}\n\t/**\n\t * URL-decode string value. Optimized to skip native call when no %.\n\t */\n\tfunction decode(str) {\n\t    if (str.indexOf(\"%\") === -1)\n\t        return str;\n\t    try {\n\t        return decodeURIComponent(str);\n\t    }\n\t    catch (e) {\n\t        return str;\n\t    }\n\t}\n\t/**\n\t * Determine if value is a Date.\n\t */\n\tfunction isDate(val) {\n\t    return __toString.call(val) === \"[object Date]\";\n\t}\n\t\n\treturn dist;\n}\n\nvar distExports = requireDist();\n\nfunction hasDocumentCookie() {\n    const testingValue = typeof global === 'undefined'\n        ? undefined\n        : global.TEST_HAS_DOCUMENT_COOKIE;\n    if (typeof testingValue === 'boolean') {\n        return testingValue;\n    }\n    // Can we get/set cookies on document.cookie?\n    return typeof document === 'object' && typeof document.cookie === 'string';\n}\nfunction parseCookies(cookies) {\n    if (typeof cookies === 'string') {\n        return distExports.parse(cookies);\n    }\n    else if (typeof cookies === 'object' && cookies !== null) {\n        return cookies;\n    }\n    else {\n        return {};\n    }\n}\nfunction readCookie(value, options = {}) {\n    const cleanValue = cleanupCookieValue(value);\n    if (!options.doNotParse) {\n        try {\n            return JSON.parse(cleanValue);\n        }\n        catch (e) {\n            // At least we tried\n        }\n    }\n    // Ignore clean value if we failed the deserialization\n    // It is not relevant anymore to trim those values\n    return value;\n}\nfunction cleanupCookieValue(value) {\n    // express prepend j: before serializing a cookie\n    if (value && value[0] === 'j' && value[1] === ':') {\n        return value.substr(2);\n    }\n    return value;\n}\n\nclass Cookies {\n    constructor(cookies, defaultSetOptions = {}) {\n        this.changeListeners = [];\n        this.HAS_DOCUMENT_COOKIE = false;\n        this.update = () => {\n            if (!this.HAS_DOCUMENT_COOKIE) {\n                return;\n            }\n            const previousCookies = this.cookies;\n            this.cookies = distExports.parse(document.cookie);\n            this._checkChanges(previousCookies);\n        };\n        const domCookies = typeof document === 'undefined' ? '' : document.cookie;\n        this.cookies = parseCookies(cookies || domCookies);\n        this.defaultSetOptions = defaultSetOptions;\n        this.HAS_DOCUMENT_COOKIE = hasDocumentCookie();\n    }\n    _emitChange(params) {\n        for (let i = 0; i < this.changeListeners.length; ++i) {\n            this.changeListeners[i](params);\n        }\n    }\n    _checkChanges(previousCookies) {\n        const names = new Set(Object.keys(previousCookies).concat(Object.keys(this.cookies)));\n        names.forEach((name) => {\n            if (previousCookies[name] !== this.cookies[name]) {\n                this._emitChange({\n                    name,\n                    value: readCookie(this.cookies[name]),\n                });\n            }\n        });\n    }\n    _startPolling() {\n        this.pollingInterval = setInterval(this.update, 300);\n    }\n    _stopPolling() {\n        if (this.pollingInterval) {\n            clearInterval(this.pollingInterval);\n        }\n    }\n    get(name, options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        return readCookie(this.cookies[name], options);\n    }\n    getAll(options = {}) {\n        if (!options.doNotUpdate) {\n            this.update();\n        }\n        const result = {};\n        for (let name in this.cookies) {\n            result[name] = readCookie(this.cookies[name], options);\n        }\n        return result;\n    }\n    set(name, value, options) {\n        if (options) {\n            options = Object.assign(Object.assign({}, this.defaultSetOptions), options);\n        }\n        else {\n            options = this.defaultSetOptions;\n        }\n        const stringValue = typeof value === 'string' ? value : JSON.stringify(value);\n        this.cookies = Object.assign(Object.assign({}, this.cookies), { [name]: stringValue });\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = distExports.serialize(name, stringValue, options);\n        }\n        this._emitChange({ name, value, options });\n    }\n    remove(name, options) {\n        const finalOptions = (options = Object.assign(Object.assign(Object.assign({}, this.defaultSetOptions), options), { expires: new Date(1970, 1, 1, 0, 0, 1), maxAge: 0 }));\n        this.cookies = Object.assign({}, this.cookies);\n        delete this.cookies[name];\n        if (this.HAS_DOCUMENT_COOKIE) {\n            document.cookie = distExports.serialize(name, '', finalOptions);\n        }\n        this._emitChange({ name, value: undefined, options });\n    }\n    addChangeListener(callback) {\n        this.changeListeners.push(callback);\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 1) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.addEventListener('change', this.update);\n            }\n            else {\n                this._startPolling();\n            }\n        }\n    }\n    removeChangeListener(callback) {\n        const idx = this.changeListeners.indexOf(callback);\n        if (idx >= 0) {\n            this.changeListeners.splice(idx, 1);\n        }\n        if (this.HAS_DOCUMENT_COOKIE && this.changeListeners.length === 0) {\n            if (typeof window === 'object' && 'cookieStore' in window) {\n                window.cookieStore.removeEventListener('change', this.update);\n            }\n            else {\n                this._stopPolling();\n            }\n        }\n    }\n    removeAllChangeListeners() {\n        while (this.changeListeners.length > 0) {\n            this.removeChangeListener(this.changeListeners[0]);\n        }\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/universal-cookie/esm/index.mjs\n");

/***/ })

};
;