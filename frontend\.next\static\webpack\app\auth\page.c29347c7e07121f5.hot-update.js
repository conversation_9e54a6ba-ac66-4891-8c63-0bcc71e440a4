"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/page",{

/***/ "(app-pages-browser)/./src/app/auth/page.tsx":
/*!*******************************!*\
  !*** ./src/app/auth/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_authentication_LoginForm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/authentication/LoginForm */ \"(app-pages-browser)/./src/components/authentication/LoginForm.tsx\");\n/* harmony import */ var _components_authentication_SignUpForm__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/authentication/SignUpForm */ \"(app-pages-browser)/./src/components/authentication/SignUpForm.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @nextui-org/button */ \"(app-pages-browser)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var _nextui_org_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @nextui-org/tabs */ \"(app-pages-browser)/./node_modules/@nextui-org/tabs/dist/chunk-TBXVIZ2K.mjs\");\n/* harmony import */ var _nextui_org_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @nextui-org/tabs */ \"(app-pages-browser)/./node_modules/@nextui-org/tabs/dist/chunk-FXLYF44B.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction page(param) {\n    let {} = param;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter)();\n    const signInGoogle = async ()=>{\n        console.log(\"Google sign-in initiated\");\n        try {\n            const res = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.fetcher)(\"/auth/google/url/\", null, \"GET\");\n            if (res.ok) {\n                const data = await res.json();\n                const url = data.url;\n                window.location.href = url // Use window.location.href for external redirect\n                ;\n            } else {\n                console.error(\"Failed to get Google auth URL\");\n            }\n        } catch (error) {\n            console.error(\"Error initiating Google sign-in:\", error);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-md space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold\",\n                            children: \"مرحباً بعودتك!\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-500 mt-2\",\n                            children: \"مرحباً بعودتك، من فضلك ادخل بياناتك.\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 37,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_tabs__WEBPACK_IMPORTED_MODULE_6__.tabs_default, {\n                    \"aria-label\": \"Auth options\",\n                    color: \"primary\",\n                    variant: \"underlined\",\n                    className: \"w-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_tabs__WEBPACK_IMPORTED_MODULE_7__.tab_item_base_default, {\n                            title: \"تسجيل الدخول\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_authentication_LoginForm__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {}, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 13\n                            }, this)\n                        }, \"login\", false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_tabs__WEBPACK_IMPORTED_MODULE_7__.tab_item_base_default, {\n                            title: \"انشاء حساب \",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_authentication_SignUpForm__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 44,\n                                columnNumber: 13\n                            }, this)\n                        }, \"signup\", false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 43,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 flex items-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"w-full border-t\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative flex justify-center text-xs uppercase\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"bg-white px-2 text-gray-500\",\n                                children: \"او اتصل باستخدام\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_8__.button_default, {\n                        variant: \"ghost\",\n                        onPress: signInGoogle,\n                        className: \"border-1 border-gray-200 flex items-center gap-2 px-6 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                src: \"/google-icon.png\",\n                                alt: \"Google\",\n                                width: 20,\n                                height: 20\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 61,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"تسجيل الدخول بواسطة Google\"\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\auth\\\\page.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this)\n    }, void 0, false);\n}\n_s(page, \"fN7XvhJ+p5oE6+Xlo0NJmXpxjC8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_5__.useRouter\n    ];\n});\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/auth/page.tsx\n"));

/***/ })

});