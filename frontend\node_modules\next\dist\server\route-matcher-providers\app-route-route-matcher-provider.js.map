{"version": 3, "sources": ["../../../src/server/route-matcher-providers/app-route-route-matcher-provider.ts"], "sourcesContent": ["import { isAppRouteRoute } from '../../lib/is-app-route-route'\nimport { APP_PATHS_MANIFEST } from '../../shared/lib/constants'\nimport { RouteKind } from '../route-kind'\nimport { AppRouteRouteMatcher } from '../route-matchers/app-route-route-matcher'\nimport type {\n  Manifest,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\nimport { AppNormalizers } from '../normalizers/built/app'\n\nexport class AppRouteRouteMatcherProvider extends ManifestRouteMatcherProvider<AppRouteRouteMatcher> {\n  private readonly normalizers: AppNormalizers\n\n  constructor(distDir: string, manifestLoader: ManifestLoader) {\n    super(APP_PATHS_MANIFEST, manifestLoader)\n\n    this.normalizers = new AppNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<AppRouteRouteMatcher>> {\n    // This matcher only matches app routes.\n    const pages = Object.keys(manifest).filter((page) => isAppRouteRoute(page))\n\n    // Format the routes.\n    const matchers: Array<AppRouteRouteMatcher> = []\n    for (const page of pages) {\n      const filename = this.normalizers.filename.normalize(manifest[page])\n      const pathname = this.normalizers.pathname.normalize(page)\n      const bundlePath = this.normalizers.bundlePath.normalize(page)\n\n      matchers.push(\n        new AppRouteRouteMatcher({\n          kind: RouteKind.APP_ROUTE,\n          pathname,\n          page,\n          bundlePath,\n          filename,\n        })\n      )\n    }\n\n    return matchers\n  }\n}\n"], "names": ["AppRouteRouteMatcherProvider", "ManifestRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "APP_PATHS_MANIFEST", "normalizers", "AppNormalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "isAppRouteRoute", "matchers", "filename", "normalize", "pathname", "bundlePath", "push", "AppRouteRouteMatcher", "kind", "RouteKind", "APP_ROUTE"], "mappings": ";;;;+BAWaA;;;eAAAA;;;iCAXmB;2BACG;2BACT;sCACW;8CAKQ;qBACd;AAExB,MAAMA,qCAAqCC,0DAA4B;IAG5EC,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACC,6BAAkB,EAAED;QAE1B,IAAI,CAACE,WAAW,GAAG,IAAIC,mBAAc,CAACJ;IACxC;IAEA,MAAgBK,UACdC,QAAkB,EAC4B;QAC9C,wCAAwC;QACxC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASC,IAAAA,gCAAe,EAACD;QAErE,qBAAqB;QACrB,MAAME,WAAwC,EAAE;QAChD,KAAK,MAAMF,QAAQJ,MAAO;YACxB,MAAMO,WAAW,IAAI,CAACX,WAAW,CAACW,QAAQ,CAACC,SAAS,CAACT,QAAQ,CAACK,KAAK;YACnE,MAAMK,WAAW,IAAI,CAACb,WAAW,CAACa,QAAQ,CAACD,SAAS,CAACJ;YACrD,MAAMM,aAAa,IAAI,CAACd,WAAW,CAACc,UAAU,CAACF,SAAS,CAACJ;YAEzDE,SAASK,IAAI,CACX,IAAIC,0CAAoB,CAAC;gBACvBC,MAAMC,oBAAS,CAACC,SAAS;gBACzBN;gBACAL;gBACAM;gBACAH;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}