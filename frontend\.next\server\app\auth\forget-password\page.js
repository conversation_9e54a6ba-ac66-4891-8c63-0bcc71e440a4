(()=>{var e={};e.id=91,e.ids=[91],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3520:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),n=r(36424),o=r(43210);function a({children:e}){let[t,r]=(0,o.useState)(!1);return t?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[50vh] p-6 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"حدث خطأ غير متوقع"}),(0,s.jsx)("p",{className:"mb-6 text-gray-600",children:"نعتذر عن هذا الخطأ. يرجى تحديث الصفحة أو العودة إلى الصفحة الرئيسية."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n.T,{color:"primary",onClick:()=>window.location.reload(),children:"تحديث الصفحة"}),(0,s.jsx)(n.T,{variant:"bordered",onClick:()=>window.location.href="/",children:"العودة للصفحة الرئيسية"})]})]}):(0,s.jsx)(s.Fragment,{children:e})}},4780:(e,t,r)=>{"use strict";r.d(t,{G:()=>a,cn:()=>o});var s=r(49384),n=r(82348);function o(...e){return(0,n.QP)((0,s.$)(e))}async function a(e,t,r="GET",s){let n={"Content-Type":"application/json"};s&&(n.Authorization=`Bearer ${s}`);let o={method:r,headers:n,next:{revalidate:60}};t&&"GET"!==r&&(o.body=JSON.stringify(t));try{let t=`http://localhost:8000${e}`;console.log(`Fetching: ${t}`);let r=await fetch(t,o);return r.ok||console.warn(`API request failed: ${t} returned status ${r.status}`),r}catch(e){throw console.error("API request failed:",e),e}}},8335:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(60687),n=r(27605),o=r(63442),a=r(37066),i=r(33942),l=r(36424),d=r(39297),c=r(4780),p=r(52581),u=r(16189);function m({}){(0,u.useRouter)();let{control:e,handleSubmit:t,formState:{errors:r,isSubmitting:m}}=(0,n.mN)({resolver:(0,o.u)(a.Ie),defaultValues:{email:""}});async function h(e){201===(await (0,c.G)("/users/request-reset-password/?redirect_url=http://localhost:3000/auth/reset-password",e,"POST")).status?p.o.success("تم إرسال رمز التحقق بنجاح"):p.o.error("فشل في إرسال رمز التحقق, يرجى مراجعة الحساب المستخدم")}return(0,s.jsxs)("form",{onSubmit:t(h),className:"space-y-6 mt-6",children:[(0,s.jsx)(n.xI,{name:"email",control:e,render:({field:e})=>(0,s.jsx)(i.r,{...e,type:"email",label:"البريد الإلكتروني",variant:"bordered",isInvalid:!!r.email,errorMessage:r.email?.message})}),(0,s.jsx)(l.T,{type:"submit",color:"primary",className:(0,d.cn)("w-full",m?"opacity-50":""),disabled:m,children:"إرسال رمز التحقق"})]})}},10093:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\authentication\\\\ForgetPasswordForm.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\authentication\\ForgetPasswordForm.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";async function s(e,t,r="GET",n){let o={"Content-Type":"application/json"};n&&(o.Authorization=`Bearer ${n}`);let a={method:r,headers:o,next:{revalidate:60}};t&&"GET"!==r&&(a.body=JSON.stringify(t));try{let t=`http://localhost:8000${e}`;console.log(`Fetching: ${t}`);let r=await fetch(t,a);return r.ok||console.warn(`API request failed: ${t} returned status ${r.status}`),r}catch(e){throw console.error("API request failed:",e),e}}r.d(t,{G:()=>s})},14329:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687),n=r(36424);function o({error:e,reset:t}){return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen p-6 text-center",children:[(0,s.jsx)("h2",{className:"text-2xl font-bold mb-4",children:"حدث خطأ غير متوقع"}),(0,s.jsx)("p",{className:"mb-6 text-gray-600",children:"نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية."}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n.T,{color:"primary",onClick:()=>t(),children:"إعادة المحاولة"}),(0,s.jsx)(n.T,{variant:"bordered",onClick:()=>window.location.href="/",children:"العودة للصفحة الرئيسية"})]})]})}r(43210)},14683:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(65239),n=r(48088),o=r(88170),a=r.n(o),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["auth",{children:["forget-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26671)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,96394)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\auth\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\auth\\forget-password\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/auth/forget-password/page",pathname:"/auth/forget-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25986:(e,t,r)=>{Promise.resolve().then(r.bind(r,8335))},26671:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(37413),n=r(10093);function o({}){return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"w-full max-w-md space-y-8",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold",children:"هل نسيت كلمة السر"}),(0,s.jsx)("p",{className:"text-gray-500 mt-2",children:"لا داعي للقلق، أدخل رقم هاتفك المحمول أدناه، وسنرسل لك رمز التحقق المكون من 6 أرقام لإعادة كلمة المرور الخاصة بك."})]}),(0,s.jsx)(n.default,{})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34147:(e,t,r)=>{Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,42873)),Promise.resolve().then(r.bind(r,3520))},37066:(e,t,r)=>{"use strict";r.d(t,{Ie:()=>a,Sd:()=>o,X5:()=>n,oW:()=>i});var s=r(45880);let n=s.Ik({email:s.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:s.Yj().min(1,{message:"كلمة المرور مطلوبة"})}),o=s.Ik({first_name:s.Yj().min(2,{message:"الاسم الأول يجب أن يكون على الأقل حرفين"}),last_name:s.Yj().min(2,{message:"اسم العائلة يجب أن يكون على الأقل حرفين"}),email:s.Yj().email({message:"البريد الإلكتروني غير صالح"}),password:s.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),password2:s.Yj()}).refine(e=>e.password===e.password2,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]}),a=s.Ik({email:s.Yj().email({message:"البريد الإلكتروني غير صالح"}).optional().or(s.eu(""))}),i=s.Ik({password:s.Yj().min(8,{message:"كلمة المرور يجب أن تكون على الأقل 8 أحرف"}),confirmPassword:s.Yj()}).refine(e=>e.password===e.confirmPassword,{message:"كلمات المرور غير متطابقة",path:["confirmPassword"]})},38882:(e,t,r)=>{Promise.resolve().then(r.bind(r,10529)),Promise.resolve().then(r.bind(r,69505)),Promise.resolve().then(r.t.bind(r,46533,23))},39297:(e,t,r)=>{"use strict";r.d(t,{cn:()=>a});var s=r(83451);let n=function(){for(var e,t,r=0,s="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){var r,s,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(s=e(t[r]))&&(n&&(n+=" "),n+=s);else for(r in t)t[r]&&(n&&(n+=" "),n+=r)}return n}(e))&&(s&&(s+=" "),s+=t);return s};var o=(0,r(82348).zu)({extend:s.w});function a(...e){return o(n(e))}},39302:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ErrorBoundary.tsx","default")},42304:(e,t,r)=>{Promise.resolve().then(r.bind(r,14329))},42873:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var s=r(60687),n=r(73705),o=r(20211);function a({children:e}){return(0,s.jsx)(o.vD,{children:(0,s.jsx)(n.b,{children:e})})}},54413:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),n=r(11075),o=r(4536),a=r.n(o);function i(){return(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-screen p-6 text-center",children:[(0,s.jsx)("h1",{className:"text-6xl font-bold mb-4",children:"404"}),(0,s.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:"الصفحة غير موجودة"}),(0,s.jsx)("p",{className:"mb-8 text-gray-600 max-w-md",children:"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها."}),(0,s.jsx)(n.Button,{as:a(),href:"/",color:"primary",children:"العودة للصفحة الرئيسية"})]})}},54431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx","default")},55456:(e,t,r)=>{Promise.resolve().then(r.bind(r,54431))},56481:(e,t,r)=>{Promise.resolve().then(r.bind(r,11075)),Promise.resolve().then(r.t.bind(r,4536,23))},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69633:(e,t,r)=>{Promise.resolve().then(r.bind(r,10529)),Promise.resolve().then(r.t.bind(r,85814,23))},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(31658);let n=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74475:(e,t,r)=>{Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,89987)),Promise.resolve().then(r.bind(r,39302))},79551:e=>{"use strict";e.exports=require("url")},82449:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},85818:(e,t,r)=>{Promise.resolve().then(r.bind(r,10093))},89987:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx","default")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>d});var s=r(37413),n=r(98856),o=r.n(n);r(61135);var a=r(89987),i=r(6931),l=r(39302);let d={title:"Palastine Emergency",description:"Comming for help when you need us"};function c({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",children:(0,s.jsxs)("body",{className:`${o().variable} antialiased`,children:[(0,s.jsx)(a.default,{children:(0,s.jsx)(l.default,{children:e})}),(0,s.jsx)(i.Toaster,{richColors:!0,position:"top-right"})]})})}},96394:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(37413),n=r(10974),o=r(11075),a=r(75479),i=r(44999),l=r(53384),d=r(39916);async function c({children:e}){let t=await (0,i.UL)(),r=t.get("access")?.value??"";return 200===(await (0,n.G)("/auth/jwt/verify/",{token:r},"POST")).status&&(0,d.redirect)("/"),(0,s.jsxs)("div",{className:"min-h-screen flex flex-col lg:flex-row",children:[(0,s.jsxs)("div",{className:"relative flex-1 flex items-center justify-center p-6 bg-white",children:[(0,s.jsx)(o.Button,{as:a.Link,href:"/",className:"absolute top-6 right-6 bg-none",variant:"bordered",children:"رجوع"}),e]}),(0,s.jsx)("div",{className:"relative lg:flex-1",children:(0,s.jsx)(l.default,{src:"/emergency.jpg",alt:"Firefighter background",fill:!0,className:"object-cover"})})]})}},98129:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},98714:(e,t,r)=>{Promise.resolve().then(r.bind(r,11075)),Promise.resolve().then(r.bind(r,75479)),Promise.resolve().then(r.t.bind(r,49603,23))}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,172,443,416,994,618],()=>r(14683));module.exports=s})();