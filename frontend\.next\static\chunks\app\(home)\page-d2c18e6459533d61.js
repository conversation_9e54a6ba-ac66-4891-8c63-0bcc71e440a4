(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[813],{5970:(e,t,a)=>{Promise.resolve().then(a.bind(a,34991)),Promise.resolve().then(a.t.bind(a,6874,23)),Promise.resolve().then(a.bind(a,24720)),Promise.resolve().then(a.bind(a,25460))},24720:(e,t,a)=>{"use strict";a.d(t,{AlertSection:()=>f});var l=a(95155),s=a(73906),r=a(41907),i=a(27290),c=a(54736),o=a(19946);let n=(0,o.A)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),d=(0,o.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var h=a(6874),x=a.n(h),m=a(4766);function f(e){let{data:t=[],heading:a}=e,o=Array.isArray(t)?t:[];return(0,l.jsxs)("div",{className:"w-full max-w-[1200px] mx-auto px-4 py-6",children:[(0,l.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(x(),{href:"/previous",className:"text-blue-500 hover:text-blue-600",children:(0,l.jsx)(n,{className:"w-5 h-5"})}),(0,l.jsx)("h2",{className:"text-xl font-bold",children:a})]})}),0===o.length?(0,l.jsx)("div",{className:"flex items-center justify-center w-full h-32",children:(0,l.jsx)("p",{className:"text-gray-500",children:"لا توجد تنبيهات حالياً"})}):(0,l.jsx)(r.H,{orientation:"horizontal",className:"flex gap-4 w-full overflow-x-auto pb-4",children:o.map(e=>(0,l.jsx)(i.Z,{className:"flex-none w-[300px] border border-gray-200",children:(0,l.jsxs)(c.U,{className:"gap-4",children:[(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)("h3",{className:"font-semibold text-sm text-start",children:e.user_first_name+" "+e.user_last_name}),(0,l.jsx)("p",{className:"text-xs text-gray-500",children:e.location})]}),(0,l.jsx)(d,{className:"w-5 h-5 text-blue-500 mt-1 flex-shrink-0"})]}),(0,l.jsx)("div",{className:"flex justify-end gap-2 border-t-1 pt-4",children:(0,l.jsxs)(s.aF,{children:[(0,l.jsx)(s.g6,{className:"bg-blue-600 text-sm text-white hover:opacity-75 transition",children:"عرض التفاصيل"}),(0,l.jsx)(s.cw,{children:(0,l.jsx)(s.$m,{children:(0,l.jsx)(m.A,{id:e.id})})})]},"modal-".concat(e.id))})]})},e.id))})]})}},25460:(e,t,a)=>{"use strict";a.d(t,{default:()=>d});var l=a(95155),s=a(27290),r=a(54736),i=a(19946);let c=(0,i.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),o=(0,i.A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);var n=a(4516);function d(e){let{}=e;return(0,l.jsx)("section",{className:"py-12 bg-white",id:"call-us",children:(0,l.jsxs)("div",{className:"container mx-auto px-4",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-center mb-8",children:"اتصل بنا"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,l.jsx)(s.Z,{children:(0,l.jsxs)(r.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,l.jsx)(c,{className:"w-6 h-6 text-blue-500"}),(0,l.jsx)("h3",{className:"font-bold",children:"الهاتف"}),(0,l.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,l.jsx)("p",{dir:"ltr",children:"+970 2384501 / +970 2384501"}),(0,l.jsx)("p",{dir:"ltr",children:"+9702384501 / +970 2384501"})]})]})}),(0,l.jsx)(s.Z,{children:(0,l.jsxs)(r.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,l.jsx)(o,{className:"w-6 h-6 text-blue-500"}),(0,l.jsx)("h3",{className:"font-bold",children:"البريد الكتروني"}),(0,l.jsxs)("div",{className:"space-y-1 text-sm text-default-500",children:[(0,l.jsx)("p",{children:"AssistanceFormat.Com.Eg"}),(0,l.jsx)("p",{children:"AssistanceFormat.Com.Eg"})]})]})}),(0,l.jsx)(s.Z,{children:(0,l.jsxs)(r.U,{className:"flex flex-col items-center text-center gap-4",children:[(0,l.jsx)(n.A,{className:"w-6 h-6 text-blue-500"}),(0,l.jsx)("h3",{className:"font-bold",children:"العنوان الرئيسي"}),(0,l.jsxs)("p",{className:"text-sm text-default-500",children:["القدس - شارع مدينة",(0,l.jsx)("br",{}),"العربية - فلسطين"]})]})})]})]})})}},34991:(e,t,a)=>{"use strict";a.d(t,{Button:()=>l.T});var l=a(66146)},41907:(e,t,a)=>{"use strict";a.d(t,{H:()=>d});var l=a(56973),s=a(47956),r=a(6548),i=a(81467),c=a(12115),o=a(95155),n=(0,l.Rf)((e,t)=>{let{Component:a,children:n,getBaseProps:d}=function(e){var t;let[a,o]=(0,l.rE)(e,s.Q.variantKeys),{ref:n,as:d,children:h,className:x,style:m,size:f=40,offset:u=0,visibility:p="auto",isEnabled:v=!0,onVisibilityChange:g,...j}=a,b=(0,r.zD)(n);!function(e={}){let{domRef:t,isEnabled:a=!0,overflowCheck:l="vertical",visibility:s="auto",offset:r=0,onVisibilityChange:o,updateDeps:n=[]}=e,d=(0,c.useRef)(s);(0,c.useEffect)(()=>{let e=null==t?void 0:t.current;if(!e||!a)return;let c=(t,a,l,r,c)=>{if("auto"===s){let t=`${r}${(0,i.ZH)(c)}Scroll`;a&&l?(e.dataset[t]="true",e.removeAttribute(`data-${r}-scroll`),e.removeAttribute(`data-${c}-scroll`)):(e.dataset[`${r}Scroll`]=a.toString(),e.dataset[`${c}Scroll`]=l.toString(),e.removeAttribute(`data-${r}-${c}-scroll`))}else{let e=a&&l?"both":a?r:l?c:"none";e!==d.current&&(null==o||o(e),d.current=e)}},n=()=>{for(let{type:t,prefix:a,suffix:s}of[{type:"vertical",prefix:"top",suffix:"bottom"},{type:"horizontal",prefix:"left",suffix:"right"}])if(l===t||"both"===l){let l="vertical"===t?e.scrollTop>r:e.scrollLeft>r,i="vertical"===t?e.scrollTop+e.clientHeight+r<e.scrollHeight:e.scrollLeft+e.clientWidth+r<e.scrollWidth;c(t,l,i,a,s)}},h=()=>{["top","bottom","top-bottom","left","right","left-right"].forEach(t=>{e.removeAttribute(`data-${t}-scroll`)})};return n(),e.addEventListener("scroll",n),"auto"!==s&&(h(),"both"===s?(e.dataset.topBottomScroll=String("vertical"===l),e.dataset.leftRightScroll=String("horizontal"===l)):(e.dataset.topBottomScroll="false",e.dataset.leftRightScroll="false",["top","bottom","left","right"].forEach(t=>{e.dataset[`${t}Scroll`]=String(s===t)}))),()=>{e.removeEventListener("scroll",n),h()}},[...n,a,s,l,o,t])}({domRef:b,offset:u,visibility:p,isEnabled:v,onVisibilityChange:g,updateDeps:[h],overflowCheck:null!=(t=e.orientation)?t:"vertical"});let N=(0,c.useMemo)(()=>(0,s.Q)({...o,className:x}),[(0,i.t6)(o),x]);return{Component:d||"div",styles:N,domRef:b,children:h,getBaseProps:function(){var t;let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return{ref:b,className:N,"data-orientation":null!=(t=e.orientation)?t:"vertical",style:{"--scroll-shadow-size":"".concat(f,"px"),...m,...a.style},...j,...a}}}}({...e,ref:t});return(0,o.jsx)(a,{...d(),children:n})});n.displayName="NextUI.ScrollShadow";var d=n},47956:(e,t,a)=>{"use strict";a.d(t,{Q:()=>l});var l=(0,a(69478).tv)({base:[],variants:{orientation:{vertical:["overflow-y-auto","data-[top-scroll=true]:[mask-image:linear-gradient(0deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[bottom-scroll=true]:[mask-image:linear-gradient(180deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[top-bottom-scroll=true]:[mask-image:linear-gradient(#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"],horizontal:["overflow-x-auto","data-[left-scroll=true]:[mask-image:linear-gradient(270deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[right-scroll=true]:[mask-image:linear-gradient(90deg,#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]","data-[left-right-scroll=true]:[mask-image:linear-gradient(to_right,#000,#000,transparent_0,#000_var(--scroll-shadow-size),#000_calc(100%_-_var(--scroll-shadow-size)),transparent)]"]},hideScrollBar:{true:"scrollbar-hide",false:""}},defaultVariants:{orientation:"vertical",hideScrollBar:!1}})}},e=>{var t=t=>e(e.s=t);e.O(0,[146,992,829,686,874,421,333,441,684,358],()=>t(5970)),_N_E=e.O()}]);