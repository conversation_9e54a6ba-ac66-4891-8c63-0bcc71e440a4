{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n} from '../../client/components/app-router-headers'\nimport {\n  createTrackedMetadataContext,\n  createMetadataContext,\n} from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { setReferenceManifestsSingleton } from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  getFirstDynamicReason,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../shared/lib/router/action-queue'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { ServerPrerenderStreamResult } from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n  prerenderServerWithPhases,\n  prerenderClientWithPhases,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport './clean-async-snapshot.external'\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { isUseCacheTimeoutError } from '../use-cache/use-cache-errors'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n}\n\nconst flightDataPathHeadKey = 'h'\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  // Align the segment with parallel-route-default in next-app-loader\n  const components = loaderTree[2]\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['not-found'],\n        },\n      ],\n    },\n    components,\n  ]\n}\n\nfunction createDivergedMetadataComponents(\n  Metadata: React.ComponentType,\n  serveStreamingMetadata: boolean\n): {\n  StaticMetadata: React.ComponentType<{}>\n  StreamingMetadata: React.ComponentType<{}> | null\n} {\n  function EmptyMetadata() {\n    return null\n  }\n  const StreamingMetadata: React.ComponentType | null = serveStreamingMetadata\n    ? Metadata\n    : null\n\n  const StaticMetadata: React.ComponentType<{}> = serveStreamingMetadata\n    ? EmptyMetadata\n    : Metadata\n\n  return {\n    StaticMetadata,\n    StreamingMetadata,\n  }\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({ ctx }: { ctx: AppRenderContext }) {\n  const is404Page = ctx.pagePath === '/404'\n  const isInvalidStatusCode =\n    typeof ctx.res.statusCode === 'number' && ctx.res.statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  if (!ctx.isAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const searchParams = createServerSearchParamsForMetadata(query, workStore)\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      searchParams,\n      metadataContext: createTrackedMetadataContext(\n        url.pathname,\n        ctx.renderOpts,\n        workStore\n      ),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      createServerParamsForMetadata,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    const { StreamingMetadata, StaticMetadata } =\n      createDivergedMetadataComponents(() => {\n        return (\n          // Adding requestId as react key to make metadata remount for each render\n          <MetadataTree key={requestId} />\n        )\n      }, serveStreamingMetadata)\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex ctx={ctx} />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={requestId} />\n            {StreamingMetadata ? <StreamingMetadata /> : null}\n            <StaticMetadata />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    routeType: ctx.isAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      ctx.workStore.route,\n      requestStore\n    )\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n  if (!renderOpts.dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    ctx.componentMod.tree,\n    ctx.getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ctx.componentMod.renderToReadableStream,\n    rscPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling\n  await cacheSignal.cacheReady()\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n    devRenderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  const searchParams = createServerSearchParamsForMetadata(query, workStore)\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    errorType: is404 ? 'not-found' : undefined,\n    searchParams,\n    metadataContext: createTrackedMetadataContext(\n      url.pathname,\n      ctx.renderOpts,\n      workStore\n    ),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    createServerParamsForMetadata,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const { StreamingMetadata, StaticMetadata } =\n    createDivergedMetadataComponents(() => {\n      return (\n        // Not add requestId as react key to ensure segment prefetch could result consistently if nothing changed\n        <MetadataTree />\n      )\n    }, serveStreamingMetadata)\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadata,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex ctx={ctx} />\n      <ViewportTree key={ctx.requestId} />\n      <StaticMetadata />\n    </React.Fragment>\n  )\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    requestId,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const searchParams = createServerSearchParamsForMetadata(query, workStore)\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    searchParams,\n    // We create an untracked metadata context here because we can't postpone\n    // again during the error render.\n    metadataContext: createMetadataContext(url.pathname, ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    createServerParamsForMetadata,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const { StreamingMetadata, StaticMetadata } =\n    createDivergedMetadataComponents(\n      () => (\n        <React.Fragment key={flightDataPathHeadKey}>\n          {/* Adding requestId as react key to make metadata remount for each render */}\n          <MetadataTree key={requestId} />\n        </React.Fragment>\n      ),\n      serveStreamingMetadata\n    )\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex ctx={ctx} />\n      {/* Adding requestId as react key to make metadata remount for each render */}\n      <ViewportTree key={requestId} />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      <StaticMetadata />\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head>{StreamingMetadata ? <StreamingMetadata /> : null}</head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n  ServerInsertedHTMLProvider,\n  ServerInsertedMetadataProvider,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  ServerInsertedMetadataProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedMetadataProvider>\n        <ServerInsertedHTMLProvider>\n          <AppRouter\n            actionQueue={actionQueue}\n            globalErrorComponentAndStyles={response.G}\n            assetPrefix={response.p}\n          />\n        </ServerInsertedHTMLProvider>\n      </ServerInsertedMetadataProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction AppWithoutContext<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState)\n\n  return (\n    <AppRouter\n      actionQueue={actionQueue}\n      globalErrorComponentAndStyles={response.G}\n      assetPrefix={response.p}\n    />\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  implicitTags: Array<string>,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n    // @ts-ignore\n    globalThis.__next_require__ = instrumented.require\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we wrap the loadChunk in this tracking. This allows us\n    // to treat chunk loading with similar semantics as cache reads to avoid\n    // async loading chunks from causing a prerender to abort too early.\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      trackChunkLoading(loadingChunk)\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to client components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = require('next/dist/compiled/nanoid').nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isActionRequest = getServerActionRequestMetadata(req).isServerAction\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isAction: isActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      workStore,\n      loaderTree,\n      implicitTags\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.revalidatedTags\n    ) {\n      const pendingPromise = Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ]).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } else {\n      // Copy the cache control value onto the render result metadata.\n      metadata.cacheControl = {\n        revalidate:\n          response.collectedRevalidate >= INFINITE_CACHE\n            ? false\n            : response.collectedRevalidate,\n        expire:\n          response.collectedExpire >= INFINITE_CACHE\n            ? undefined\n            : response.collectedExpire,\n      }\n    }\n\n    // provide bailout info for debugging\n    if (metadata.cacheControl?.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.devRenderResumeDataCache ??\n      postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            workStore,\n            notFoundLoaderTree,\n            formState,\n            postponedState\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      workStore,\n      loaderTree,\n      formState,\n      postponedState\n    )\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.revalidatedTags\n    ) {\n      const pendingPromise = Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ]).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n  })\n\n  const { isPrefetchRequest } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.devRenderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const implicitTags = getImplicitTags(\n    renderOpts.routeModule.definition.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    implicitTags,\n    serverComponentsHmrCache,\n    sharedContext\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null\n): Promise<ReadableStream<Uint8Array>> {\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(ctx.nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      renderOpts.dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      renderOpts.experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame(url: string, _functionName: string): boolean {\n                // The default implementation filters out <anonymous> stack frames\n                // but we want to retain them because current Server Components and\n                // built-in Components in parent stacks don't have source location.\n                return !url.startsWith('node:') && !url.includes('node_modules')\n              },\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        workStore.route,\n        requestStore\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          ctx.nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = require('react-dom/server.edge')\n          .resume as (typeof import('react-dom/server.edge'))['resume']\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          postponed,\n          {\n            onError: htmlRendererErrorHandler,\n            nonce: ctx.nonce,\n          }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            ctx.nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = require('react-dom/server.edge')\n      .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={ctx.nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce: ctx.nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: renderOpts.reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath: renderOpts.basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      renderOpts.supportsDynamicResponse !== true ||\n      !!renderOpts.shouldWaitOnAllReady\n\n    const validateRootLayout = renderOpts.dev\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        ctx.nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer: require('react-dom/server.edge'),\n          element: (\n            <AppWithoutContext\n              reactServerStream={errorServerStream}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={ctx.nonce}\n            />\n          ),\n          streamOptions: {\n            nonce: ctx.nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        renderOpts.supportsDynamicResponse !== true ||\n        !!renderOpts.shouldWaitOnAllReady\n      const validateRootLayout = renderOpts.dev\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          ctx.nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  route: string,\n  requestStore: RequestStore\n): Promise<void> {\n  const { componentMod: ComponentMod } = ctx\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    ctx.getDynamicParamFromSegment\n  )\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  const cacheSignal = new CacheSignal()\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const initialClientController = new AbortController()\n  const initialClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: initialClientController.signal,\n    controller: initialClientController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  let initialServerStream\n  try {\n    initialServerStream = workUnitAsyncStorage.run(\n      initialServerPrerenderStore,\n      ComponentMod.renderToReadableStream,\n      firstAttemptRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // The render aborted before this error was handled which indicates\n            // the error is caused by unfinished components within the render\n            return\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n        signal: initialServerRenderController.signal,\n      }\n    )\n  } catch (err: unknown) {\n    if (\n      initialServerPrerenderController.signal.aborted ||\n      initialServerRenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, route)\n    }\n  }\n\n  const nonce = '1'\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider } = createServerInsertedMetadata(nonce)\n\n  if (initialServerStream) {\n    const [warmupStream, renderStream] = initialServerStream.tee()\n    initialServerStream = null\n    // Before we attempt the SSR initial render we need to ensure all client modules\n    // are already loaded.\n    await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={renderStream}\n        preinitScripts={() => {}}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (initialClientController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n      }\n    )\n    pendingInitialClientResult.catch((err: unknown) => {\n      if (initialClientController.signal.aborted) {\n        // We aborted the render normally and can ignore this error\n      } else {\n        // We're going to retry to so we normally would suppress this error but\n        // when verbose logging is on we print it\n        if (process.env.__NEXT_VERBOSE_LOGGING) {\n          printDebugThrownValueForProspectiveRender(err, route)\n        }\n      }\n    })\n  }\n\n  await cacheSignal.cacheReady()\n  // It is important that we abort the SSR render first to avoid\n  // connection closed errors from having an incomplete RSC stream\n  initialClientController.abort()\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We've now filled caches and triggered any inadvertent sync bailouts\n  // due to lazy module initialization. We can restart our render to capture results\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(false)\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const finalClientController = new AbortController()\n  const clientDynamicTracking = createDynamicTrackingState(false)\n  const dynamicValidation = createDynamicValidationState()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const finalServerPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverPrerenderStreamResult = await prerenderServerWithPhases(\n    finalServerController.signal,\n    () =>\n      workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.renderToReadableStream,\n        finalServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          onError: (err) => {\n            if (isUseCacheTimeoutError(err)) {\n              return err.digest\n            }\n\n            if (\n              finalServerController.signal.aborted &&\n              isPrerenderInterruptedError(err)\n            ) {\n              return err.digest\n            }\n\n            return getDigestForWellKnownError(err)\n          },\n          signal: finalServerController.signal,\n        }\n      ),\n    () => {\n      finalServerController.abort()\n    }\n  )\n\n  let rootDidError = false\n  const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n  try {\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    await prerenderClientWithPhases(\n      () =>\n        workUnitAsyncStorage.run(\n          finalClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={serverPhasedStream}\n            preinitScripts={() => {}}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          {\n            signal: finalClientController.signal,\n            onError: (err, errorInfo) => {\n              if (isUseCacheTimeoutError(err)) {\n                dynamicValidation.dynamicErrors.push(err)\n\n                return\n              }\n\n              if (\n                isPrerenderInterruptedError(err) ||\n                finalClientController.signal.aborted\n              ) {\n                if (!rootDidError) {\n                  // If the root errored before we observe this error then it wasn't caused by something dynamic.\n                  // If the root did not error or is erroring because of a sync dynamic API or a prerender interrupt error\n                  // then we are a dynamic route.\n                  requestStore.usedDynamic = true\n                }\n\n                const componentStack = errorInfo.componentStack\n                if (typeof componentStack === 'string') {\n                  trackAllowedDynamicAccess(\n                    route,\n                    componentStack,\n                    dynamicValidation,\n                    serverDynamicTracking,\n                    clientDynamicTracking\n                  )\n                }\n                return\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n          }\n        ),\n      () => {\n        finalClientController.abort()\n        serverPhasedStream.assertExhausted()\n      }\n    )\n  } catch (err) {\n    rootDidError = true\n    if (\n      isPrerenderInterruptedError(err) ||\n      finalClientController.signal.aborted\n    ) {\n      // we don't have a root because the abort errored in the root. We can just ignore this error\n    } else {\n      // If an error is thrown in the root before prerendering is aborted, we\n      // don't want to rethrow it here, otherwise this would lead to a hanging\n      // response and unhandled rejection. We also don't want to log it, because\n      // it's most likely already logged as part of the normal render. So we\n      // just fall through here, to make sure `resolveValidation` is called.\n    }\n  }\n\n  function LogDynamicValidation() {\n    try {\n      throwIfDisallowedDynamic(\n        route,\n        dynamicValidation,\n        serverDynamicTracking,\n        clientDynamicTracking\n      )\n    } catch {}\n    return null\n  }\n\n  resolveValidation(<LogDynamicValidation />)\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  implicitTags: Array<string>\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n  const rootParams = getRootParams(tree, ctx.getDynamicParamFromSegment)\n\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(ctx.nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!renderOpts.experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (renderOpts.experimental.dynamicIO) {\n      if (renderOpts.experimental.isRoutePPREnabled) {\n        /**\n         * dynamicIO with PPR\n         *\n         * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n         * Once we have settled all cache reads we restart the render and abort after a single Task.\n         *\n         * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n         * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n         * and a synchronous abort might prevent us from filling all caches.\n         *\n         * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n         * and the reactServerIsDynamic value to determine how to treat the resulting render\n         */\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        // The cacheSignal helps us track whether caches are still filling or we are ready\n        // to cut the render off.\n        const cacheSignal = new CacheSignal()\n\n        // The resume data cache here should use a fresh instance as it's\n        // performing a fresh prerender. If we get to implementing the\n        // prerendering of an already prerendered page, we should use the passed\n        // resume data cache instead.\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const initialServerPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const pendingInitialServerResult = workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          ComponentMod.prerender,\n          initialServerPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (initialServerPrerenderController.signal.aborted) {\n                // The render aborted before this error was handled which indicates\n                // the error is caused by unfinished components within the render\n                return\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            // we don't care to track postpones during the prospective render because we need\n            // to always do a final render anyway\n            onPostpone: undefined,\n            // We don't want to stop rendering until the cacheSignal is complete so we pass\n            // a different signal to this render call than is used by dynamic APIs to signify\n            // transitioning out of the prerender environment\n            signal: initialServerRenderController.signal,\n          }\n        )\n\n        await cacheSignal.cacheReady()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        let initialServerResult\n        try {\n          initialServerResult = await createReactServerPrerenderResult(\n            pendingInitialServerResult\n          )\n        } catch (err) {\n          if (\n            initialServerRenderController.signal.aborted ||\n            initialServerPrerenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerResult) {\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(\n            initialServerResult.asStream(),\n            clientReferenceManifest\n          )\n\n          const initialClientController = new AbortController()\n          const initialClientPrerenderStore: PrerenderStore = {\n            type: 'prerender',\n            phase: 'render',\n            rootParams,\n            implicitTags: implicitTags,\n            renderSignal: initialClientController.signal,\n            controller: initialClientController,\n            cacheSignal: null,\n            dynamicTracking: null,\n            revalidate: INFINITE_CACHE,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags],\n            prerenderResumeDataCache,\n          }\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          await prerenderAndAbortInSequentialTasks(\n            () =>\n              workUnitAsyncStorage.run(\n                initialClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={initialServerResult.asUnclosingStream()}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={ctx.nonce}\n                />,\n                {\n                  signal: initialClientController.signal,\n                  onError: (err) => {\n                    const digest = getDigestForWellKnownError(err)\n\n                    if (digest) {\n                      return digest\n                    }\n\n                    if (initialClientController.signal.aborted) {\n                      // These are expected errors that might error the prerender. we ignore them.\n                    } else if (\n                      process.env.NEXT_DEBUG_BUILD ||\n                      process.env.__NEXT_VERBOSE_LOGGING\n                    ) {\n                      // We don't normally log these errors because we are going to retry anyway but\n                      // it can be useful for debugging Next.js itself to get visibility here when needed\n                      printDebugThrownValueForProspectiveRender(\n                        err,\n                        workStore.route\n                      )\n                    }\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              initialClientController.abort()\n            }\n          ).catch((err) => {\n            if (\n              initialServerRenderController.signal.aborted ||\n              isPrerenderInterruptedError(err)\n            ) {\n              // These are expected errors that might error the prerender. we ignore them.\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              // We don't normally log these errors because we are going to retry anyway but\n              // it can be useful for debugging Next.js itself to get visibility here when needed\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          })\n        }\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalRenderPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n          finalRenderPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n        let prerenderIsPending = true\n        const reactServerResult = (reactServerPrerenderResult =\n          await createReactServerPrerenderResult(\n            prerenderAndAbortInSequentialTasks(\n              async () => {\n                const prerenderResult = await workUnitAsyncStorage.run(\n                  // The store to scope\n                  finalRenderPrerenderStore,\n                  // The function to run\n                  ComponentMod.prerender,\n                  // ... the arguments for the function to run\n                  finalAttemptRSCPayload,\n                  clientReferenceManifest.clientModules,\n                  {\n                    onError: (err: unknown) => {\n                      return serverComponentsErrorHandler(err)\n                    },\n                    signal: finalServerController.signal,\n                  }\n                )\n                prerenderIsPending = false\n                return prerenderResult\n              },\n              () => {\n                if (finalServerController.signal.aborted) {\n                  // If the server controller is already aborted we must have called something\n                  // that required aborting the prerender synchronously such as with new Date()\n                  serverIsDynamic = true\n                  return\n                }\n\n                if (prerenderIsPending) {\n                  // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                  // there is something unfinished.\n                  serverIsDynamic = true\n                }\n                finalServerController.abort()\n              }\n            )\n          ))\n\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const finalClientController = new AbortController()\n        const finalClientPrerenderStore: PrerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // For HTML Generation we don't need to track cache reads (RSC only)\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        }\n\n        let clientIsDynamic = false\n        let dynamicValidation = createDynamicValidationState()\n\n        const prerender = require('react-dom/static.edge')\n          .prerender as (typeof import('react-dom/static.edge'))['prerender']\n        let { prelude, postponed } = await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={ctx.nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore.route,\n                        componentStack,\n                        dynamicValidation,\n                        serverDynamicTracking,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: renderOpts.reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalRenderPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          if (postponed != null) {\n            // Dynamic HTML case\n            metadata.postponed = await getDynamicHTMLPostponedState(\n              postponed,\n              fallbackRouteParams,\n              prerenderResumeDataCache\n            )\n          } else {\n            // Dynamic Data case\n            metadata.postponed = await getDynamicDataPostponedState(\n              prerenderResumeDataCache\n            )\n          }\n          reactServerResult.consume()\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueDynamicPrerender(prelude, {\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: finalRenderPrerenderStore.stale,\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        } else {\n          // Static case\n          if (workStore.forceDynamic) {\n            throw new StaticGenBailoutError(\n              'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n            )\n          }\n\n          let htmlStream = prelude\n          if (postponed != null) {\n            // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n            // so we can set all the postponed boundaries to client render mode before we store the HTML response\n            const resume = require('react-dom/server.edge')\n              .resume as (typeof import('react-dom/server.edge'))['resume']\n\n            // We don't actually want to render anything so we just pass a stream\n            // that never resolves. The resume call is going to abort immediately anyway\n            const foreverStream = new ReadableStream<Uint8Array>()\n\n            const resumeStream = await resume(\n              <App\n                reactServerStream={foreverStream}\n                preinitScripts={() => {}}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={ctx.nonce}\n              />,\n              JSON.parse(JSON.stringify(postponed)),\n              {\n                signal: createPostponedAbortSignal('static prerender resume'),\n                onError: htmlRendererErrorHandler,\n                nonce: ctx.nonce,\n              }\n            )\n\n            // First we write everything from the prerender, then we write everything from the aborted resume render\n            htmlStream = chainStreams(prelude, resumeStream)\n          }\n\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueStaticPrerender(htmlStream, {\n              inlinedDataStream: createInlinedDataReadableStream(\n                reactServerResult.consumeAsStream(),\n                ctx.nonce,\n                formState\n              ),\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: finalRenderPrerenderStore.stale,\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        }\n      } else {\n        /**\n         * dynamicIO without PPR\n         *\n         * The general approach is to render the RSC tree first allowing for any inflight\n         * caches to resolve. Once we have settled inflight caches we can check and see if any\n         * synchronous dynamic APIs were used. If so we don't need to bother doing anything more\n         * because the page will be dynamic on re-render anyway\n         *\n         * If no sync dynamic APIs were used we then re-render and abort after a single Task.\n         * If the render errors we know that the page has some dynamic IO. This assumes and relies\n         * upon caches reading from a in process memory cache and resolving in a microtask. While this\n         * is true from our own default cache implementation and if you don't exceed our LRU size it\n         * might not be true for custom cache implementations.\n         *\n         * Future implementations can do some different strategies during build like using IPC to\n         * synchronously fill caches during this special rendering mode. For now this heuristic should work\n         */\n\n        const cache = workStore.incrementalCache\n        if (!cache) {\n          throw new Error(\n            'Expected incremental cache to exist. This is a bug in Next.js'\n          )\n        }\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        const cacheSignal = new CacheSignal()\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const initialClientController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: initialClientController.signal,\n          controller: initialClientController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        let initialServerStream\n        try {\n          initialServerStream = workUnitAsyncStorage.run(\n            initialServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            firstAttemptRSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (\n                  initialServerPrerenderController.signal.aborted ||\n                  initialServerRenderController.signal.aborted\n                ) {\n                  // The render aborted before this error was handled which indicates\n                  // the error is caused by unfinished components within the render\n                  return\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              signal: initialServerRenderController.signal,\n            }\n          )\n        } catch (err: unknown) {\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerStream) {\n          const [warmupStream, renderStream] = initialServerStream.tee()\n          initialServerStream = null\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const pendingInitialClientResult = workUnitAsyncStorage.run(\n            initialClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={renderStream}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={ctx.nonce}\n            />,\n            {\n              signal: initialClientController.signal,\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (initialClientController.signal.aborted) {\n                  // These are expected errors that might error the prerender. we ignore them.\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  // We don't normally log these errors because we are going to retry anyway but\n                  // it can be useful for debugging Next.js itself to get visibility here when needed\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              bootstrapScripts: [bootstrapScript],\n            }\n          )\n          pendingInitialClientResult.catch((err: unknown) => {\n            if (initialClientController.signal.aborted) {\n              // We aborted the render normally and can ignore this error\n            } else {\n              // We're going to retry to so we normally would suppress this error but\n              // when verbose logging is on we print it\n              if (process.env.__NEXT_VERBOSE_LOGGING) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            }\n          })\n        }\n\n        await cacheSignal.cacheReady()\n        // It is important that we abort the SSR render first to avoid\n        // connection closed errors from having an incomplete RSC stream\n        initialClientController.abort()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        // We've now filled caches and triggered any inadvertant sync bailouts\n        // due to lazy module initialization. We can restart our render to capture results\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        let clientIsDynamic = false\n        const finalClientController = new AbortController()\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const dynamicValidation = createDynamicValidationState()\n\n        const finalClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const finalServerPayload = await workUnitAsyncStorage.run(\n          finalServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const serverPrerenderStreamResult = (reactServerPrerenderResult =\n          await prerenderServerWithPhases(\n            finalServerController.signal,\n            () =>\n              workUnitAsyncStorage.run(\n                finalServerPrerenderStore,\n                ComponentMod.renderToReadableStream,\n                finalServerPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  onError: (err: unknown) => {\n                    if (finalServerController.signal.aborted) {\n                      serverIsDynamic = true\n                      if (isPrerenderInterruptedError(err)) {\n                        return err.digest\n                      }\n                      return getDigestForWellKnownError(err)\n                    }\n\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              ),\n            () => {\n              finalServerController.abort()\n            }\n          ))\n\n        let htmlStream\n        const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n        try {\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const result = await prerenderClientWithPhases(\n            () =>\n              workUnitAsyncStorage.run(\n                finalClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={serverPhasedStream}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={ctx.nonce}\n                />,\n                {\n                  signal: finalClientController.signal,\n                  onError: (err: unknown, errorInfo: ErrorInfo) => {\n                    if (\n                      isPrerenderInterruptedError(err) ||\n                      finalClientController.signal.aborted\n                    ) {\n                      clientIsDynamic = true\n\n                      const componentStack: string | undefined = (\n                        errorInfo as any\n                      ).componentStack\n                      if (typeof componentStack === 'string') {\n                        trackAllowedDynamicAccess(\n                          workStore.route,\n                          componentStack,\n                          dynamicValidation,\n                          serverDynamicTracking,\n                          clientDynamicTracking\n                        )\n                      }\n                      return\n                    }\n\n                    return htmlRendererErrorHandler(err, errorInfo)\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              finalClientController.abort()\n              serverPhasedStream.assertExhausted()\n            }\n          )\n          htmlStream = result.prelude\n        } catch (err) {\n          if (\n            isPrerenderInterruptedError(err) ||\n            finalClientController.signal.aborted\n          ) {\n            // we don't have a root because the abort errored in the root. We can just ignore this error\n          } else {\n            // This error is something else and should bubble up\n            throw err\n          }\n        }\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          const dynamicReason = serverIsDynamic\n            ? getFirstDynamicReason(serverDynamicTracking)\n            : getFirstDynamicReason(clientDynamicTracking)\n          if (dynamicReason) {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          } else {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically it accessed data without explicitly caching it. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          }\n        }\n\n        const flightData = await streamToBuffer(\n          serverPrerenderStreamResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalClientPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        const validateRootLayout = renderOpts.dev\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueFizzStream(htmlStream!, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              serverPrerenderStreamResult.asStream(),\n              ctx.nonce,\n              formState\n            ),\n            isStaticGeneration: true,\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            validateRootLayout,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: finalServerPrerenderStore.stale,\n          collectedTags: finalServerPrerenderStore.tags,\n        }\n      }\n    } else if (renderOpts.experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(\n        renderOpts.isDebugDynamicAccesses\n      )\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags: implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags: implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n        prerenderResumeDataCache,\n      }\n      const prerender = require('react-dom/static.edge')\n        .prerender as (typeof import('react-dom/static.edge'))['prerender']\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={ctx.nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              appendHeader(key, value)\n            })\n          },\n          maxHeadersLength: renderOpts.reactMaxHeadersLength,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = require('react-dom/server.edge')\n            .resume as (typeof import('react-dom/server.edge'))['resume']\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={ctx.nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce: ctx.nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              ctx.nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags: implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = require('react-dom/server.edge')\n        .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={ctx.nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce: ctx.nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            ctx.nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: prerenderLegacyStore.stale,\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      const fizzStream = await renderToInitialFizzStream({\n        ReactDOMServer: require('react-dom/server.edge'),\n        element: (\n          <AppWithoutContext\n            reactServerStream={errorServerStream}\n            preinitScripts={errorPreinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            nonce={ctx.nonce}\n          />\n        ),\n        streamOptions: {\n          nonce: ctx.nonce,\n          // Include hydration scripts in the HTML\n          bootstrapScripts: [errorBootstrapScript],\n          formState,\n        },\n      })\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const validateRootLayout = renderOpts.dev\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            ctx.nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath: renderOpts.basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale:\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE,\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst loadingChunks: Set<Promise<unknown>> = new Set()\nconst chunkListeners: Array<(x?: unknown) => void> = []\n\nfunction trackChunkLoading(load: Promise<unknown>) {\n  loadingChunks.add(load)\n  load.finally(() => {\n    if (loadingChunks.has(load)) {\n      loadingChunks.delete(load)\n      if (loadingChunks.size === 0) {\n        // We are not currently loading any chunks. We can notify all listeners\n        for (let i = 0; i < chunkListeners.length; i++) {\n          chunkListeners[i]()\n        }\n        chunkListeners.length = 0\n      }\n    }\n  })\n}\n\nexport async function warmFlightResponse(\n  flightStream: ReadableStream<Uint8Array>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n) {\n  let createFromReadableStream\n  if (process.env.TURBOPACK) {\n    createFromReadableStream =\n      // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-turbopack/client.edge').createFromReadableStream\n  } else {\n    createFromReadableStream =\n      // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge').createFromReadableStream\n  }\n\n  try {\n    createFromReadableStream(flightStream, {\n      serverConsumerManifest: {\n        moduleLoading: clientReferenceManifest.moduleLoading,\n        moduleMap: clientReferenceManifest.ssrModuleMapping,\n        serverModuleMap: null,\n      },\n    })\n  } catch {\n    // We don't want to handle errors here but we don't want it to\n    // interrupt the outer flow. We simply ignore it here and expect\n    // it will bubble up during a render\n  }\n\n  // We'll wait at least one task and then if no chunks have started to load\n  // we'll we can infer that there are none to load from this flight response\n  trackChunkLoading(waitAtLeastOneReactRenderTask())\n  return new Promise((r) => {\n    chunkListeners.push(r)\n  })\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<React.ReactNode | undefined> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n\n  return globalErrorStyles\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (!clientReferenceManifest || !renderOpts.experimental.clientSegmentCache) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: null,\n  }\n\n  // When dynamicIO is enabled, missing data is encoded to an infinitely hanging\n  // promise, the absence of which we use to determine if a segment is fully\n  // static or partially static. However, when dynamicIO is not enabled, this\n  // trick doesn't work.\n  //\n  // So if PPR is enabled, and dynamicIO is not, we have to be conservative and\n  // assume all segments are partial.\n  //\n  // TODO: When PPR is on, we can at least optimize the case where the entire\n  // page is static. Either by passing that as an argument to this function, or\n  // by setting a header on the response like the we do for full page RSC\n  // prefetches today. The latter approach might be simpler since it requires\n  // less plumbing, and the client has to check the header regardless to see if\n  // PPR is enabled.\n  const shouldAssumePartialData =\n    renderOpts.experimental.isRoutePPREnabled === true && // PPR is enabled\n    !renderOpts.experimental.dynamicIO // dynamicIO is disabled\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    shouldAssumePartialData,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest,\n    fallbackRouteParams\n  )\n}\n"], "names": ["renderToHTMLOrFlight", "warmFlightResponse", "flightDataPathHeadKey", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "NEXT_ROUTER_PREFETCH_HEADER", "toLowerCase", "undefined", "isHmrRefresh", "NEXT_HMR_REFRESH_HEADER", "isRSCRequest", "RSC_HEADER", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "parseAndValidateFlightRouterState", "NEXT_ROUTER_STATE_TREE_HEADER", "isRouteTreePrefetchRequest", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "csp", "nonce", "getScriptNonceFromHeader", "createNotFoundLoaderTree", "loaderTree", "components", "children", "PAGE_SEGMENT_KEY", "page", "createDivergedMetadataComponents", "<PERSON><PERSON><PERSON>", "serveStreamingMetadata", "EmptyMetadata", "StreamingMetadata", "StaticMetadata", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "getSegmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "dynamicParamTypes", "treeSegment", "split", "slice", "flatMap", "pathSegment", "parseParameter", "join", "getShortDynamicParamType", "NonIndex", "ctx", "is404Page", "isInvalidStatusCode", "res", "statusCode", "isAction", "meta", "name", "content", "generateDynamicRSCPayload", "flightData", "componentMod", "tree", "createServerSearchParamsForMetadata", "createServerParamsForMetadata", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "requestId", "workStore", "url", "renderOpts", "skipFlight", "preloadCallbacks", "searchParams", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "metadataContext", "createTrackedMetadataContext", "pathname", "walkTreeWithFlightRouterState", "loaderTreeToFilter", "parentParams", "rscHead", "React", "Fragment", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "getRevalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "createFlightReactServerErrorHandler", "dev", "RSCPayload", "workUnitAsyncStorage", "run", "process", "env", "NODE_ENV", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "route", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "FlightRenderResult", "fetchMetrics", "warmupDevRender", "InvariantError", "rootParams", "getRootParams", "prerenderResumeDataCache", "createPrerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "CacheSignal", "prerenderStore", "phase", "implicitTags", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "INFINITE_CACHE", "expire", "stale", "tags", "rscPayload", "cacheReady", "abort", "devRenderResumeDataCache", "createRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "GlobalError", "initialTree", "createFlightRouterStateFromLoaderTree", "errorType", "seedData", "createComponentTree", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "NEXT_URL", "initialHead", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "createMetadataContext", "isError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "digest", "data-next-error-stack", "stack", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "ServerInsertedMetadataProvider", "response", "use", "useFlightStream", "initialState", "createInitialRouterState", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "createMutableActionQueue", "HeadManagerContext", "require", "Provider", "appDir", "AppRouter", "globalErrorComponentAndStyles", "AppWithoutContext", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "ComponentMod", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "wrapClientComponentLoader", "globalThis", "__next_require__", "__next_chunk_load__", "args", "loadingChunk", "loadChunk", "trackChunkLoading", "URL", "setIsrStatus", "NEXT_RUNTIME", "isNodeNextRequest", "originalRequest", "on", "ended", "metrics", "getClientComponentLoaderMetrics", "reset", "getTracer", "startSpan", "NextNodeServerSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "metadata", "appUsingSizeAdjust", "serverModuleMap", "createServerModuleMap", "setReferenceManifestsSingleton", "patchFetch", "taintObjectReference", "stripInternalQueries", "crypto", "randomUUID", "nanoid", "isActionRequest", "getServerActionRequestMetadata", "isServerAction", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "AppRenderSpan", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "accessedDynamicData", "isDebugDynamicAccesses", "warn", "access", "formatDynamicAPIAccesses", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "isUserLandError", "pendingRevalidates", "pendingRevalidateWrites", "revalidatedTags", "pendingPromise", "Promise", "all", "incrementalCache", "revalidateTag", "Object", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "NEXT_ROUTER_STALE_TIME_HEADER", "forceStatic", "collectedRevalidate", "cacheControl", "collectedExpire", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "RenderResult", "streamToString", "stream", "renderResumeDataCache", "createRequestStoreForRender", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "handleAction", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "parseRelativeUrl", "parsePostponedState", "getImplicitTags", "routeModule", "definition", "createWorkStore", "workAsyncStorage", "renderServerInsertedHTML", "createServerInsertedHTML", "getServerInsertedMetadata", "createServerInsertedMetadata", "tracingMetadata", "getTracedMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "buildManifest", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "getAssetQueryString", "integrity", "subresourceIntegrityManifest", "crossOrigin", "noModule", "bootstrapScript", "getRequiredScripts", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "createHTMLReactServerErrorHandler", "nextExport", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "createHTMLErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "scheduleInSequentialTasks", "prerenderPhase", "environmentName", "filterStackFrame", "_functionName", "startsWith", "ReactServerResult", "waitAtLeastOneReactRenderTask", "DynamicState", "DATA", "inlinedReactServerDataStream", "createInlinedDataReadableStream", "tee", "chainStreams", "createDocumentClosingStream", "getPostponedFromState", "resume", "htmlStream", "getServerInsertedHTML", "makeGetServerInsertedHTML", "serverCapturedErrors", "basePath", "continueDynamicHTMLResume", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactMaxHeadersLength", "bootstrapScripts", "generateStaticHTML", "supportsDynamicResponse", "shouldWaitOnAllReady", "validateRootLayout", "continueFizzStream", "isStaticGenBailoutError", "shouldBailoutToCSR", "isBailoutToCSRError", "getStackWithoutErrorMessage", "error", "reason", "isHTTPAccessFallbackError", "getAccessFallbackHTTPStatus", "getAccessFallbackErrorTypeByStatus", "isRedirectError", "getRedirectStatusCodeFromError", "redirectUrl", "addPathPrefix", "getURLFromRedirectError", "Headers", "appendMutableCookies", "mutableCookies", "from", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "renderToInitialFizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "initialServerPrerenderStore", "initialClientController", "initialClientPrerenderStore", "firstAttemptRSCPayload", "initialServerStream", "getDigestForWellKnownError", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "printDebugThrownValueForProspectiveRender", "warmupStream", "renderStream", "prerender", "pendingInitialClientResult", "catch", "finalServerController", "serverDynamicTracking", "createDynamicTrackingState", "finalServerPrerenderStore", "finalClientController", "clientDynamicTracking", "dynamicValidation", "createDynamicValidationState", "finalClientPrerenderStore", "finalServerPayload", "serverPrerenderStreamResult", "prerenderServerWithPhases", "isUseCacheTimeoutError", "isPrerenderInterruptedError", "rootDidError", "serverPhasedStream", "asPhasedStream", "prerenderClientWithPhases", "errorInfo", "dynamicErrors", "push", "componentStack", "trackAllowedDynamicAccess", "assertExhausted", "LogDynamicValidation", "throwIfDisallowedDynamic", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "initialServerPayload", "pendingInitialServerResult", "onPostpone", "initialServerResult", "createReactServerPrerenderResult", "asStream", "prerenderAndAbortInSequentialTasks", "asUnclosingStream", "serverIsDynamic", "finalRenderPrerenderStore", "finalAttemptRSCPayload", "prerenderIsPending", "prerenderResult", "clientIsDynamic", "prelude", "streamToBuffer", "segmentData", "collectSegmentData", "getDynamicHTMLPostponedState", "getDynamicDataPostponedState", "continueDynamicPrerender", "consumeDynamicAccess", "StaticGenBailoutError", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "createPostponedAbortSignal", "continueStaticP<PERSON><PERSON>", "consumeAsStream", "cache", "dynamicReason", "getFirstDynamicReason", "DynamicServerError", "reactServerPrerenderStore", "createReactServerPrerenderResultFromRender", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "isDynamicServerError", "flightStream", "ServerPrerenderStreamResult", "loadingChunks", "chunkListeners", "load", "add", "delete", "createFromReadableStream", "TURBOPACK", "serverConsumerManifest", "moduleLoading", "moduleMap", "ssrModuleMapping", "r", "modules", "globalErrorModule", "parseLoaderTree", "styles", "createComponentStylesAndScripts", "filePath", "getComponent", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "edgeRscModuleMapping", "rscModuleMapping", "shouldAssumePartialData", "staleTime"], "mappings": ";;;;;;;;;;;;;;;IAskDaA,oBAAoB;eAApBA;;IAk9ESC,kBAAkB;eAAlBA;;;;0CAxgIf;8DAayC;qEAKzC;sCAWA;+BAC8B;kCAS9B;iCAIA;8BACqC;2BACZ;oCAKzB;0BAIA;+BACyB;8BACA;2BACkB;wBACxB;oCACS;oCAQ5B;0CAIA;iCACyB;0CACS;mDACS;uDACI;+BACzB;8BACO;qBACR;gCACS;oCACI;iCACN;+BACL;2CACY;+CACI;qCACK;qCACf;iCACW;gCAKxC;oCAM8B;mCAI9B;yCAIA;mCACqC;kCAarC;+CAIA;6BAC+B;yBACJ;4BACH;kCACE;kEACX;yCAGyB;0CACN;6BACA;uBACL;yBACH;yCAGW;wCAUc;sCAChB;2BACI;8CAIvC;6BACqB;wBACM;gCACH;QAExB;4BACwB;iDACiB;iCAChB;iCAIzB;gEAEa;gCACmB;8CACM;;;;;;AA8C7C,MAAMC,wBAAwB;AAkB9B,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAACK,6CAA2B,CAACC,WAAW,GAAG,KAAKC;IAEzD,MAAMC,eACJR,OAAO,CAACS,yCAAuB,CAACH,WAAW,GAAG,KAAKC;IAErD,2DAA2D;IAC3D,MAAMG,eACJR,sBAAsBF,OAAO,CAACW,4BAAU,CAACL,WAAW,GAAG,KAAKC;IAE9D,MAAMK,iCACJF,gBAAiB,CAAA,CAACN,qBAAqB,CAACH,QAAQY,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBG,IAAAA,oEAAiC,EAC/Bf,OAAO,CAACgB,+CAA6B,CAACV,WAAW,GAAG,IAEtDC;IAEJ,sEAAsE;IACtE,MAAMU,6BACJjB,OAAO,CAACkB,qDAAmC,CAACZ,WAAW,GAAG,KAAK;IAEjE,MAAMa,MACJnB,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMoB,QACJ,OAAOD,QAAQ,WAAWE,IAAAA,kDAAwB,EAACF,OAAOZ;IAE5D,OAAO;QACLO;QACAV;QACAa;QACAT;QACAE;QACAR;QACAkB;IACF;AACF;AAEA,SAASE,yBAAyBC,UAAsB;IACtD,mEAAmE;IACnE,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,OAAO;QACL;QACA;YACEE,UAAU;gBACRC,yBAAgB;gBAChB,CAAC;gBACD;oBACEC,MAAMH,UAAU,CAAC,YAAY;gBAC/B;aACD;QACH;QACAA;KACD;AACH;AAEA,SAASI,iCACPC,QAA6B,EAC7BC,sBAA+B;IAK/B,SAASC;QACP,OAAO;IACT;IACA,MAAMC,oBAAgDF,yBAClDD,WACA;IAEJ,MAAMI,iBAA0CH,yBAC5CC,gBACAF;IAEJ,OAAO;QACLI;QACAD;IACF;AACF;AAEA;;CAEC,GACD,SAASE,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAeC,IAAAA,gCAAe,EAACF;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAME,MAAMF,aAAaG,KAAK;QAE9B,IAAIC,QAAQT,MAAM,CAACO,IAAI;QAEvB,IAAIL,uBAAuBA,oBAAoBQ,GAAG,CAACL,aAAaG,KAAK,GAAG;YACtEC,QAAQP,oBAAoBS,GAAG,CAACN,aAAaG,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaZ,aAAaa,IAAI,KAAK;YACzC,MAAMC,qBAAqBd,aAAaa,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmBC,2CAAiB,CAAChB,aAAaa,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNE,aAAa;4BAACf;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQR,SACLsB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMlB,QAAQmB,IAAAA,0BAAc,EAACD;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAO1B,MAAM,CAACQ,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCE,aAAa;wBAACf;wBAAKE,MAAMmB,IAAI,CAAC;wBAAMR;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAOW,IAAAA,kDAAwB,EAACxB,aAAaa,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDa,aAAa;gBAACf;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMmB,IAAI,CAAC,OAAOnB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASY,SAAS,EAAEC,GAAG,EAA6B;IAClD,MAAMC,YAAYD,IAAI9B,QAAQ,KAAK;IACnC,MAAMgC,sBACJ,OAAOF,IAAIG,GAAG,CAACC,UAAU,KAAK,YAAYJ,IAAIG,GAAG,CAACC,UAAU,GAAG;IAEjE,gEAAgE;IAChE,IAAI,CAACJ,IAAIK,QAAQ,IAAKJ,CAAAA,aAAaC,mBAAkB,GAAI;QACvD,qBAAO,qBAACI;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbT,GAAqB,EACrBjE,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAI2E,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAMvD,UAAU,EAChBwD,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACD7C,0BAA0B,EAC1B8C,sBAAsB,EACtBC,KAAK,EACLC,SAAS,EACTxE,iBAAiB,EACjByE,SAAS,EACTC,GAAG,EACJ,GAAGtB;IAEJ,MAAMpC,yBAAyB,CAAC,CAACoC,IAAIuB,UAAU,CAAC3D,sBAAsB;IAEtE,IAAI,EAAC7B,2BAAAA,QAASyF,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAMC,eAAeb,oCAAoCM,OAAOE;QAChE,MAAM,EACJM,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGhB,yBAAyB;YAC3BH,MAAMvD;YACNqE;YACAM,iBAAiBC,IAAAA,6CAA4B,EAC3CX,IAAIY,QAAQ,EACZlC,IAAIuB,UAAU,EACdF;YAEFjD;YACA8C;YACAJ;YACAO;YACAL;YACAC;YACArD;QACF;QAEA,MAAM,EAAEE,iBAAiB,EAAEC,cAAc,EAAE,GACzCL,iCAAiC;YAC/B,OACE,yEAAyE;0BACzE,qBAACkE,kBAAkBR;QAEvB,GAAGxD;QAEL8C,aAAa,AACX,CAAA,MAAMyB,IAAAA,4DAA6B,EAAC;YAClCnC;YACAoC,oBAAoB/E;YACpBgF,cAAc,CAAC;YACfzF;YACA,+CAA+C;YAC/C0F,uBACE,sBAACC,cAAK,CAACC,QAAQ;;kCAEb,qBAACzC;wBAASC,KAAKA;;kCAEf,qBAAC2B,kBAAkBP;oBAClBtD,kCAAoB,qBAACA,yBAAuB;kCAC7C,qBAACC;;eANkBnC;YASvB6G,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBhB;YACAC;YACAL;YACAM;QACF,EAAC,EACDhD,GAAG,CAAC,CAAC+D,OAASA,KAAKrD,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAI1D,2BAAAA,QAASgH,YAAY,EAAE;QACzB,OAAO;YACLC,GAAGjH,QAAQgH,YAAY;YACvBE,GAAGvC;YACHwC,GAAGlD,IAAImD,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAGlD,IAAImD,aAAa,CAACC,OAAO;QAC5BH,GAAGvC;QACH2C,GAAGhC,UAAUiC,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACPvD,GAAqB,EACrBwD,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAW1D,IAAI9B,QAAQ;QACvByF,WAAW3D,IAAIK,QAAQ,GAAG,WAAW;QACrCmD;QACAI,kBAAkBC,IAAAA,0BAAmB,EAAC7D,IAAIqB,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAeyC,kCACbC,GAAoB,EACpB/D,GAAqB,EACrBgE,YAA0B,EAC1BjI,OAMC;IAED,MAAMwF,aAAavB,IAAIuB,UAAU;IAEjC,SAAS0C,wBAAwBC,GAAkB;QACjD,OAAO3C,WAAW4C,6BAA6B,oBAAxC5C,WAAW4C,6BAA6B,MAAxC5C,YACL2C,KACAH,KACAR,mBAAmBvD,KAAK;IAE5B;IACA,MAAMoE,UAAUC,IAAAA,uDAAmC,EACjD,CAAC,CAAC9C,WAAW+C,GAAG,EAChBL;IAGF,MAAMM,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACAvD,2BACAT,KACAjE;IAGF,IACE,qDAAqD;IACrDwF,WAAW+C,GAAG,IACd,uEAAuE;IACvEI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzErD,WAAWsD,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CV,WAAWW,WAAW,GAAGF;QAEzBG,4BACEJ,mBACA/E,IAAIW,YAAY,CAACC,IAAI,EACrBZ,KACA,OACAA,IAAIoF,uBAAuB,EAC3BpF,IAAIqB,SAAS,CAACgE,KAAK,EACnBrB;IAEJ;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMsB,uBAAuBd,kDAAoB,CAACC,GAAG,CACnDT,cACAhE,IAAIW,YAAY,CAAC4E,sBAAsB,EACvChB,YACAvE,IAAIoF,uBAAuB,CAACI,aAAa,EACzC;QACEpB;QACAqB,mBAAmB,EAAE1J,2BAAAA,QAAS0J,mBAAmB;IACnD;IAGF,OAAO,IAAIC,sCAAkB,CAACJ,sBAAsB;QAClDK,cAAc3F,IAAIqB,SAAS,CAACsE,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACb7B,GAAoB,EACpB/D,GAAqB;IAErB,MAAMuB,aAAavB,IAAIuB,UAAU;IACjC,IAAI,CAACA,WAAW+C,GAAG,EAAE;QACnB,MAAM,qBAEL,CAFK,IAAIuB,8BAAc,CACtB,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMC,aAAaC,IAAAA,kCAAa,EAC9B/F,IAAIW,YAAY,CAACC,IAAI,EACrBZ,IAAI5B,0BAA0B;IAGhC,SAAS6F,wBAAwBC,GAAkB;QACjD,OAAO3C,WAAW4C,6BAA6B,oBAAxC5C,WAAW4C,6BAA6B,MAAxC5C,YACL2C,KACAH,KACAR,mBAAmBvD,KAAK;IAE5B;IACA,MAAMoE,UAAUC,IAAAA,uDAAmC,EACjD,MACAJ;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAM+B,2BAA2BC,IAAAA,+CAA8B;IAE/D,MAAMC,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAIC,wBAAW;IAEnC,MAAMC,iBAAiC;QACrCpH,MAAM;QACNqH,OAAO;QACPV;QACAW,cAAc,EAAE;QAChBC,cAAcR,iBAAiBS,MAAM;QACrCC,YAAYR;QACZC;QACAQ,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMmB,aAAa,MAAM3C,kDAAoB,CAACC,GAAG,CAC/C8B,gBACA9F,2BACAT;IAGF,0FAA0F;IAC1F,mCAAmC;IACnCwE,kDAAoB,CAACC,GAAG,CACtB8B,gBACAvG,IAAIW,YAAY,CAAC4E,sBAAsB,EACvC4B,YACAnH,IAAIoF,uBAAuB,CAACI,aAAa,EACzC;QACEpB;QACAuC,QAAQT,iBAAiBS,MAAM;IACjC;IAGF,6CAA6C;IAC7C,MAAMN,YAAYe,UAAU;IAC5B,uFAAuF;IACvFb,eAAeP,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBE,iBAAiBmB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAI3B,sCAAkB,CAAC,IAAI;QAChCC,cAAc3F,IAAIqB,SAAS,CAACsE,YAAY;QACxC2B,0BAA0BC,IAAAA,4CAA2B,EACnDvB;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASwB,2BAA2BlG,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIY,QAAQ,GAAGZ,IAAImG,MAAM,AAAD,EAAGjI,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAekI,cACb9G,IAAgB,EAChBZ,GAAqB,EACrB2H,KAAc;IAEd,MAAMlF,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIkF;IAEJ,sDAAsD;IACtD,IAAIlD,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CgD,eAAe,IAAIlF;IACrB;IAEA,MAAM,EACJtE,0BAA0B,EAC1B+C,KAAK,EACLD,sBAAsB,EACtBP,cAAc,EACZkH,WAAW,EACXhH,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDK,GAAG,EACHD,SAAS,EACV,GAAGrB;IAEJ,MAAM8H,cAAcC,IAAAA,4EAAqC,EACvDnH,MACAxC,4BACA+C;IAEF,MAAMvD,yBAAyB,CAAC,CAACoC,IAAIuB,UAAU,CAAC3D,sBAAsB;IAEtE,MAAM8D,eAAeb,oCAAoCM,OAAOE;IAChE,MAAM,EACJM,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGhB,yBAAyB;QAC3BH;QACAoH,WAAWL,QAAQ,cAActL;QACjCqF;QACAM,iBAAiBC,IAAAA,6CAA4B,EAC3CX,IAAIY,QAAQ,EACZlC,IAAIuB,UAAU,EACdF;QAEFjD;QACA8C;QACAJ;QACAO;QACAL;QACAC;QACArD;IACF;IAEA,MAAM6D,mBAAqC,EAAE;IAE7C,MAAM,EAAE3D,iBAAiB,EAAEC,cAAc,EAAE,GACzCL,iCAAiC;QAC/B,OACE,yGAAyG;sBACzG,qBAACkE;IAEL,GAAGhE;IAEL,MAAMqK,WAAW,MAAMC,IAAAA,wCAAmB,EAAC;QACzClI;QACA3C,YAAYuD;QACZyB,cAAc,CAAC;QACfI;QACAE;QACAC;QACAC,oBAAoB;QACpBhB;QACAC;QACA8F;QACAnG;QACA0G,gBAAgBnI,IAAIuB,UAAU,CAACsD,YAAY,CAACsD,cAAc;QAC1DrK;QACAiE;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMqG,aAAapI,IAAIG,GAAG,CAACkI,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACC,0BAAQ;IAEhE,MAAMC,4BACJ,sBAAClG,cAAK,CAACC,QAAQ;;0BACb,qBAACzC;gBAASC,KAAKA;;0BACf,qBAAC2B,kBAAkB3B,IAAIoB,SAAS;0BAChC,qBAACrD;;OAHkBnC;IAOvB,MAAM8M,oBAAoB,MAAMC,qBAAqB/H,MAAMZ;IAE3D,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAM4I,wBACJvH,UAAUiC,kBAAkB,IAC5BtD,IAAIuB,UAAU,CAACsD,YAAY,CAAClI,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FkM,iBAAG,qBAACC;YAASrH,kBAAkBA;;QAC/ByB,GAAGlD,IAAImD,aAAa,CAACC,OAAO;QAC5B2F,GAAG/I,IAAIgJ,WAAW;QAClBC,GAAGzB,2BAA2BlG;QAC9BtC,GAAG,CAAC,CAACsJ;QACLrF,GAAG;YACD;gBACE6E;gBACAG;gBACAQ;gBACAG;aACD;SACF;QACDM,GAAGtB;QACHuB,GAAG;YAACtB;YAAaa;SAAkB;QACnCU,GAAG,OAAOpJ,IAAIuB,UAAU,CAAC8H,SAAS,KAAK;QACvChG,GAAGhC,UAAUiC,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAASwF,SAAS,EAAErH,gBAAgB,EAAoC;IACtEA,iBAAiB6H,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb5I,IAAgB,EAChBZ,GAAqB,EACrByJ,QAAiB,EACjBzB,SAAqD;IAErD,MAAM,EACJ5J,0BAA0B,EAC1B+C,KAAK,EACLD,sBAAsB,EACtBP,cAAc,EACZkH,WAAW,EACXhH,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDK,GAAG,EACHF,SAAS,EACTC,SAAS,EACV,GAAGrB;IAEJ,MAAMpC,yBAAyB,CAAC,CAACoC,IAAIuB,UAAU,CAAC3D,sBAAsB;IACtE,MAAM8D,eAAeb,oCAAoCM,OAAOE;IAChE,MAAM,EAAEO,YAAY,EAAED,YAAY,EAAE,GAAGZ,yBAAyB;QAC9DH;QACAc;QACA,yEAAyE;QACzE,iCAAiC;QACjCM,iBAAiB0H,IAAAA,sCAAqB,EAACpI,IAAIY,QAAQ,EAAElC,IAAIuB,UAAU;QACnEyG;QACA5J;QACA8C;QACAJ;QACAO;QACAL;QACAC;QACArD,wBAAwBA;IAC1B;IAEA,MAAM,EAAEE,iBAAiB,EAAEC,cAAc,EAAE,GACzCL,iCACE,kBACE,qBAAC6E,cAAK,CAACC,QAAQ;sBAEb,cAAA,qBAACZ,kBAAkBR;WAFAxF,wBAKvBgC;IAGJ,MAAM6K,4BACJ,sBAAClG,cAAK,CAACC,QAAQ;;0BACb,qBAACzC;gBAASC,KAAKA;;0BAEf,qBAAC2B,kBAAkBP;YAClBsD,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,qBAACtE;gBAAKC,MAAK;gBAAaC,SAAQ;;0BAElC,qBAACzC;;OAPkBnC;IAWvB,MAAMkM,cAAcC,IAAAA,4EAAqC,EACvDnH,MACAxC,4BACA+C;IAGF,IAAI+C,MAAyB7H;IAC7B,IAAIoN,UAAU;QACZvF,MAAMyF,IAAAA,gBAAO,EAACF,YAAYA,WAAW,qBAAwB,CAAxB,IAAIG,MAAMH,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMxB,WAA8B;QAClCH,WAAW,CAAC,EAAE;sBACd,sBAAC+B;YAAKC,IAAG;;8BACP,qBAACC;8BAAMjM,kCAAoB,qBAACA,yBAAuB;;8BACnD,qBAACkM;8BACEtF,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBV,oBACxC,qBAAC+F;wBACCC,2BAAyBhG,IAAIiG,OAAO;wBACpCC,0BAAwB,YAAYlG,MAAMA,IAAImG,MAAM,GAAG;wBACvDC,yBAAuBpG,IAAIqG,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM7B,oBAAoB,MAAMC,qBAAqB/H,MAAMZ;IAE3D,MAAM4I,wBACJvH,UAAUiC,kBAAkB,IAC5BtD,IAAIuB,UAAU,CAACsD,YAAY,CAAClI,iBAAiB,KAAK;IAEpD,OAAO;QACLuG,GAAGlD,IAAImD,aAAa,CAACC,OAAO;QAC5B2F,GAAG/I,IAAIgJ,WAAW;QAClBC,GAAGzB,2BAA2BlG;QAC9B4H,GAAG7M;QACH2C,GAAG;QACHiE,GAAG;YACD;gBACE6E;gBACAG;gBACAQ;gBACAG;aACD;SACF;QACDO,GAAG;YAACtB;YAAaa;SAAkB;QACnCU,GAAG,OAAOpJ,IAAIuB,UAAU,CAAC8H,SAAS,KAAK;QACvChG,GAAGhC,UAAUiC,kBAAkB;IACjC;AACF;AAEA,mFAAmF;AACnF,SAASkH,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACdtF,uBAAuB,EACvBlI,KAAK,EACLyN,0BAA0B,EAC1BC,8BAA8B,EAQ/B;IACCF;IACA,MAAMG,WAAWtI,cAAK,CAACuI,GAAG,CACxBC,IAAAA,kCAAe,EACbN,mBACArF,yBACAlI;IAIJ,MAAM8N,eAAeC,IAAAA,kDAAwB,EAAC;QAC5CC,mBAAmBL,SAAS5H,CAAC;QAC7BkI,0BAA0BN,SAAS5B,CAAC;QACpCmC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVhD,oBAAoBuC,SAAS7L,CAAC;QAC9BqK,WAAWwB,SAASzB,CAAC;QACrBmC,aAAaV,SAASxH,CAAC;IACzB;IAEA,MAAMmI,cAAcC,IAAAA,qCAAwB,EAACT;IAE7C,MAAM,EAAEU,kBAAkB,EAAE,GAC1BC,QAAQ;IAEV,qBACE,qBAACD,mBAAmBE,QAAQ;QAC1BlN,OAAO;YACLmN,QAAQ;YACR3O;QACF;kBAEA,cAAA,qBAAC0N;sBACC,cAAA,qBAACD;0BACC,cAAA,qBAACmB,kBAAS;oBACRN,aAAaA;oBACbO,+BAA+BlB,SAAS1B,CAAC;oBACzCH,aAAa6B,SAAS9B,CAAC;;;;;AAMnC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAASiD,kBAAqB,EAC5BvB,iBAAiB,EACjBC,cAAc,EACdtF,uBAAuB,EACvBlI,KAAK,EAMN;IACCwN;IACA,MAAMG,WAAWtI,cAAK,CAACuI,GAAG,CACxBC,IAAAA,kCAAe,EACbN,mBACArF,yBACAlI;IAIJ,MAAM8N,eAAeC,IAAAA,kDAAwB,EAAC;QAC5CC,mBAAmBL,SAAS5H,CAAC;QAC7BkI,0BAA0BN,SAAS5B,CAAC;QACpCmC,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACVhD,oBAAoBuC,SAAS7L,CAAC;QAC9BqK,WAAWwB,SAASzB,CAAC;QACrBmC,aAAaV,SAASxH,CAAC;IACzB;IAEA,MAAMmI,cAAcC,IAAAA,qCAAwB,EAACT;IAE7C,qBACE,qBAACc,kBAAS;QACRN,aAAaA;QACbO,+BAA+BlB,SAAS1B,CAAC;QACzCH,aAAa6B,SAAS9B,CAAC;;AAG7B;AASA,eAAekD,yBACblI,GAAoB,EACpB5D,GAAqB,EACrBmB,GAAwC,EACxCpD,QAAgB,EAChBiD,KAAyB,EACzBI,UAAsB,EACtBF,SAAoB,EACpB6K,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrC3F,YAA2B,EAC3B4F,wBAA8D,EAC9DlJ,aAA+B;IAE/B,MAAMmJ,iBAAiBpO,aAAa;IACpC,IAAIoO,gBAAgB;QAClBnM,IAAIC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAMmM,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACb7D,cAAc,EAAE,EAChB8D,cAAc,EACf,GAAGvL;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAIoL,aAAaI,YAAY,EAAE;QAC7B,MAAMC,eAAeC,IAAAA,wDAAyB,EAACN;QAC/C,aAAa;QACbO,WAAWC,gBAAgB,GAAGH,aAAarB,OAAO;QAClD,kEAAkE;QAClE,qEAAqE;QACrE,wEAAwE;QACxE,oEAAoE;QACpE,MAAMyB,sBAAqD,CAAC,GAAGC;YAC7D,MAAMC,eAAeN,aAAaO,SAAS,IAAIF;YAC/CG,kBAAkBF;YAClB,OAAOA;QACT;QACA,mBAAmB;QACnBJ,WAAWE,mBAAmB,GAAGA;IACnC;IAEA,IAAI1I,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAE1C,QAAQ,EAAE,GAAG,IAAIuL,IAAI1J,IAAIzC,GAAG,IAAI,KAAK;QAC7CC,WAAWmM,YAAY,oBAAvBnM,WAAWmM,YAAY,MAAvBnM,YAA0BW,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DwC,QAAQC,GAAG,CAACgJ,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAC7J,MAClB;QACAA,IAAI8J,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5B3B,kBAAkB4B,KAAK,GAAG;YAE1B,IAAI,iBAAiBb,YAAY;gBAC/B,MAAMc,UAAUC,IAAAA,8DAA+B,EAAC;oBAAEC,OAAO;gBAAK;gBAC9D,IAAIF,SAAS;oBACXG,IAAAA,iBAAS,IACNC,SAAS,CAACC,6BAAkB,CAACC,sBAAsB,EAAE;wBACpDC,WAAWP,QAAQQ,wBAAwB;wBAC3CC,YAAY;4BACV,iCACET,QAAQU,wBAAwB;4BAClC,kBAAkBL,6BAAkB,CAACC,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFX,QAAQQ,wBAAwB,GAC9BR,QAAQY,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMC,WAAwC,CAAC;IAE/C,MAAM3N,yBAAyB,CAAC,EAAC0L,oCAAAA,iBAAkBkC,kBAAkB;IAErE,4BAA4B;IAC5B,MAAM1J,0BAA0B7D,WAAW6D,uBAAuB;IAElE,MAAM2J,kBAAkBC,IAAAA,kCAAqB,EAAC;QAAEtC;IAAsB;IAEtEuC,IAAAA,+CAA8B,EAAC;QAC7BxR,MAAM4D,UAAU5D,IAAI;QACpB2H;QACAsH;QACAqC;IACF;IAEApC,aAAauC,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAEtO,MAAMvD,UAAU,EAAE8R,oBAAoB,EAAE,GAAGxC;IAEnD,IAAIG,gBAAgB;QAClBqC,qBACE,kFACAzK,QAAQC,GAAG;IAEf;IAEAtD,UAAUsE,YAAY,GAAG,EAAE;IAC3BkJ,SAASlJ,YAAY,GAAGtE,UAAUsE,YAAY;IAE9C,qCAAqC;IACrCxE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBiO,IAAAA,mCAAoB,EAACjO;IAErB,MAAM,EACJvE,iBAAiB,EACjBV,iBAAiB,EACjBM,YAAY,EACZR,kBAAkB,EAClBM,YAAY,EACZY,KAAK,EACN,GAAGgP;IAEJ;;;GAGC,GACD,IAAI9K;IAEJ,IAAIsD,QAAQC,GAAG,CAACgJ,YAAY,KAAK,QAAQ;QACvCvM,YAAYiO,OAAOC,UAAU;IAC/B,OAAO;QACLlO,YAAYuK,QAAQ,6BAA6B4D,MAAM;IACzD;IAEA;;GAEC,GACD,MAAMtR,SAASsD,WAAWtD,MAAM,IAAI,CAAC;IAErC,MAAM,EAAEqF,kBAAkB,EAAEnF,mBAAmB,EAAE,GAAGkD;IAEpD,MAAMjD,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMqR,kBAAkBC,IAAAA,uDAA8B,EAAC1L,KAAK2L,cAAc;IAE1E,MAAM1P,MAAwB;QAC5BW,cAAcgM;QACdrL;QACAC;QACAF;QACA6K;QACA9N;QACA+C;QACAwO,YAAYzT;QACZmE,UAAUmP;QACVjD;QACArL;QACAtE;QACAwE;QACAlD;QACAkH;QACA4D;QACAsD;QACApP;QACAiD;QACAgD;IACF;IAEAgL,IAAAA,iBAAS,IAAGyB,oBAAoB,CAAC,cAAc1R;IAE/C,IAAIoF,oBAAoB;YA6GlBuL;QA5GJ,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMgB,+BAA+B1B,IAAAA,iBAAS,IAAG2B,IAAI,CACnDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAE/R,UAAU;YAC7CuQ,YAAY;gBACV,cAAcvQ;YAChB;QACF,GACAgS;QAGF,MAAMrF,WAAW,MAAMgF,6BACrB9L,KACA5D,KACAH,KACA6O,UACAxN,WACAhE,YACAoJ;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACEoE,SAASsF,aAAa,IACtBC,IAAAA,qCAAmB,EAACvF,SAASsF,aAAa,KAC1C5O,WAAW8O,sBAAsB,EACjC;YACAC,IAAAA,SAAI,EAAC;YACL,KAAK,MAAMC,UAAUC,IAAAA,0CAAwB,EAAC3F,SAASsF,aAAa,EAAG;gBACrEG,IAAAA,SAAI,EAACC;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAI1F,SAAS4F,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoB9F,SAAS4F,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAGnS,KAAK;YACxE,IAAIiS,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAI9F,SAASiG,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoB9F,SAASiG,SAAS,CAACE,IAAI,CAAC,CAAC9M,MACjD+M,IAAAA,mCAAe,EAAC/M;YAElB,IAAIyM,mBAAmB,MAAMA;QAC/B;QAEA,MAAM5U,UAA+B;YACnC8S;QACF;QACA,oEAAoE;QACpE,IACExN,UAAU6P,kBAAkB,IAC5B7P,UAAU8P,uBAAuB,IACjC9P,UAAU+P,eAAe,EACzB;gBAEE/P;YADF,MAAMgQ,iBAAiBC,QAAQC,GAAG,CAAC;iBACjClQ,8BAAAA,UAAUmQ,gBAAgB,qBAA1BnQ,4BAA4BoQ,aAAa,CACvCpQ,UAAU+P,eAAe,IAAI,EAAE;mBAE9BM,OAAOd,MAAM,CAACvP,UAAU6P,kBAAkB,IAAI,CAAC;mBAC9C7P,UAAU8P,uBAAuB,IAAI,EAAE;aAC5C,EAAEQ,OAAO,CAAC;gBACT,IAAIjN,QAAQC,GAAG,CAACiN,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CxQ;gBAC3D;YACF;YAEA,IAAIC,WAAWwQ,SAAS,EAAE;gBACxBxQ,WAAWwQ,SAAS,CAACV;YACvB,OAAO;gBACLtV,QAAQgW,SAAS,GAAGV;YACtB;QACF;QAEA,IAAIxG,SAASmH,aAAa,EAAE;YAC1BnD,SAASoD,SAAS,GAAGpH,SAASmH,aAAa,CAACnS,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAMqS,cAAcC,OAAOtH,SAASuH,cAAc;QAClDjS,IAAIkS,SAAS,CAACC,+CAA6B,EAAEJ;QAC7CrD,SAAS/S,OAAO,KAAK,CAAC;QACtB+S,SAAS/S,OAAO,CAACwW,+CAA6B,CAAC,GAAGJ;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAI7Q,UAAUkR,WAAW,KAAK,SAAS1H,SAAS2H,mBAAmB,KAAK,GAAG;YACzE3D,SAAS4D,YAAY,GAAG;gBAAE3L,YAAY;gBAAGE,QAAQ3K;YAAU;QAC7D,OAAO;YACL,gEAAgE;YAChEwS,SAAS4D,YAAY,GAAG;gBACtB3L,YACE+D,SAAS2H,mBAAmB,IAAIzL,0BAAc,GAC1C,QACA8D,SAAS2H,mBAAmB;gBAClCxL,QACE6D,SAAS6H,eAAe,IAAI3L,0BAAc,GACtC1K,YACAwO,SAAS6H,eAAe;YAChC;QACF;QAEA,qCAAqC;QACrC,IAAI7D,EAAAA,yBAAAA,SAAS4D,YAAY,qBAArB5D,uBAAuB/H,UAAU,MAAK,GAAG;YAC3C+H,SAAS8D,iBAAiB,GAAG;gBAC3BC,aAAavR,UAAUwR,uBAAuB;gBAC9CtI,OAAOlJ,UAAUyR,iBAAiB;YACpC;QACF;QAEA,OAAO,IAAIC,qBAAY,CAAC,MAAMC,IAAAA,oCAAc,EAACnI,SAASoI,MAAM,GAAGlX;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMmX,wBACJ3R,WAAW+F,wBAAwB,KACnC8E,kCAAAA,eAAgB8G,qBAAqB;QAEvC,MAAMpN,aAAaC,IAAAA,kCAAa,EAAC1I,YAAY2C,IAAI5B,0BAA0B;QAC3E,MAAM4F,eAAemP,IAAAA,yCAA2B,EAC9CpP,KACA5D,KACAmB,KACAwE,YACAW,cACAlF,WAAW6R,eAAe,EAC1B7R,WAAW8R,YAAY,EACvB/W,cACA+P,0BACA6G;QAGF,IACExO,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBrD,WAAWmM,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7DhJ,QAAQC,GAAG,CAACgJ,YAAY,KAAK,UAC7BC,IAAAA,0BAAiB,EAAC7J,QAClB,CAAC/H,oBACD;YACA,MAAM0R,eAAenM,WAAWmM,YAAY;YAC5C3J,IAAI8J,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAAC9J,aAAasP,WAAW,IAAI,CAACjS,UAAUkS,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAErR,QAAQ,EAAE,GAAG,IAAIuL,IAAI1J,IAAIzC,GAAG,IAAI,KAAK;oBAC7CoM,aAAaxL,UAAU;gBACzB;YACF;QACF;QAEA,IAAIlG,oBAAoB;YACtB,OAAO4J,gBAAgB7B,KAAK/D;QAC9B,OAAO,IAAIxD,cAAc;YACvB,OAAOsH,kCAAkCC,KAAK/D,KAAKgE;QACrD;QAEA,MAAMwP,4BAA4BrF,IAAAA,iBAAS,IAAG2B,IAAI,CAChDC,wBAAa,CAACC,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAE/R,UAAU;YAC1CuQ,YAAY;gBACV,cAAcvQ;YAChB;QACF,GACAuV;QAGF,IAAIC,YAAwB;QAC5B,IAAIlE,iBAAiB;YACnB,gFAAgF;YAChF,MAAMmE,sBAAsB,MAAMC,IAAAA,2BAAY,EAAC;gBAC7C7P;gBACA5D;gBACAwM;gBACAoC;gBACA8E,gBAAgB/P;gBAChBzC;gBACA2C;gBACA6I;gBACA7M;YACF;YAEA,IAAI2T,qBAAqB;gBACvB,IAAIA,oBAAoBxU,IAAI,KAAK,aAAa;oBAC5C,MAAM2U,qBAAqB1W,yBAAyBC;oBACpD8C,IAAIC,UAAU,GAAG;oBACjB,MAAM6S,SAAS,MAAMO,0BACnBxP,cACAD,KACA5D,KACAH,KACAqB,WACAyS,oBACAJ,WACAtH;oBAGF,OAAO,IAAI2G,qBAAY,CAACE,QAAQ;wBAAEpE;oBAAS;gBAC7C,OAAO,IAAI8E,oBAAoBxU,IAAI,KAAK,QAAQ;oBAC9C,IAAIwU,oBAAoBI,MAAM,EAAE;wBAC9BJ,oBAAoBI,MAAM,CAACC,cAAc,CAACnF;wBAC1C,OAAO8E,oBAAoBI,MAAM;oBACnC,OAAO,IAAIJ,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAM3X,UAA+B;YACnC8S;QACF;QAEA,MAAMoE,SAAS,MAAMO,0BACnBxP,cACAD,KACA5D,KACAH,KACAqB,WACAhE,YACAqW,WACAtH;QAGF,oEAAoE;QACpE,IACE/K,UAAU6P,kBAAkB,IAC5B7P,UAAU8P,uBAAuB,IACjC9P,UAAU+P,eAAe,EACzB;gBAEE/P;YADF,MAAMgQ,iBAAiBC,QAAQC,GAAG,CAAC;iBACjClQ,+BAAAA,UAAUmQ,gBAAgB,qBAA1BnQ,6BAA4BoQ,aAAa,CACvCpQ,UAAU+P,eAAe,IAAI,EAAE;mBAE9BM,OAAOd,MAAM,CAACvP,UAAU6P,kBAAkB,IAAI,CAAC;mBAC9C7P,UAAU8P,uBAAuB,IAAI,EAAE;aAC5C,EAAEQ,OAAO,CAAC;gBACT,IAAIjN,QAAQC,GAAG,CAACiN,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CxQ;gBAC3D;YACF;YAEA,IAAIC,WAAWwQ,SAAS,EAAE;gBACxBxQ,WAAWwQ,SAAS,CAACV;YACvB,OAAO;gBACLtV,QAAQgW,SAAS,GAAGV;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAI0B,qBAAY,CAACE,QAAQlX;IAClC;AACF;AAcO,MAAML,uBAAsC,CACjDqI,KACA5D,KACAjC,UACAiD,OACAhD,qBACAoD,YACA8K,0BACApQ,aACAkH;IAEA,IAAI,CAACY,IAAIzC,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAIsI,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAMtI,MAAM2S,IAAAA,kCAAgB,EAAClQ,IAAIzC,GAAG,EAAEjF,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAM6P,uBAAuBrQ,oBAAoBkI,IAAIjI,OAAO,EAAE;QAC5DG;QACAU,mBAAmB4E,WAAWsD,YAAY,CAAClI,iBAAiB,KAAK;IACnE;IAEA,MAAM,EAAET,iBAAiB,EAAE,GAAGgQ;IAE9B,MAAMC,oBAAoB;QAAE4B,OAAO;IAAM;IACzC,IAAI3B,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAO7K,WAAW8H,SAAS,KAAK,UAAU;QAC5C,IAAIlL,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAI0H,8BAAc,CACtB,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEAuG,iBAAiB8H,IAAAA,mCAAmB,EAClC3S,WAAW8H,SAAS,EACpB9H,WAAWtD,MAAM;IAErB;IAEA,IACEmO,CAAAA,kCAAAA,eAAgB8G,qBAAqB,KACrC3R,WAAW+F,wBAAwB,EACnC;QACA,MAAM,qBAEL,CAFK,IAAIzB,8BAAc,CACtB,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMY,eAAe0N,IAAAA,6BAAe,EAClC5S,WAAW6S,WAAW,CAACC,UAAU,CAAC5W,IAAI,EACtC6D,KACAnD;IAGF,MAAMkD,YAAYiT,IAAAA,0BAAe,EAAC;QAChC7W,MAAM8D,WAAW6S,WAAW,CAACC,UAAU,CAAC5W,IAAI;QAC5CU;QACAoD;QACA4K;QACA,8CAA8C;QAC9CjQ;QACAkH,SAASD,cAAcC,OAAO;IAChC;IAEA,OAAOmR,0CAAgB,CAAC9P,GAAG,CACzBpD,WACA,sBAAsB;IACtB4K,0BACA,mBAAmB;IACnBlI,KACA5D,KACAmB,KACApD,UACAiD,OACAI,YACAF,WACA6K,sBACAC,mBACAC,gBACA3F,cACA4F,0BACAlJ;AAEJ;AAEA,eAAesQ,eACbzP,YAA0B,EAC1BD,GAAoB,EACpB5D,GAAqB,EACrBH,GAAqB,EACrBqB,SAAoB,EACpBT,IAAgB,EAChB8S,SAAc,EACdtH,cAAqC;IAErC,MAAM7K,aAAavB,IAAIuB,UAAU;IACjC,MAAMoL,eAAepL,WAAWoL,YAAY;IAC5C,4BAA4B;IAC5B,MAAMvH,0BAA0B7D,WAAW6D,uBAAuB;IAElE,MAAM,EAAEuF,0BAA0B,EAAE6J,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAM,EAAE7J,8BAA8B,EAAE8J,yBAAyB,EAAE,GACjEC,IAAAA,0DAA4B,EAAC3U,IAAI9C,KAAK;IAExC,MAAM0X,kBAAkBC,IAAAA,yBAAiB,EACvC1G,IAAAA,iBAAS,IAAG2G,uBAAuB,IACnCvT,WAAWsD,YAAY,CAACkQ,mBAAmB;IAG7C,MAAMC,YACJzT,WAAW0T,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDtW,GAAG,CAAC,CAACqW;YAKO7T;eALO;YAClB+T,KAAK,GAAGtV,IAAIgJ,WAAW,CAAC,OAAO,EAAEoM,WAAWG,IAAAA,wCAAmB,EAC7DvV,KACA,QACC;YACHwV,SAAS,GAAEjU,2CAAAA,WAAWkU,4BAA4B,qBAAvClU,wCAAyC,CAAC6T,SAAS;YAC9DM,aAAanU,WAAWmU,WAAW;YACnCC,UAAU;YACVzY,OAAO8C,IAAI9C,KAAK;QAClB;;IAEJ,MAAM,CAACwN,gBAAgBkL,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DtU,WAAW0T,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9EjV,IAAIgJ,WAAW,EACfzH,WAAWmU,WAAW,EACtBnU,WAAWkU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACvV,KAAK,OACzBA,IAAI9C,KAAK,EACTqE,WAAW9D,IAAI;IAGjB,MAAMqY,4BAAwD,IAAIzK;IAClE,MAAM0K,gBAAgB;IACtB,SAASC,qBAAqB9R,GAAkB;QAC9C,OAAO3C,WAAW4C,6BAA6B,oBAAxC5C,WAAW4C,6BAA6B,MAAxC5C,YACL2C,KACAH,KACAR,mBAAmBvD,KAAK;IAE5B;IACA,MAAMiW,+BAA+BC,IAAAA,qDAAiC,EACpE,CAAC,CAAC3U,WAAW+C,GAAG,EAChB,CAAC,CAAC/C,WAAW4U,UAAU,EACvBL,2BACAC,eACAC;IAGF,SAASI,qBAAqBlS,GAAkB;QAC9C,OAAO3C,WAAW4C,6BAA6B,oBAAxC5C,WAAW4C,6BAA6B,MAAxC5C,YACL2C,KACAH,KACAR,mBAAmBvD,KAAK;IAE5B;IAEA,MAAMqW,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD,CAAC,CAAChV,WAAW+C,GAAG,EAChB,CAAC,CAAC/C,WAAW4U,UAAU,EACvBL,2BACAO,mBACAN,eACAK;IAGF,IAAII,oBAA8C;IAElD,MAAMnE,YAAYlS,IAAIkS,SAAS,CAACoE,IAAI,CAACtW;IACrC,MAAMuW,eAAevW,IAAIuW,YAAY,CAACD,IAAI,CAACtW;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrDoB,WAAW+C,GAAG,IACd,uEAAuE;QACvEI,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAACgJ,YAAY,KAAK,UAC7B,yEAAyE;QACzEpM,WAAWsD,YAAY,CAACC,SAAS,EACjC;YACA,wFAAwF;YACxF,MAAMP,aAGF,MAAMC,kDAAoB,CAACC,GAAG,CAChCT,cACA0D,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAM,CAAC2E,mBAAmBC,iBAAiB,GAAGC;YAC9CV,WAAWW,WAAW,GAAGF;YAEzB,MAAMyF,oBAAoB,MAAMjG,kDAAoB,CAACC,GAAG,CACtDT,cACA2S,+CAAyB,EACzB;gBACE3S,aAAa4S,cAAc,GAAG;gBAC9B,OAAOjK,aAAapH,sBAAsB,CACxChB,YACAa,wBAAwBI,aAAa,EACrC;oBACEpB,SAAS6R;oBACTY,iBAAiB,IACf7S,aAAa4S,cAAc,KAAK,OAAO,cAAc;oBACvDE,kBAAiBxV,GAAW,EAAEyV,aAAqB;wBACjD,kEAAkE;wBAClE,mEAAmE;wBACnE,mEAAmE;wBACnE,OAAO,CAACzV,IAAI0V,UAAU,CAAC,YAAY,CAAC1V,IAAIiH,QAAQ,CAAC;oBACnD;gBACF;YAEJ,GACA;gBACEvE,aAAa4S,cAAc,GAAG;YAChC;YAGFzR,4BACEJ,mBACAnE,MACAZ,KACAG,IAAIC,UAAU,KAAK,KACnBgF,yBACA/D,UAAUgE,KAAK,EACfrB;YAGFwS,oBAAoB,IAAIS,0CAAiB,CAACxM;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMlG,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/CT,cACA0D,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAGrBoW,oBAAoB,IAAIS,0CAAiB,CACvCzS,kDAAoB,CAACC,GAAG,CACtBT,cACA2I,aAAapH,sBAAsB,EACnChB,YACAa,wBAAwBI,aAAa,EACrC;gBACEpB,SAAS6R;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMiB,IAAAA,wCAA6B;QAEnC,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAO3V,WAAW8H,SAAS,KAAK,UAAU;YAC5C,IAAI+C,CAAAA,kCAAAA,eAAgBjN,IAAI,MAAKgY,4BAAY,CAACC,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+BC,IAAAA,kDAA+B,EAClEd,kBAAkBe,GAAG,IACrBvX,IAAI9C,KAAK,EACTwW;gBAGF,OAAO8D,IAAAA,kCAAY,EACjBH,8BACAI,IAAAA,iDAA2B;YAE/B,OAAO,IAAIrL,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAM/C,YAAYqO,IAAAA,qCAAqB,EAACtL;gBAExC,MAAMuL,SAAShM,QAAQ,yBACpBgM,MAAM;gBAET,MAAMC,aAAa,MAAMpT,kDAAoB,CAACC,GAAG,CAC/CT,cACA2T,sBACA,qBAACnN;oBACCC,mBAAmB+L,kBAAkBe,GAAG;oBACxC7M,gBAAgBA;oBAChBtF,yBAAyBA;oBACzBuF,4BAA4BA;oBAC5BC,gCAAgCA;oBAChC1N,OAAO8C,IAAI9C,KAAK;oBAElBmM,WACA;oBACEjF,SAASkS;oBACTpZ,OAAO8C,IAAI9C,KAAK;gBAClB;gBAGF,MAAM2a,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAR;oBACAuD,sBAAsB1B;oBACtB2B,UAAUzW,WAAWyW,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACA,OAAO,MAAMqD,IAAAA,+CAAyB,EAACL,YAAY;oBACjDM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkB2B,OAAO,IACzBnY,IAAI9C,KAAK,EACTwW;oBAEFmE;oBACAnD;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAMnP,yBAAyBoG,QAAQ,yBACpCpG,sBAAsB;QAEzB,MAAMqS,aAAa,MAAMpT,kDAAoB,CAACC,GAAG,CAC/CT,cACAuB,sCACA,qBAACiF;YACCC,mBAAmB+L,kBAAkBe,GAAG;YACxC7M,gBAAgBA;YAChBtF,yBAAyBA;YACzBuF,4BAA4BA;YAC5BC,gCAAgCA;YAChC1N,OAAO8C,IAAI9C,KAAK;YAElB;YACEkH,SAASkS;YACTpZ,OAAO8C,IAAI9C,KAAK;YAChBkb,WAAW,CAACtc;gBACVA,QAAQwN,OAAO,CAAC,CAAC5K,OAAOF;oBACtBkY,aAAalY,KAAKE;gBACpB;YACF;YACA2Z,kBAAkB9W,WAAW+W,qBAAqB;YAClDC,kBAAkB;gBAAC3C;aAAgB;YACnClC;QACF;QAGF,MAAMmE,wBAAwBC,IAAAA,oDAAyB,EAAC;YACtD9C;YACAR;YACAuD,sBAAsB1B;YACtB2B,UAAUzW,WAAWyW,QAAQ;YAC7BpD,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAM4D,qBACJjX,WAAWkX,uBAAuB,KAAK,QACvC,CAAC,CAAClX,WAAWmX,oBAAoB;QAEnC,MAAMC,qBAAqBpX,WAAW+C,GAAG;QACzC,OAAO,MAAMsU,IAAAA,wCAAkB,EAAChB,YAAY;YAC1CM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkB2B,OAAO,IACzBnY,IAAI9C,KAAK,EACTwW;YAEFpQ,oBAAoBkV;YACpBX;YACAnD;YACAiE;QACF;IACF,EAAE,OAAOzU,KAAK;QACZ,IACE2U,IAAAA,gDAAuB,EAAC3U,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIiG,OAAO,KAAK,YACvBjG,IAAIiG,OAAO,CAAC5B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMrE;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4U,qBAAqBC,IAAAA,iCAAmB,EAAC7U;QAC/C,IAAI4U,oBAAoB;YACtB,MAAMvO,QAAQyO,IAAAA,8CAA2B,EAAC9U;YAC1C+U,IAAAA,UAAK,EACH,GAAG/U,IAAIgV,MAAM,CAAC,mDAAmD,EAAElZ,IAAI9B,QAAQ,CAAC,kFAAkF,EAAEqM,OAAO;YAG7K,MAAMrG;QACR;QAEA,IAAI8D;QAEJ,IAAImR,IAAAA,6CAAyB,EAACjV,MAAM;YAClC/D,IAAIC,UAAU,GAAGgZ,IAAAA,+CAA2B,EAAClV;YAC7C8D,YAAYqR,IAAAA,sDAAkC,EAAClZ,IAAIC,UAAU;QAC/D,OAAO,IAAIkZ,IAAAA,8BAAe,EAACpV,MAAM;YAC/B8D,YAAY;YACZ7H,IAAIC,UAAU,GAAGmZ,IAAAA,wCAA8B,EAACrV;YAEhD,MAAMsV,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACxV,MACxB3C,WAAWyW,QAAQ;YAGrB,gEAAgE;YAChE,YAAY;YACZ,MAAMlc,UAAU,IAAI6d;YACpB,IAAIC,IAAAA,oCAAoB,EAAC9d,SAASkI,aAAa6V,cAAc,GAAG;gBAC9DxH,UAAU,cAAcxT,MAAMib,IAAI,CAAChe,QAAQ8U,MAAM;YACnD;YAEAyB,UAAU,YAAYmH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B3Y,IAAIC,UAAU,GAAG;QACnB;QAEA,MAAM,CAAC2Z,qBAAqBC,qBAAqB,GAAGnE,IAAAA,mCAAkB,EACpEtU,WAAW0T,aAAa,EACxBjV,IAAIgJ,WAAW,EACfzH,WAAWmU,WAAW,EACtBnU,WAAWkU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACvV,KAAK,QACzBA,IAAI9C,KAAK,EACT;QAGF,MAAM+c,kBAAkB,MAAMzV,kDAAoB,CAACC,GAAG,CACpDT,cACAwF,oBACA5I,MACAZ,KACA8V,0BAA0BnX,GAAG,CAAC,AAACuF,IAAYmG,MAAM,IAAI,OAAOnG,KAC5D8D;QAGF,MAAMkS,oBAAoB1V,kDAAoB,CAACC,GAAG,CAChDT,cACA2I,aAAapH,sBAAsB,EACnC0U,iBACA7U,wBAAwBI,aAAa,EACrC;YACEpB,SAAS6R;QACX;QAGF,IAAIO,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAMtS;QACR;QAEA,IAAI;YACF,MAAMiW,aAAa,MAAM3V,kDAAoB,CAACC,GAAG,CAC/CT,cACAoW,+CAAyB,EACzB;gBACEC,gBAAgB1O,QAAQ;gBACxB2O,uBACE,qBAACtO;oBACCvB,mBAAmByP;oBACnBxP,gBAAgBqP;oBAChB3U,yBAAyBA;oBACzBlI,OAAO8C,IAAI9C,KAAK;;gBAGpBqd,eAAe;oBACbrd,OAAO8C,IAAI9C,KAAK;oBAChB,wCAAwC;oBACxCqb,kBAAkB;wBAACyB;qBAAqB;oBACxCtG;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAM8E,qBACJjX,WAAWkX,uBAAuB,KAAK,QACvC,CAAC,CAAClX,WAAWmX,oBAAoB;YACnC,MAAMC,qBAAqBpX,WAAW+C,GAAG;YACzC,OAAO,MAAMsU,IAAAA,wCAAkB,EAACuB,YAAY;gBAC1CjC,mBAAmBZ,IAAAA,kDAA+B,EAChD,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACTd,kBAAkB2B,OAAO,IACzBnY,IAAI9C,KAAK,EACTwW;gBAEFpQ,oBAAoBkV;gBACpBX,uBAAuBC,IAAAA,oDAAyB,EAAC;oBAC/C9C;oBACAR;oBACAuD,sBAAsB,EAAE;oBACxBC,UAAUzW,WAAWyW,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACAF;gBACAiE;YACF;QACF,EAAE,OAAO6B,UAAe;YACtB,IACE9V,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuU,IAAAA,6CAAyB,EAACqB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1B9O,QAAQ;gBACV8O;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAASvV;IACP,IAAIF;IACJ,IAAI2V,SAAS,IAAIpJ,QAAyB,CAACqJ;QACzC5V,oBAAoB4V;IACtB;IACA,OAAO;QAAC5V;QAAoB2V;KAAO;AACrC;AAEA,eAAevV,4BACbJ,iBAA+D,EAC/DnE,IAAgB,EAChBZ,GAAqB,EACrB4a,UAAmB,EACnBxV,uBAA2E,EAC3EC,KAAa,EACbrB,YAA0B;IAE1B,MAAM,EAAErD,cAAcgM,YAAY,EAAE,GAAG3M;IACvC,MAAM8F,aAAaC,IAAAA,kCAAa,EAC9B4G,aAAa/L,IAAI,EACjBZ,IAAI5B,0BAA0B;IAGhC,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMyc,mCAAmC,IAAI1U;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAM2U,gCAAgC,IAAI3U;IAE1C,MAAME,cAAc,IAAIC,wBAAW;IACnC,MAAMN,2BAA2BC,IAAAA,+CAA8B;IAC/D,MAAM8U,8BAA8C;QAClD5b,MAAM;QACNqH,OAAO;QACPV;QACAW,cAAc,EAAE;QAChBC,cAAcoU,8BAA8BnU,MAAM;QAClDC,YAAYiU;QACZxU;QACAQ,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMgV,0BAA0B,IAAI7U;IACpC,MAAM8U,8BAA8C;QAClD9b,MAAM;QACNqH,OAAO;QACPV;QACAW,cAAc,EAAE;QAChBC,cAAcsU,wBAAwBrU,MAAM;QAC5CC,YAAYoU;QACZ3U;QACAQ,iBAAiB;QACjBC,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAMkV,yBAAyB,MAAM1W,kDAAoB,CAACC,GAAG,CAC3DsW,6BACArT,eACA9G,MACAZ,KACA4a;IAGF,IAAIO;IACJ,IAAI;QACFA,sBAAsB3W,kDAAoB,CAACC,GAAG,CAC5CsW,6BACApO,aAAapH,sBAAsB,EACnC2V,wBACA9V,wBAAwBI,aAAa,EACrC;YACEpB,SAAS,CAACF;gBACR,MAAMmG,SAAS+Q,IAAAA,8CAA0B,EAAClX;gBAE1C,IAAImG,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IACEwQ,iCAAiClU,MAAM,CAAC0U,OAAO,IAC/CP,8BAA8BnU,MAAM,CAAC0U,OAAO,EAC5C;oBACA,mEAAmE;oBACnE,iEAAiE;oBACjE;gBACF,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;oBACAC,IAAAA,iEAAyC,EAACtX,KAAKmB;gBACjD;YACF;YACAsB,QAAQmU,8BAA8BnU,MAAM;QAC9C;IAEJ,EAAE,OAAOzC,KAAc;QACrB,IACE2W,iCAAiClU,MAAM,CAAC0U,OAAO,IAC/CP,8BAA8BnU,MAAM,CAAC0U,OAAO,EAC5C;QACA,4EAA4E;QAC9E,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFC,IAAAA,iEAAyC,EAACtX,KAAKmB;QACjD;IACF;IAEA,MAAMnI,QAAQ;IACd,MAAM,EAAEyN,0BAA0B,EAAE,GAAG8J,IAAAA,4CAAwB;IAC/D,MAAM,EAAE7J,8BAA8B,EAAE,GAAG+J,IAAAA,0DAA4B,EAACzX;IAExE,IAAIie,qBAAqB;QACvB,MAAM,CAACM,cAAcC,aAAa,GAAGP,oBAAoB5D,GAAG;QAC5D4D,sBAAsB;QACtB,gFAAgF;QAChF,sBAAsB;QACtB,MAAMxf,mBAAmB8f,cAAcrW;QAEvC,MAAMuW,YAAYhQ,QAAQ,yBACvBgQ,SAAS;QACZ,MAAMC,6BAA6BpX,kDAAoB,CAACC,GAAG,CACzDwW,6BACAU,yBACA,qBAACnR;YACCC,mBAAmBiR;YACnBhR,gBAAgB,KAAO;YACvBtF,yBAAyBA;YACzBuF,4BAA4BA;YAC5BC,gCAAgCA;YAChC1N,OAAOA;YAET;YACEyJ,QAAQqU,wBAAwBrU,MAAM;YACtCvC,SAAS,CAACF;gBACR,MAAMmG,SAAS+Q,IAAAA,8CAA0B,EAAClX;gBAE1C,IAAImG,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAI2Q,wBAAwBrU,MAAM,CAAC0U,OAAO,EAAE;gBAC1C,4EAA4E;gBAC9E,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFC,IAAAA,iEAAyC,EAACtX,KAAKmB;gBACjD;YACF;QACF;QAEFuW,2BAA2BC,KAAK,CAAC,CAAC3X;YAChC,IAAI8W,wBAAwBrU,MAAM,CAAC0U,OAAO,EAAE;YAC1C,2DAA2D;YAC7D,OAAO;gBACL,uEAAuE;gBACvE,yCAAyC;gBACzC,IAAI3W,QAAQC,GAAG,CAAC4W,sBAAsB,EAAE;oBACtCC,IAAAA,iEAAyC,EAACtX,KAAKmB;gBACjD;YACF;QACF;IACF;IAEA,MAAMgB,YAAYe,UAAU;IAC5B,8DAA8D;IAC9D,gEAAgE;IAChE4T,wBAAwB3T,KAAK;IAC7ByT,8BAA8BzT,KAAK;IACnCwT,iCAAiCxT,KAAK;IAEtC,sEAAsE;IACtE,kFAAkF;IAElF,MAAMyU,wBAAwB,IAAI3V;IAClC,MAAM4V,wBAAwBC,IAAAA,4CAA0B,EAAC;IAEzD,MAAMC,4BAA4C;QAChD9c,MAAM;QACNqH,OAAO;QACPV;QACAW,cAAc,EAAE;QAChBC,cAAcoV,sBAAsBnV,MAAM;QAC1CC,YAAYkV;QACZ,uFAAuF;QACvFzV,aAAa;QACbQ,iBAAiBkV;QACjBjV,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMkW,wBAAwB,IAAI/V;IAClC,MAAMgW,wBAAwBH,IAAAA,4CAA0B,EAAC;IACzD,MAAMI,oBAAoBC,IAAAA,8CAA4B;IAEtD,MAAMC,4BAA4C;QAChDnd,MAAM;QACNqH,OAAO;QACPV;QACAW,cAAc,EAAE;QAChBC,cAAcwV,sBAAsBvV,MAAM;QAC1CC,YAAYsV;QACZ,uFAAuF;QACvF7V,aAAa;QACbQ,iBAAiBsV;QACjBrV,YAAYC,0BAAc;QAC1BC,QAAQD,0BAAc;QACtBE,OAAOF,0BAAc;QACrBG,MAAM,EAAE;QACRlB;IACF;IAEA,MAAMuW,qBAAqB,MAAM/X,kDAAoB,CAACC,GAAG,CACvDwX,2BACAvU,eACA9G,MACAZ,KACA4a;IAGF,MAAM4B,8BAA8B,MAAMC,IAAAA,kDAAyB,EACjEX,sBAAsBnV,MAAM,EAC5B,IACEnC,kDAAoB,CAACC,GAAG,CACtBwX,2BACAtP,aAAapH,sBAAsB,EACnCgX,oBACAnX,wBAAwBI,aAAa,EACrC;YACEpB,SAAS,CAACF;gBACR,IAAIwY,IAAAA,sCAAsB,EAACxY,MAAM;oBAC/B,OAAOA,IAAImG,MAAM;gBACnB;gBAEA,IACEyR,sBAAsBnV,MAAM,CAAC0U,OAAO,IACpCsB,IAAAA,6CAA2B,EAACzY,MAC5B;oBACA,OAAOA,IAAImG,MAAM;gBACnB;gBAEA,OAAO+Q,IAAAA,8CAA0B,EAAClX;YACpC;YACAyC,QAAQmV,sBAAsBnV,MAAM;QACtC,IAEJ;QACEmV,sBAAsBzU,KAAK;IAC7B;IAGF,IAAIuV,eAAe;IACnB,MAAMC,qBAAqBL,4BAA4BM,cAAc;IACrE,IAAI;QACF,MAAMnB,YAAYhQ,QAAQ,yBACvBgQ,SAAS;QACZ,MAAMoB,IAAAA,kDAAyB,EAC7B,IACEvY,kDAAoB,CAACC,GAAG,CACtB6X,2BACAX,yBACA,qBAACnR;gBACCC,mBAAmBoS;gBACnBnS,gBAAgB,KAAO;gBACvBtF,yBAAyBA;gBACzBuF,4BAA4BA;gBAC5BC,gCAAgCA;gBAChC1N,OAAO8C,IAAI9C,KAAK;gBAElB;gBACEyJ,QAAQuV,sBAAsBvV,MAAM;gBACpCvC,SAAS,CAACF,KAAK8Y;oBACb,IAAIN,IAAAA,sCAAsB,EAACxY,MAAM;wBAC/BkY,kBAAkBa,aAAa,CAACC,IAAI,CAAChZ;wBAErC;oBACF;oBAEA,IACEyY,IAAAA,6CAA2B,EAACzY,QAC5BgY,sBAAsBvV,MAAM,CAAC0U,OAAO,EACpC;wBACA,IAAI,CAACuB,cAAc;4BACjB,+FAA+F;4BAC/F,wGAAwG;4BACxG,+BAA+B;4BAC/B5Y,aAAasP,WAAW,GAAG;wBAC7B;wBAEA,MAAM6J,iBAAiBH,UAAUG,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtCC,IAAAA,2CAAyB,EACvB/X,OACA8X,gBACAf,mBACAL,uBACAI;wBAEJ;wBACA;oBACF;oBAEA,OAAOf,IAAAA,8CAA0B,EAAClX;gBACpC;YACF,IAEJ;YACEgY,sBAAsB7U,KAAK;YAC3BwV,mBAAmBQ,eAAe;QACpC;IAEJ,EAAE,OAAOnZ,KAAK;QACZ0Y,eAAe;QACf,IACED,IAAAA,6CAA2B,EAACzY,QAC5BgY,sBAAsBvV,MAAM,CAAC0U,OAAO,EACpC;QACA,4FAA4F;QAC9F,OAAO;QACL,uEAAuE;QACvE,wEAAwE;QACxE,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACxE;IACF;IAEA,SAASiC;QACP,IAAI;YACFC,IAAAA,0CAAwB,EACtBlY,OACA+W,mBACAL,uBACAI;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;IACT;IAEApX,gCAAkB,qBAACuY;AACrB;AAaA;;CAEC,GACD,SAASE,+BAA+Bnc,SAAoB;IAC1D,MAAM,EAAEiC,kBAAkB,EAAE,GAAGjC;IAC/B,IAAI,CAACiC,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAe4M,kBACbnM,GAAoB,EACpB5D,GAAqB,EACrBH,GAAqB,EACrB6O,QAAqC,EACrCxN,SAAoB,EACpBT,IAAgB,EAChB6F,YAA2B;IAE3B,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMiN,YAAY;IAClB,MAAM5N,aAAaC,IAAAA,kCAAa,EAACnF,MAAMZ,IAAI5B,0BAA0B;IAErE,MAAMmD,aAAavB,IAAIuB,UAAU;IACjC,MAAMoL,eAAepL,WAAWoL,YAAY;IAC5C,4BAA4B;IAC5B,MAAMvH,0BAA0B7D,WAAW6D,uBAAuB;IAClE,MAAMjH,sBAAsBkD,UAAUlD,mBAAmB;IAEzD,MAAM,EAAEwM,0BAA0B,EAAE6J,wBAAwB,EAAE,GAC5DC,IAAAA,4CAAwB;IAC1B,MAAM,EAAE7J,8BAA8B,EAAE8J,yBAAyB,EAAE,GACjEC,IAAAA,0DAA4B,EAAC3U,IAAI9C,KAAK;IAExC,MAAM0X,kBAAkBC,IAAAA,yBAAiB,EACvC1G,IAAAA,iBAAS,IAAG2G,uBAAuB,IACnCvT,WAAWsD,YAAY,CAACkQ,mBAAmB;IAG7C,MAAMC,YACJzT,WAAW0T,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDtW,GAAG,CAAC,CAACqW;YAKO7T;eALO;YAClB+T,KAAK,GAAGtV,IAAIgJ,WAAW,CAAC,OAAO,EAAEoM,WAAWG,IAAAA,wCAAmB,EAC7DvV,KACA,QACC;YACHwV,SAAS,GAAEjU,2CAAAA,WAAWkU,4BAA4B,qBAAvClU,wCAAyC,CAAC6T,SAAS;YAC9DM,aAAanU,WAAWmU,WAAW;YACnCC,UAAU;YACVzY,OAAO8C,IAAI9C,KAAK;QAClB;;IAEJ,MAAM,CAACwN,gBAAgBkL,gBAAgB,GAAGC,IAAAA,mCAAkB,EAC1DtU,WAAW0T,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9EjV,IAAIgJ,WAAW,EACfzH,WAAWmU,WAAW,EACtBnU,WAAWkU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACvV,KAAK,OACzBA,IAAI9C,KAAK,EACTqE,WAAW9D,IAAI;IAGjB,MAAMqY,4BAAwD,IAAIzK;IAClE,+EAA+E;IAC/E,MAAM0K,gBAAgB,CAAC,CAACxU,WAAWsD,YAAY,CAAClI,iBAAiB;IACjE,SAASqZ,qBAAqB9R,GAAkB;QAC9C,OAAO3C,WAAW4C,6BAA6B,oBAAxC5C,WAAW4C,6BAA6B,MAAxC5C,YACL2C,KACAH,KACAR,mBAAmBvD,KAAK;IAE5B;IACA,MAAMiW,+BAA+BC,IAAAA,qDAAiC,EACpE,CAAC,CAAC3U,WAAW+C,GAAG,EAChB,CAAC,CAAC/C,WAAW4U,UAAU,EACvBL,2BACAC,eACAC;IAGF,SAASI,qBAAqBlS,GAAkB;QAC9C,OAAO3C,WAAW4C,6BAA6B,oBAAxC5C,WAAW4C,6BAA6B,MAAxC5C,YACL2C,KACAH,KACAR,mBAAmBvD,KAAK;IAE5B;IACA,MAAMqW,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2BC,IAAAA,0CAAsB,EACrD,CAAC,CAAChV,WAAW+C,GAAG,EAChB,CAAC,CAAC/C,WAAW4U,UAAU,EACvBL,2BACAO,mBACAN,eACAK;IAGF,IAAIqH,6BAG8B;IAClC,MAAMC,oBAAoB,CAACnd;QACzBsO,SAAS/S,OAAO,KAAK,CAAC;QACtB+S,SAAS/S,OAAO,CAACyE,KAAK,GAAGJ,IAAIkI,SAAS,CAAC9H;IACzC;IACA,MAAM8R,YAAY,CAAC9R,MAAc7B;QAC/ByB,IAAIkS,SAAS,CAAC9R,MAAM7B;QACpBgf,kBAAkBnd;QAClB,OAAOJ;IACT;IACA,MAAMuW,eAAe,CAACnW,MAAc7B;QAClC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxBA,MAAM4K,OAAO,CAAC,CAACqU;gBACbxd,IAAIuW,YAAY,CAACnW,MAAMod;YACzB;QACF,OAAO;YACLxd,IAAIuW,YAAY,CAACnW,MAAM7B;QACzB;QACAgf,kBAAkBnd;IACpB;IAEA,IAAIgG,iBAAwC;IAE5C,IAAI;QACF,IAAIhF,WAAWsD,YAAY,CAACC,SAAS,EAAE;YACrC,IAAIvD,WAAWsD,YAAY,CAAClI,iBAAiB,EAAE;gBAC7C;;;;;;;;;;;;SAYC,GAED,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAMke,mCAAmC,IAAI1U;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAM2U,gCAAgC,IAAI3U;gBAE1C,kFAAkF;gBAClF,yBAAyB;gBACzB,MAAME,cAAc,IAAIC,wBAAW;gBAEnC,iEAAiE;gBACjE,8DAA8D;gBAC9D,wEAAwE;gBACxE,6BAA6B;gBAC7B,MAAMN,2BAA2BC,IAAAA,+CAA8B;gBAE/D,MAAM8U,8BAA+CxU,iBAAiB;oBACpEpH,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcoU,8BAA8BnU,MAAM;oBAClDC,YAAYiU;oBACZxU;oBACAQ,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAM4X,uBAAuB,MAAMpZ,kDAAoB,CAACC,GAAG,CACzDsW,6BACArT,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAGrB,MAAMyd,6BAA6BrZ,kDAAoB,CAACC,GAAG,CACzDsW,6BACApO,aAAagP,SAAS,EACtBiC,sBACAxY,wBAAwBI,aAAa,EACrC;oBACEpB,SAAS,CAACF;wBACR,MAAMmG,SAAS+Q,IAAAA,8CAA0B,EAAClX;wBAE1C,IAAImG,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAIwQ,iCAAiClU,MAAM,CAAC0U,OAAO,EAAE;4BACnD,mEAAmE;4BACnE,iEAAiE;4BACjE;wBACF,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;4BACAC,IAAAA,iEAAyC,EAACtX,KAAK7C,UAAUgE,KAAK;wBAChE;oBACF;oBACA,iFAAiF;oBACjF,qCAAqC;oBACrCyY,YAAYzhB;oBACZ,+EAA+E;oBAC/E,iFAAiF;oBACjF,iDAAiD;oBACjDsK,QAAQmU,8BAA8BnU,MAAM;gBAC9C;gBAGF,MAAMN,YAAYe,UAAU;gBAC5B0T,8BAA8BzT,KAAK;gBACnCwT,iCAAiCxT,KAAK;gBAEtC,IAAI0W;gBACJ,IAAI;oBACFA,sBAAsB,MAAMC,IAAAA,yDAAgC,EAC1DH;gBAEJ,EAAE,OAAO3Z,KAAK;oBACZ,IACE4W,8BAA8BnU,MAAM,CAAC0U,OAAO,IAC5CR,iCAAiClU,MAAM,CAAC0U,OAAO,EAC/C;oBACA,4EAA4E;oBAC9E,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACtX,KAAK7C,UAAUgE,KAAK;oBAChE;gBACF;gBAEA,IAAI0Y,qBAAqB;oBACvB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMpiB,mBACJoiB,oBAAoBE,QAAQ,IAC5B7Y;oBAGF,MAAM4V,0BAA0B,IAAI7U;oBACpC,MAAM8U,8BAA8C;wBAClD9b,MAAM;wBACNqH,OAAO;wBACPV;wBACAW,cAAcA;wBACdC,cAAcsU,wBAAwBrU,MAAM;wBAC5CC,YAAYoU;wBACZ3U,aAAa;wBACbQ,iBAAiB;wBACjBC,YAAYC,0BAAc;wBAC1BC,QAAQD,0BAAc;wBACtBE,OAAOF,0BAAc;wBACrBG,MAAM;+BAAIT;yBAAa;wBACvBT;oBACF;oBAEA,MAAM2V,YAAYhQ,QAAQ,yBACvBgQ,SAAS;oBACZ,MAAMuC,IAAAA,2DAAkC,EACtC,IACE1Z,kDAAoB,CAACC,GAAG,CACtBwW,6BACAU,yBACA,qBAACnR;4BACCC,mBAAmBsT,oBAAoBI,iBAAiB;4BACxDzT,gBAAgBA;4BAChBtF,yBAAyBA;4BACzBuF,4BAA4BA;4BAC5BC,gCACEA;4BAEF1N,OAAO8C,IAAI9C,KAAK;4BAElB;4BACEyJ,QAAQqU,wBAAwBrU,MAAM;4BACtCvC,SAAS,CAACF;gCACR,MAAMmG,SAAS+Q,IAAAA,8CAA0B,EAAClX;gCAE1C,IAAImG,QAAQ;oCACV,OAAOA;gCACT;gCAEA,IAAI2Q,wBAAwBrU,MAAM,CAAC0U,OAAO,EAAE;gCAC1C,4EAA4E;gCAC9E,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;oCACA,8EAA8E;oCAC9E,mFAAmF;oCACnFC,IAAAA,iEAAyC,EACvCtX,KACA7C,UAAUgE,KAAK;gCAEnB;4BACF;4BACAkT,kBAAkB;gCAAC3C;6BAAgB;wBACrC,IAEJ;wBACEoF,wBAAwB3T,KAAK;oBAC/B,GACAwU,KAAK,CAAC,CAAC3X;wBACP,IACE4W,8BAA8BnU,MAAM,CAAC0U,OAAO,IAC5CsB,IAAAA,6CAA2B,EAACzY,MAC5B;wBACA,4EAA4E;wBAC9E,OAAO,IACLQ,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFC,IAAAA,iEAAyC,EAACtX,KAAK7C,UAAUgE,KAAK;wBAChE;oBACF;gBACF;gBAEA,IAAI+Y,kBAAkB;gBACtB,MAAMtC,wBAAwB,IAAI3V;gBAClC,MAAM4V,wBAAwBC,IAAAA,4CAA0B,EACtDza,WAAW8O,sBAAsB;gBAGnC,MAAMgO,4BAA6C9X,iBAAiB;oBAClEpH,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcoV,sBAAsBnV,MAAM;oBAC1CC,YAAYkV;oBACZ,uFAAuF;oBACvFzV,aAAa;oBACbQ,iBAAiBkV;oBACjBjV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,MAAMsY,yBAAyB,MAAM9Z,kDAAoB,CAACC,GAAG,CAC3D4Z,2BACA3W,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAErB,IAAIme,qBAAqB;gBACzB,MAAM/H,oBAAqBiH,6BACzB,MAAMO,IAAAA,yDAAgC,EACpCE,IAAAA,2DAAkC,EAChC;oBACE,MAAMM,kBAAkB,MAAMha,kDAAoB,CAACC,GAAG,CACpD,qBAAqB;oBACrB4Z,2BACA,sBAAsB;oBACtB1R,aAAagP,SAAS,EACtB,4CAA4C;oBAC5C2C,wBACAlZ,wBAAwBI,aAAa,EACrC;wBACEpB,SAAS,CAACF;4BACR,OAAO+R,6BAA6B/R;wBACtC;wBACAyC,QAAQmV,sBAAsBnV,MAAM;oBACtC;oBAEF4X,qBAAqB;oBACrB,OAAOC;gBACT,GACA;oBACE,IAAI1C,sBAAsBnV,MAAM,CAAC0U,OAAO,EAAE;wBACxC,4EAA4E;wBAC5E,6EAA6E;wBAC7E+C,kBAAkB;wBAClB;oBACF;oBAEA,IAAIG,oBAAoB;wBACtB,kFAAkF;wBAClF,iCAAiC;wBACjCH,kBAAkB;oBACpB;oBACAtC,sBAAsBzU,KAAK;gBAC7B;gBAIN,MAAM8U,wBAAwBH,IAAAA,4CAA0B,EACtDza,WAAW8O,sBAAsB;gBAEnC,MAAM6L,wBAAwB,IAAI/V;gBAClC,MAAMmW,4BAA4C;oBAChDnd,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcwV,sBAAsBvV,MAAM;oBAC1CC,YAAYsV;oBACZ,oEAAoE;oBACpE7V,aAAa;oBACbQ,iBAAiBsV;oBACjBrV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,IAAIyY,kBAAkB;gBACtB,IAAIrC,oBAAoBC,IAAAA,8CAA4B;gBAEpD,MAAMV,YAAYhQ,QAAQ,yBACvBgQ,SAAS;gBACZ,IAAI,EAAE+C,OAAO,EAAErV,SAAS,EAAE,GAAG,MAAM6U,IAAAA,2DAAkC,EACnE,IACE1Z,kDAAoB,CAACC,GAAG,CACtB6X,2BACAX,yBACA,qBAACnR;wBACCC,mBAAmB+L,kBAAkB2H,iBAAiB;wBACtDzT,gBAAgBA;wBAChBtF,yBAAyBA;wBACzBuF,4BAA4BA;wBAC5BC,gCAAgCA;wBAChC1N,OAAO8C,IAAI9C,KAAK;wBAElB;wBACEyJ,QAAQuV,sBAAsBvV,MAAM;wBACpCvC,SAAS,CAACF,KAAc8Y;4BACtB,IACEL,IAAAA,6CAA2B,EAACzY,QAC5BgY,sBAAsBvV,MAAM,CAAC0U,OAAO,EACpC;gCACAoD,kBAAkB;gCAElB,MAAMtB,iBAAqC,AACzCH,UACAG,cAAc;gCAChB,IAAI,OAAOA,mBAAmB,UAAU;oCACtCC,IAAAA,2CAAyB,EACvB/b,UAAUgE,KAAK,EACf8X,gBACAf,mBACAL,uBACAI;gCAEJ;gCACA;4BACF;4BAEA,OAAO7F,yBAAyBpS,KAAK8Y;wBACvC;wBACA5E,WAAW,CAACtc;4BACVA,QAAQwN,OAAO,CAAC,CAAC5K,OAAOF;gCACtBkY,aAAalY,KAAKE;4BACpB;wBACF;wBACA2Z,kBAAkB9W,WAAW+W,qBAAqB;wBAClDC,kBAAkB;4BAAC3C;yBAAgB;oBACrC,IAEJ;oBACEsG,sBAAsB7U,KAAK;gBAC7B;gBAGFkW,IAAAA,0CAAwB,EACtBlc,UAAUgE,KAAK,EACf+W,mBACAL,uBACAI;gBAGF,MAAMtE,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAR;oBACAuD,sBAAsB1B;oBACtB2B,UAAUzW,WAAWyW,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBAEA,MAAMlU,aAAa,MAAMie,IAAAA,oCAAc,EAACnI,kBAAkByH,QAAQ;gBAClEpP,SAASnO,UAAU,GAAGA;gBACtBmO,SAAS+P,WAAW,GAAG,MAAMC,mBAC3Bne,YACA2d,2BACA1R,cACApL,YACApD;gBAGF,IAAIigB,mBAAmBK,iBAAiB;oBACtC,IAAIpV,aAAa,MAAM;wBACrB,oBAAoB;wBACpBwF,SAASxF,SAAS,GAAG,MAAMyV,IAAAA,4CAA4B,EACrDzV,WACAlL,qBACA6H;oBAEJ,OAAO;wBACL,oBAAoB;wBACpB6I,SAASxF,SAAS,GAAG,MAAM0V,IAAAA,4CAA4B,EACrD/Y;oBAEJ;oBACAwQ,kBAAkB2B,OAAO;oBACzB,OAAO;wBACL1H,iBAAiBqF;wBACjBhF,WAAWuF;wBACXpD,QAAQ,MAAM+L,IAAAA,8CAAwB,EAACN,SAAS;4BAC9C7G;4BACAnD;wBACF;wBACAvE,eAAe8O,IAAAA,sCAAoB,EACjClD,uBACAI;wBAEF,0CAA0C;wBAC1C3J,qBAAqB6L,0BAA0BvX,UAAU;wBACzD4L,iBAAiB2L,0BAA0BrX,MAAM;wBACjDoL,gBAAgBiM,0BAA0BpX,KAAK;wBAC/C+K,eAAeqM,0BAA0BnX,IAAI;oBAC/C;gBACF,OAAO;oBACL,cAAc;oBACd,IAAI7F,UAAUkS,YAAY,EAAE;wBAC1B,MAAM,qBAEL,CAFK,IAAI2L,8CAAqB,CAC7B,qHADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAItH,aAAa8G;oBACjB,IAAIrV,aAAa,MAAM;wBACrB,+FAA+F;wBAC/F,qGAAqG;wBACrG,MAAMsO,SAAShM,QAAQ,yBACpBgM,MAAM;wBAET,qEAAqE;wBACrE,4EAA4E;wBAC5E,MAAMwH,gBAAgB,IAAIC;wBAE1B,MAAMC,eAAe,MAAM1H,qBACzB,qBAACnN;4BACCC,mBAAmB0U;4BACnBzU,gBAAgB,KAAO;4BACvBtF,yBAAyBA;4BACzBuF,4BAA4BA;4BAC5BC,gCAAgCA;4BAChC1N,OAAO8C,IAAI9C,KAAK;4BAElBoiB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACnW,aAC1B;4BACE1C,QAAQ8Y,IAAAA,4CAA0B,EAAC;4BACnCrb,SAASkS;4BACTpZ,OAAO8C,IAAI9C,KAAK;wBAClB;wBAGF,wGAAwG;wBACxG0a,aAAaJ,IAAAA,kCAAY,EAACkH,SAASW;oBACrC;oBAEA,OAAO;wBACL5O,iBAAiBqF;wBACjBhF,WAAWuF;wBACXpD,QAAQ,MAAMyM,IAAAA,6CAAuB,EAAC9H,YAAY;4BAChDM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkBmJ,eAAe,IACjC3f,IAAI9C,KAAK,EACTwW;4BAEFmE;4BACAnD;wBACF;wBACAvE,eAAe8O,IAAAA,sCAAoB,EACjClD,uBACAI;wBAEF,0CAA0C;wBAC1C3J,qBAAqB6L,0BAA0BvX,UAAU;wBACzD4L,iBAAiB2L,0BAA0BrX,MAAM;wBACjDoL,gBAAgBiM,0BAA0BpX,KAAK;wBAC/C+K,eAAeqM,0BAA0BnX,IAAI;oBAC/C;gBACF;YACF,OAAO;gBACL;;;;;;;;;;;;;;;;SAgBC,GAED,MAAM0Y,QAAQve,UAAUmQ,gBAAgB;gBACxC,IAAI,CAACoO,OAAO;oBACV,MAAM,qBAEL,CAFK,IAAIhW,MACR,kEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAMiR,mCAAmC,IAAI1U;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAM2U,gCAAgC,IAAI3U;gBAE1C,MAAME,cAAc,IAAIC,wBAAW;gBACnC,MAAMN,2BAA2BC,IAAAA,+CAA8B;gBAE/D,MAAM8U,8BAA+CxU,iBAAiB;oBACpEpH,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcoU,8BAA8BnU,MAAM;oBAClDC,YAAYiU;oBACZxU;oBACAQ,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,MAAMgV,0BAA0B,IAAI7U;gBACpC,MAAM8U,8BAA+C1U,iBAAiB;oBACpEpH,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcsU,wBAAwBrU,MAAM;oBAC5CC,YAAYoU;oBACZ3U;oBACAQ,iBAAiB;oBACjBC,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMkV,yBAAyB,MAAM1W,kDAAoB,CAACC,GAAG,CAC3DsW,6BACArT,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAGrB,IAAI+a;gBACJ,IAAI;oBACFA,sBAAsB3W,kDAAoB,CAACC,GAAG,CAC5CsW,6BACApO,aAAapH,sBAAsB,EACnC2V,wBACA9V,wBAAwBI,aAAa,EACrC;wBACEpB,SAAS,CAACF;4BACR,MAAMmG,SAAS+Q,IAAAA,8CAA0B,EAAClX;4BAE1C,IAAImG,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IACEwQ,iCAAiClU,MAAM,CAAC0U,OAAO,IAC/CP,8BAA8BnU,MAAM,CAAC0U,OAAO,EAC5C;gCACA,mEAAmE;gCACnE,iEAAiE;gCACjE;4BACF,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;gCACAC,IAAAA,iEAAyC,EACvCtX,KACA7C,UAAUgE,KAAK;4BAEnB;wBACF;wBACAsB,QAAQmU,8BAA8BnU,MAAM;oBAC9C;gBAEJ,EAAE,OAAOzC,KAAc;oBACrB,IACE2W,iCAAiClU,MAAM,CAAC0U,OAAO,IAC/CP,8BAA8BnU,MAAM,CAAC0U,OAAO,EAC5C;oBACA,4EAA4E;oBAC9E,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFC,IAAAA,iEAAyC,EAACtX,KAAK7C,UAAUgE,KAAK;oBAChE;gBACF;gBAEA,IAAI8V,qBAAqB;oBACvB,MAAM,CAACM,cAAcC,aAAa,GAAGP,oBAAoB5D,GAAG;oBAC5D4D,sBAAsB;oBACtB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMxf,mBAAmB8f,cAAcrW;oBAEvC,MAAMuW,YAAYhQ,QAAQ,yBACvBgQ,SAAS;oBACZ,MAAMC,6BAA6BpX,kDAAoB,CAACC,GAAG,CACzDwW,6BACAU,yBACA,qBAACnR;wBACCC,mBAAmBiR;wBACnBhR,gBAAgBA;wBAChBtF,yBAAyBA;wBACzBuF,4BAA4BA;wBAC5BC,gCAAgCA;wBAChC1N,OAAO8C,IAAI9C,KAAK;wBAElB;wBACEyJ,QAAQqU,wBAAwBrU,MAAM;wBACtCvC,SAAS,CAACF;4BACR,MAAMmG,SAAS+Q,IAAAA,8CAA0B,EAAClX;4BAE1C,IAAImG,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IAAI2Q,wBAAwBrU,MAAM,CAAC0U,OAAO,EAAE;4BAC1C,4EAA4E;4BAC9E,OAAO,IACL3W,QAAQC,GAAG,CAAC2W,gBAAgB,IAC5B5W,QAAQC,GAAG,CAAC4W,sBAAsB,EAClC;gCACA,8EAA8E;gCAC9E,mFAAmF;gCACnFC,IAAAA,iEAAyC,EACvCtX,KACA7C,UAAUgE,KAAK;4BAEnB;wBACF;wBACAkT,kBAAkB;4BAAC3C;yBAAgB;oBACrC;oBAEFgG,2BAA2BC,KAAK,CAAC,CAAC3X;wBAChC,IAAI8W,wBAAwBrU,MAAM,CAAC0U,OAAO,EAAE;wBAC1C,2DAA2D;wBAC7D,OAAO;4BACL,uEAAuE;4BACvE,yCAAyC;4BACzC,IAAI3W,QAAQC,GAAG,CAAC4W,sBAAsB,EAAE;gCACtCC,IAAAA,iEAAyC,EAACtX,KAAK7C,UAAUgE,KAAK;4BAChE;wBACF;oBACF;gBACF;gBAEA,MAAMgB,YAAYe,UAAU;gBAC5B,8DAA8D;gBAC9D,gEAAgE;gBAChE4T,wBAAwB3T,KAAK;gBAC7ByT,8BAA8BzT,KAAK;gBACnCwT,iCAAiCxT,KAAK;gBAEtC,sEAAsE;gBACtE,kFAAkF;gBAElF,IAAI+W,kBAAkB;gBACtB,MAAMtC,wBAAwB,IAAI3V;gBAClC,MAAM4V,wBAAwBC,IAAAA,4CAA0B,EACtDza,WAAW8O,sBAAsB;gBAGnC,MAAM4L,4BAA6C1V,iBAAiB;oBAClEpH,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcoV,sBAAsBnV,MAAM;oBAC1CC,YAAYkV;oBACZ,uFAAuF;oBACvFzV,aAAa;oBACbQ,iBAAiBkV;oBACjBjV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,IAAIyY,kBAAkB;gBACtB,MAAMvC,wBAAwB,IAAI/V;gBAClC,MAAMgW,wBAAwBH,IAAAA,4CAA0B,EACtDza,WAAW8O,sBAAsB;gBAEnC,MAAM+L,oBAAoBC,IAAAA,8CAA4B;gBAEtD,MAAMC,4BAA6C/V,iBAAiB;oBAClEpH,MAAM;oBACNqH,OAAO;oBACPV;oBACAW,cAAcA;oBACdC,cAAcwV,sBAAsBvV,MAAM;oBAC1CC,YAAYsV;oBACZ,uFAAuF;oBACvF7V,aAAa;oBACbQ,iBAAiBsV;oBACjBrV,YAAYC,0BAAc;oBAC1BC,QAAQD,0BAAc;oBACtBE,OAAOF,0BAAc;oBACrBG,MAAM;2BAAIT;qBAAa;oBACvBT;gBACF;gBAEA,MAAMuW,qBAAqB,MAAM/X,kDAAoB,CAACC,GAAG,CACvDwX,2BACAvU,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAGrB,MAAMoc,8BAA+BiB,6BACnC,MAAMhB,IAAAA,kDAAyB,EAC7BX,sBAAsBnV,MAAM,EAC5B,IACEnC,kDAAoB,CAACC,GAAG,CACtBwX,2BACAtP,aAAapH,sBAAsB,EACnCgX,oBACAnX,wBAAwBI,aAAa,EACrC;wBACEpB,SAAS,CAACF;4BACR,IAAI4X,sBAAsBnV,MAAM,CAAC0U,OAAO,EAAE;gCACxC+C,kBAAkB;gCAClB,IAAIzB,IAAAA,6CAA2B,EAACzY,MAAM;oCACpC,OAAOA,IAAImG,MAAM;gCACnB;gCACA,OAAO+Q,IAAAA,8CAA0B,EAAClX;4BACpC;4BAEA,OAAO+R,6BAA6B/R;wBACtC;wBACAyC,QAAQmV,sBAAsBnV,MAAM;oBACtC,IAEJ;oBACEmV,sBAAsBzU,KAAK;gBAC7B;gBAGJ,IAAIuQ;gBACJ,MAAMiF,qBAAqBL,4BAA4BM,cAAc;gBACrE,IAAI;oBACF,MAAMnB,YAAYhQ,QAAQ,yBACvBgQ,SAAS;oBACZ,MAAM5H,SAAS,MAAMgJ,IAAAA,kDAAyB,EAC5C,IACEvY,kDAAoB,CAACC,GAAG,CACtB6X,2BACAX,yBACA,qBAACnR;4BACCC,mBAAmBoS;4BACnBnS,gBAAgBA;4BAChBtF,yBAAyBA;4BACzBuF,4BAA4BA;4BAC5BC,gCACEA;4BAEF1N,OAAO8C,IAAI9C,KAAK;4BAElB;4BACEyJ,QAAQuV,sBAAsBvV,MAAM;4BACpCvC,SAAS,CAACF,KAAc8Y;gCACtB,IACEL,IAAAA,6CAA2B,EAACzY,QAC5BgY,sBAAsBvV,MAAM,CAAC0U,OAAO,EACpC;oCACAoD,kBAAkB;oCAElB,MAAMtB,iBAAqC,AACzCH,UACAG,cAAc;oCAChB,IAAI,OAAOA,mBAAmB,UAAU;wCACtCC,IAAAA,2CAAyB,EACvB/b,UAAUgE,KAAK,EACf8X,gBACAf,mBACAL,uBACAI;oCAEJ;oCACA;gCACF;gCAEA,OAAO7F,yBAAyBpS,KAAK8Y;4BACvC;4BACAzE,kBAAkB;gCAAC3C;6BAAgB;wBACrC,IAEJ;wBACEsG,sBAAsB7U,KAAK;wBAC3BwV,mBAAmBQ,eAAe;oBACpC;oBAEFzF,aAAa7D,OAAO2K,OAAO;gBAC7B,EAAE,OAAOxa,KAAK;oBACZ,IACEyY,IAAAA,6CAA2B,EAACzY,QAC5BgY,sBAAsBvV,MAAM,CAAC0U,OAAO,EACpC;oBACA,4FAA4F;oBAC9F,OAAO;wBACL,oDAAoD;wBACpD,MAAMnX;oBACR;gBACF;gBAEAqZ,IAAAA,0CAAwB,EACtBlc,UAAUgE,KAAK,EACf+W,mBACAL,uBACAI;gBAGF,IAAIiC,mBAAmBK,iBAAiB;oBACtC,MAAMoB,gBAAgBzB,kBAClB0B,IAAAA,uCAAqB,EAAC/D,yBACtB+D,IAAAA,uCAAqB,EAAC3D;oBAC1B,IAAI0D,eAAe;wBACjB,MAAM,qBAEL,CAFK,IAAIE,sCAAkB,CAC1B,CAAC,OAAO,EAAE1e,UAAUgE,KAAK,CAAC,oDAAoD,EAAEwa,cAAc,4EAA4E,CAAC,GADvK,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAIE,sCAAkB,CAC1B,CAAC,OAAO,EAAE1e,UAAUgE,KAAK,CAAC,0JAA0J,CAAC,GADjL,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,MAAM3E,aAAa,MAAMie,IAAAA,oCAAc,EACrCnC,4BAA4ByB,QAAQ;gBAEtCpP,SAASnO,UAAU,GAAGA;gBACtBmO,SAAS+P,WAAW,GAAG,MAAMC,mBAC3Bne,YACA4b,2BACA3P,cACApL,YACApD;gBAGF,MAAM0Z,wBAAwBC,IAAAA,oDAAyB,EAAC;oBACtD9C;oBACAR;oBACAuD,sBAAsB1B;oBACtB2B,UAAUzW,WAAWyW,QAAQ;oBAC7BpD,iBAAiBA;gBACnB;gBACA,MAAM+D,qBAAqBpX,WAAW+C,GAAG;gBACzC,OAAO;oBACLmM,iBAAiBqF;oBACjBhF,WAAWuF;oBACXpD,QAAQ,MAAM2F,IAAAA,wCAAkB,EAAChB,YAAa;wBAC5CM,mBAAmBZ,IAAAA,kDAA+B,EAChDkF,4BAA4ByB,QAAQ,IACpCje,IAAI9C,KAAK,EACTwW;wBAEFpQ,oBAAoB;wBACpBuU;wBACAnD;wBACAiE;oBACF;oBACAxI,eAAe8O,IAAAA,sCAAoB,EACjClD,uBACAI;oBAEF,0CAA0C;oBAC1C3J,qBAAqByJ,0BAA0BnV,UAAU;oBACzD4L,iBAAiBuJ,0BAA0BjV,MAAM;oBACjDoL,gBAAgB6J,0BAA0BhV,KAAK;oBAC/C+K,eAAeiK,0BAA0B/U,IAAI;gBAC/C;YACF;QACF,OAAO,IAAI3F,WAAWsD,YAAY,CAAClI,iBAAiB,EAAE;YACpD,uEAAuE;YACvE,IAAIkK,kBAAkBmV,IAAAA,4CAA0B,EAC9Cza,WAAW8O,sBAAsB;YAGnC,MAAMrK,2BAA2BC,IAAAA,+CAA8B;YAC/D,MAAM+Z,4BAA6CzZ,iBAAiB;gBAClEpH,MAAM;gBACNqH,OAAO;gBACPV;gBACAW,cAAcA;gBACdI;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIT;iBAAa;gBACvBT;YACF;YACA,MAAMzB,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/Cub,2BACAtY,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAMoW,oBAAqBiH,6BACzB,MAAMwC,IAAAA,mEAA0C,EAC9Czb,kDAAoB,CAACC,GAAG,CACtBub,2BACArT,aAAapH,sBAAsB,EACnC,4CAA4C;YAC5ChB,YACAa,wBAAwBI,aAAa,EACrC;gBACEpB,SAAS6R;YACX;YAIN,MAAMiK,oBAAoC;gBACxC/gB,MAAM;gBACNqH,OAAO;gBACPV;gBACAW,cAAcA;gBACdI;gBACAC,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIT;iBAAa;gBACvBT;YACF;YACA,MAAM2V,YAAYhQ,QAAQ,yBACvBgQ,SAAS;YACZ,MAAM,EAAE+C,OAAO,EAAErV,SAAS,EAAE,GAAG,MAAM7E,kDAAoB,CAACC,GAAG,CAC3Dyb,mBACAvE,yBACA,qBAACnR;gBACCC,mBAAmB+L,kBAAkB2H,iBAAiB;gBACtDzT,gBAAgBA;gBAChBtF,yBAAyBA;gBACzBuF,4BAA4BA;gBAC5BC,gCAAgCA;gBAChC1N,OAAO8C,IAAI9C,KAAK;gBAElB;gBACEkH,SAASkS;gBACT8B,WAAW,CAACtc;oBACVA,QAAQwN,OAAO,CAAC,CAAC5K,OAAOF;wBACtBkY,aAAalY,KAAKE;oBACpB;gBACF;gBACA2Z,kBAAkB9W,WAAW+W,qBAAqB;gBAClDC,kBAAkB;oBAAC3C;iBAAgB;YACrC;YAEF,MAAMiC,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD9C;gBACAR;gBACAuD,sBAAsB1B;gBACtB2B,UAAUzW,WAAWyW,QAAQ;gBAC7BpD,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMlU,aAAa,MAAMie,IAAAA,oCAAc,EAACnI,kBAAkByH,QAAQ;YAElE,IAAIT,+BAA+Bnc,YAAY;gBAC7CwN,SAASnO,UAAU,GAAGA;gBACtBmO,SAAS+P,WAAW,GAAG,MAAMC,mBAC3Bne,YACAwf,mBACAvT,cACApL,YACApD;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAIiS,IAAAA,qCAAmB,EAACvJ,gBAAgBsZ,eAAe,GAAG;gBACxD,IAAI9W,aAAa,MAAM;oBACrB,qBAAqB;oBACrBwF,SAASxF,SAAS,GAAG,MAAMyV,IAAAA,4CAA4B,EACrDzV,WACAlL,qBACA6H;gBAEJ,OAAO;oBACL,qBAAqB;oBACrB6I,SAASxF,SAAS,GAAG,MAAM0V,IAAAA,4CAA4B,EACrD/Y;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtDwQ,kBAAkB2B,OAAO;gBACzB,OAAO;oBACL1H,iBAAiBqF;oBACjBhF,WAAWuF;oBACXpD,QAAQ,MAAM+L,IAAAA,8CAAwB,EAACN,SAAS;wBAC9C7G;wBACAnD;oBACF;oBACAvE,eAAetJ,gBAAgBsZ,eAAe;oBAC9C,0CAA0C;oBAC1C3N,qBAAqBwN,0BAA0BlZ,UAAU;oBACzD4L,iBAAiBsN,0BAA0BhZ,MAAM;oBACjDoL,gBAAgB4N,0BAA0B/Y,KAAK;oBAC/C+K,eAAegO,0BAA0B9Y,IAAI;gBAC/C;YACF,OAAO,IAAI/I,uBAAuBA,oBAAoBuS,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/B7B,SAASxF,SAAS,GAAG,MAAM0V,IAAAA,4CAA4B,EACrD/Y;gBAGF,OAAO;oBACLyK,iBAAiBqF;oBACjBhF,WAAWuF;oBACXpD,QAAQ,MAAM+L,IAAAA,8CAAwB,EAACN,SAAS;wBAC9C7G;wBACAnD;oBACF;oBACAvE,eAAetJ,gBAAgBsZ,eAAe;oBAC9C,0CAA0C;oBAC1C3N,qBAAqBwN,0BAA0BlZ,UAAU;oBACzD4L,iBAAiBsN,0BAA0BhZ,MAAM;oBACjDoL,gBAAgB4N,0BAA0B/Y,KAAK;oBAC/C+K,eAAegO,0BAA0B9Y,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAI7F,UAAUkS,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAI2L,8CAAqB,CAC7B,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAItH,aAAa8G;gBACjB,IAAIrV,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAMsO,SAAShM,QAAQ,yBACpBgM,MAAM;oBAET,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMwH,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAM1H,qBACzB,qBAACnN;wBACCC,mBAAmB0U;wBACnBzU,gBAAgB,KAAO;wBACvBtF,yBAAyBA;wBACzBuF,4BAA4BA;wBAC5BC,gCAAgCA;wBAChC1N,OAAO8C,IAAI9C,KAAK;wBAElBoiB,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACnW,aAC1B;wBACE1C,QAAQ8Y,IAAAA,4CAA0B,EAAC;wBACnCrb,SAASkS;wBACTpZ,OAAO8C,IAAI9C,KAAK;oBAClB;oBAGF,wGAAwG;oBACxG0a,aAAaJ,IAAAA,kCAAY,EAACkH,SAASW;gBACrC;gBAEA,OAAO;oBACL5O,iBAAiBqF;oBACjBhF,WAAWuF;oBACXpD,QAAQ,MAAMyM,IAAAA,6CAAuB,EAAC9H,YAAY;wBAChDM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkBmJ,eAAe,IACjC3f,IAAI9C,KAAK,EACTwW;wBAEFmE;wBACAnD;oBACF;oBACAvE,eAAetJ,gBAAgBsZ,eAAe;oBAC9C,0CAA0C;oBAC1C3N,qBAAqBwN,0BAA0BlZ,UAAU;oBACzD4L,iBAAiBsN,0BAA0BhZ,MAAM;oBACjDoL,gBAAgB4N,0BAA0B/Y,KAAK;oBAC/C+K,eAAegO,0BAA0B9Y,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAMkZ,uBAAwC7Z,iBAAiB;gBAC7DpH,MAAM;gBACNqH,OAAO;gBACPV;gBACAW,cAAcA;gBACdK,YAAYC,0BAAc;gBAC1BC,QAAQD,0BAAc;gBACtBE,OAAOF,0BAAc;gBACrBG,MAAM;uBAAIT;iBAAa;YACzB;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAMlC,aAAa,MAAMC,kDAAoB,CAACC,GAAG,CAC/C2b,sBACA1Y,eACA9G,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAMoW,oBAAqBiH,6BACzB,MAAMwC,IAAAA,mEAA0C,EAC9Czb,kDAAoB,CAACC,GAAG,CACtB2b,sBACAzT,aAAapH,sBAAsB,EACnChB,YACAa,wBAAwBI,aAAa,EACrC;gBACEpB,SAAS6R;YACX;YAIN,MAAM1Q,yBAAyBoG,QAAQ,yBACpCpG,sBAAsB;YAEzB,MAAMqS,aAAa,MAAMpT,kDAAoB,CAACC,GAAG,CAC/C2b,sBACA7a,sCACA,qBAACiF;gBACCC,mBAAmB+L,kBAAkB2H,iBAAiB;gBACtDzT,gBAAgBA;gBAChBtF,yBAAyBA;gBACzBuF,4BAA4BA;gBAC5BC,gCAAgCA;gBAChC1N,OAAO8C,IAAI9C,KAAK;gBAElB;gBACEkH,SAASkS;gBACTpZ,OAAO8C,IAAI9C,KAAK;gBAChBqb,kBAAkB;oBAAC3C;iBAAgB;YACrC;YAGF,IAAI4H,+BAA+Bnc,YAAY;gBAC7C,MAAMX,aAAa,MAAMie,IAAAA,oCAAc,EAACnI,kBAAkByH,QAAQ;gBAClEpP,SAASnO,UAAU,GAAGA;gBACtBmO,SAAS+P,WAAW,GAAG,MAAMC,mBAC3Bne,YACA0f,sBACAzT,cACApL,YACApD;YAEJ;YAEA,MAAM0Z,wBAAwBC,IAAAA,oDAAyB,EAAC;gBACtD9C;gBACAR;gBACAuD,sBAAsB1B;gBACtB2B,UAAUzW,WAAWyW,QAAQ;gBAC7BpD,iBAAiBA;YACnB;YACA,OAAO;gBACLnE,iBAAiBqF;gBACjBhF,WAAWuF;gBACXpD,QAAQ,MAAM2F,IAAAA,wCAAkB,EAAChB,YAAY;oBAC3CM,mBAAmBZ,IAAAA,kDAA+B,EAChDd,kBAAkBmJ,eAAe,IACjC3f,IAAI9C,KAAK,EACTwW;oBAEFpQ,oBAAoB;oBACpBuU;oBACAnD;gBACF;gBACA,0CAA0C;gBAC1ClC,qBAAqB4N,qBAAqBtZ,UAAU;gBACpD4L,iBAAiB0N,qBAAqBpZ,MAAM;gBAC5CoL,gBAAgBgO,qBAAqBnZ,KAAK;gBAC1C+K,eAAeoO,qBAAqBlZ,IAAI;YAC1C;QACF;IACF,EAAE,OAAOhD,KAAK;QACZ,IACE2U,IAAAA,gDAAuB,EAAC3U,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAIiG,OAAO,KAAK,YACvBjG,IAAIiG,OAAO,CAAC5B,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAMrE;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAImc,IAAAA,wCAAoB,EAACnc,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAM4U,qBAAqBC,IAAAA,iCAAmB,EAAC7U;QAC/C,IAAI4U,oBAAoB;YACtB,MAAMvO,QAAQyO,IAAAA,8CAA2B,EAAC9U;YAC1C+U,IAAAA,UAAK,EACH,GAAG/U,IAAIgV,MAAM,CAAC,mDAAmD,EAAElZ,IAAI9B,QAAQ,CAAC,kFAAkF,EAAEqM,OAAO;YAG7K,MAAMrG;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIuZ,+BAA+B,MAAM;YACvC,MAAMvZ;QACR;QAEA,IAAI8D;QAEJ,IAAImR,IAAAA,6CAAyB,EAACjV,MAAM;YAClC/D,IAAIC,UAAU,GAAGgZ,IAAAA,+CAA2B,EAAClV;YAC7C8D,YAAYqR,IAAAA,sDAAkC,EAAClZ,IAAIC,UAAU;QAC/D,OAAO,IAAIkZ,IAAAA,8BAAe,EAACpV,MAAM;YAC/B8D,YAAY;YACZ7H,IAAIC,UAAU,GAAGmZ,IAAAA,wCAA8B,EAACrV;YAEhD,MAAMsV,cAAcC,IAAAA,4BAAa,EAC/BC,IAAAA,iCAAuB,EAACxV,MACxB3C,WAAWyW,QAAQ;YAGrB3F,UAAU,YAAYmH;QACxB,OAAO,IAAI,CAACV,oBAAoB;YAC9B3Y,IAAIC,UAAU,GAAG;QACnB;QAEA,MAAM,CAAC2Z,qBAAqBC,qBAAqB,GAAGnE,IAAAA,mCAAkB,EACpEtU,WAAW0T,aAAa,EACxBjV,IAAIgJ,WAAW,EACfzH,WAAWmU,WAAW,EACtBnU,WAAWkU,4BAA4B,EACvCF,IAAAA,wCAAmB,EAACvV,KAAK,QACzBA,IAAI9C,KAAK,EACT;QAGF,MAAMkjB,uBAAwC7Z,iBAAiB;YAC7DpH,MAAM;YACNqH,OAAO;YACPV;YACAW,cAAcA;YACdK,YACE,QAAOP,kCAAAA,eAAgBO,UAAU,MAAK,cAClCP,eAAeO,UAAU,GACzBC,0BAAc;YACpBC,QACE,QAAOT,kCAAAA,eAAgBS,MAAM,MAAK,cAC9BT,eAAeS,MAAM,GACrBD,0BAAc;YACpBE,OACE,QAAOV,kCAAAA,eAAgBU,KAAK,MAAK,cAC7BV,eAAeU,KAAK,GACpBF,0BAAc;YACpBG,MAAM;mBAAKX,CAAAA,kCAAAA,eAAgBW,IAAI,KAAIT;aAAc;QACnD;QACA,MAAMwT,kBAAkB,MAAMzV,kDAAoB,CAACC,GAAG,CACpD2b,sBACA5W,oBACA5I,MACAZ,KACA8V,0BAA0BnX,GAAG,CAAC,AAACuF,IAAYmG,MAAM,IAAIhO,YAAY6H,KACjE8D;QAGF,MAAMkS,oBAAoB1V,kDAAoB,CAACC,GAAG,CAChD2b,sBACAzT,aAAapH,sBAAsB,EACnC0U,iBACA7U,wBAAwBI,aAAa,EACrC;YACEpB,SAAS6R;QACX;QAGF,IAAI;YACF,MAAMkE,aAAa,MAAMC,IAAAA,+CAAyB,EAAC;gBACjDC,gBAAgB1O,QAAQ;gBACxB2O,uBACE,qBAACtO;oBACCvB,mBAAmByP;oBACnBxP,gBAAgBqP;oBAChB3U,yBAAyBA;oBACzBlI,OAAO8C,IAAI9C,KAAK;;gBAGpBqd,eAAe;oBACbrd,OAAO8C,IAAI9C,KAAK;oBAChB,wCAAwC;oBACxCqb,kBAAkB;wBAACyB;qBAAqB;oBACxCtG;gBACF;YACF;YAEA,IAAI8J,+BAA+Bnc,YAAY;gBAC7C,MAAMX,aAAa,MAAMie,IAAAA,oCAAc,EACrClB,2BAA2BQ,QAAQ;gBAErCpP,SAASnO,UAAU,GAAGA;gBACtBmO,SAAS+P,WAAW,GAAG,MAAMC,mBAC3Bne,YACA0f,sBACAzT,cACApL,YACApD;YAEJ;YAEA,MAAMwa,qBAAqBpX,WAAW+C,GAAG;YAEzC,oEAAoE;YACpE,gEAAgE;YAChE,MAAMgc,eACJ7C,sCAAsC8C,oDAA2B,GAC7D9C,2BAA2BQ,QAAQ,KACnCR,2BAA2BkC,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9BlP,iBAAiBqF;gBACjBhF,WAAWuF;gBACXpD,QAAQ,MAAM2F,IAAAA,wCAAkB,EAACuB,YAAY;oBAC3CjC,mBAAmBZ,IAAAA,kDAA+B,EAChDgJ,cACAtgB,IAAI9C,KAAK,EACTwW;oBAEFpQ,oBAAoB;oBACpBuU,uBAAuBC,IAAAA,oDAAyB,EAAC;wBAC/C9C;wBACAR;wBACAuD,sBAAsB,EAAE;wBACxBC,UAAUzW,WAAWyW,QAAQ;wBAC7BpD,iBAAiBA;oBACnB;oBACAF;oBACAiE;gBACF;gBACAxI,eAAe;gBACfqC,qBACEjM,mBAAmB,OAAOA,eAAeO,UAAU,GAAGC,0BAAc;gBACtE2L,iBACEnM,mBAAmB,OAAOA,eAAeS,MAAM,GAAGD,0BAAc;gBAClEqL,gBACE7L,mBAAmB,OAAOA,eAAeU,KAAK,GAAGF,0BAAc;gBACjEiL,eAAezL,mBAAmB,OAAOA,eAAeW,IAAI,GAAG;YACjE;QACF,EAAE,OAAOsT,UAAe;YACtB,IACE9V,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBuU,IAAAA,6CAAyB,EAACqB,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1B9O,QAAQ;gBACV8O;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMgG,gBAAuC,IAAI9d;AACjD,MAAM+d,iBAA+C,EAAE;AAEvD,SAASjT,kBAAkBkT,IAAsB;IAC/CF,cAAcG,GAAG,CAACD;IAClBA,KAAK/O,OAAO,CAAC;QACX,IAAI6O,cAAc7hB,GAAG,CAAC+hB,OAAO;YAC3BF,cAAcI,MAAM,CAACF;YACrB,IAAIF,cAAc9P,IAAI,KAAK,GAAG;gBAC5B,uEAAuE;gBACvE,IAAK,IAAI1R,IAAI,GAAGA,IAAIyhB,eAAe1P,MAAM,EAAE/R,IAAK;oBAC9CyhB,cAAc,CAACzhB,EAAE;gBACnB;gBACAyhB,eAAe1P,MAAM,GAAG;YAC1B;QACF;IACF;AACF;AAEO,eAAepV,mBACpB2kB,YAAwC,EACxClb,uBAA8D;IAE9D,IAAIyb;IACJ,IAAInc,QAAQC,GAAG,CAACmc,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DlV,QAAQ,0CAA0CkV,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DlV,QAAQ,wCAAwCkV,wBAAwB;IAC5E;IAEA,IAAI;QACFA,yBAAyBP,cAAc;YACrCS,wBAAwB;gBACtBC,eAAe5b,wBAAwB4b,aAAa;gBACpDC,WAAW7b,wBAAwB8b,gBAAgB;gBACnDnS,iBAAiB;YACnB;QACF;IACF,EAAE,OAAM;IACN,8DAA8D;IAC9D,gEAAgE;IAChE,oCAAoC;IACtC;IAEA,0EAA0E;IAC1E,2EAA2E;IAC3EvB,kBAAkB0J,IAAAA,wCAA6B;IAC/C,OAAO,IAAI5F,QAAQ,CAAC6P;QAClBV,eAAevD,IAAI,CAACiE;IACtB;AACF;AAEA,MAAMxY,uBAAuB,OAC3B/H,MACAZ;IAEA,MAAM,EACJohB,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAGC,IAAAA,gCAAe,EAAC1gB;IAEpB,IAAI8H;IACJ,IAAI2Y,mBAAmB;QACrB,MAAM,GAAGE,OAAO,GAAG,MAAMC,IAAAA,gEAA+B,EAAC;YACvDxhB;YACAyhB,UAAUJ,iBAAiB,CAAC,EAAE;YAC9BK,cAAcL,iBAAiB,CAAC,EAAE;YAClC5e,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAgG,oBAAoB6Y;IACtB;IAEA,OAAO7Y;AACT;AAEA,eAAemW,mBACb8C,kBAA0B,EAC1Bpb,cAA8B,EAC9BoG,YAA2B,EAC3BpL,UAAsB,EACtBpD,mBAA+C;IAE/C,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMiH,0BAA0B7D,WAAW6D,uBAAuB;IAClE,IAAI,CAACA,2BAA2B,CAAC7D,WAAWsD,YAAY,CAAC+c,kBAAkB,EAAE;QAC3E;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgBnd,QAAQC,GAAG,CAACgJ,YAAY,KAAK;IACnD,MAAMoT,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWY,gBACPzc,wBAAwB0c,oBAAoB,GAC5C1c,wBAAwB2c,gBAAgB;QAC5ChT,iBAAiB;IACnB;IAEA,8EAA8E;IAC9E,0EAA0E;IAC1E,2EAA2E;IAC3E,sBAAsB;IACtB,EAAE;IACF,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,2EAA2E;IAC3E,6EAA6E;IAC7E,uEAAuE;IACvE,2EAA2E;IAC3E,6EAA6E;IAC7E,kBAAkB;IAClB,MAAMiT,0BACJzgB,WAAWsD,YAAY,CAAClI,iBAAiB,KAAK,QAAQ,iBAAiB;IACvE,CAAC4E,WAAWsD,YAAY,CAACC,SAAS,CAAC,wBAAwB;;IAE7D,MAAMmd,YAAY1b,eAAeU,KAAK;IACtC,OAAO,MAAM0F,aAAakS,kBAAkB,CAC1CmD,yBACAL,oBACAM,WACA7c,wBAAwBI,aAAa,EACrCub,wBACA5iB;AAEJ"}