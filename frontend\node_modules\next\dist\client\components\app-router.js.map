{"version": 3, "sources": ["../../../src/client/components/app-router.tsx"], "sourcesContent": ["'use client'\n\nimport React, {\n  use,\n  useEffect,\n  useMemo,\n  useCallback,\n  startTransition,\n  useInsertionEffect,\n  useDeferredValue,\n} from 'react'\nimport {\n  AppRouterContext,\n  LayoutRouterContext,\n  GlobalLayoutRouterContext,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport type {\n  CacheNode,\n  AppRouterInstance,\n} from '../../shared/lib/app-router-context.shared-runtime'\nimport {\n  ACTION_HMR_REFRESH,\n  ACTION_NAVIGATE,\n  ACTION_PREFETCH,\n  ACTION_REFRESH,\n  ACTION_RESTORE,\n  ACTION_SERVER_PATCH,\n  PrefetchKind,\n} from './router-reducer/router-reducer-types'\nimport type {\n  AppRouterState,\n  ReducerActions,\n  RouterChangeByServerResponse,\n  RouterNavigate,\n} from './router-reducer/router-reducer-types'\nimport { createHrefFromUrl } from './router-reducer/create-href-from-url'\nimport {\n  SearchParamsContext,\n  PathnameContext,\n  PathParamsContext,\n} from '../../shared/lib/hooks-client-context.shared-runtime'\nimport { useReducer, useUnwrapState } from './use-reducer'\nimport {\n  default as DefaultGlobalError,\n  ErrorBoundary,\n  type GlobalErrorComponent,\n} from './error-boundary'\nimport { isBot } from '../../shared/lib/router/utils/is-bot'\nimport { addBasePath } from '../add-base-path'\nimport { AppRouterAnnouncer } from './app-router-announcer'\nimport { RedirectBoundary } from './redirect-boundary'\nimport { findHeadInCache } from './router-reducer/reducers/find-head-in-cache'\nimport { unresolvedThenable } from './unresolved-thenable'\nimport { removeBasePath } from '../remove-base-path'\nimport { hasBasePath } from '../has-base-path'\nimport { getSelectedParams } from './router-reducer/compute-changed-path'\nimport type { FlightRouterState } from '../../server/app-render/types'\nimport { useNavFailureHandler } from './nav-failure-handler'\nimport { useServerActionDispatcher } from '../app-call-server'\nimport type { AppRouterActionQueue } from '../../shared/lib/router/action-queue'\nimport { prefetch as prefetchWithSegmentCache } from './segment-cache'\nimport { getRedirectTypeFromError, getURLFromRedirectError } from './redirect'\nimport { isRedirectError, RedirectType } from './redirect-error'\nimport { prefetchReducer } from './router-reducer/reducers/prefetch-reducer'\nimport { pingVisibleLinks } from './links'\n\nconst globalMutable: {\n  pendingMpaPath?: string\n} = {}\n\nfunction isExternalURL(url: URL) {\n  return url.origin !== window.location.origin\n}\n\n/**\n * Given a link href, constructs the URL that should be prefetched. Returns null\n * in cases where prefetching should be disabled, like external URLs, or\n * during development.\n * @param href The href passed to <Link>, router.prefetch(), or similar\n * @returns A URL object to prefetch, or null if prefetching should be disabled\n */\nexport function createPrefetchURL(href: string): URL | null {\n  // Don't prefetch for bots as they don't navigate.\n  if (isBot(window.navigator.userAgent)) {\n    return null\n  }\n\n  let url: URL\n  try {\n    url = new URL(addBasePath(href), window.location.href)\n  } catch (_) {\n    // TODO: Does this need to throw or can we just console.error instead? Does\n    // anyone rely on this throwing? (Seems unlikely.)\n    throw new Error(\n      `Cannot prefetch '${href}' because it cannot be converted to a URL.`\n    )\n  }\n\n  // Don't prefetch during development (improves compilation performance)\n  if (process.env.NODE_ENV === 'development') {\n    return null\n  }\n\n  // External urls can't be prefetched in the same way.\n  if (isExternalURL(url)) {\n    return null\n  }\n\n  return url\n}\n\nfunction HistoryUpdater({\n  appRouterState,\n}: {\n  appRouterState: AppRouterState\n}) {\n  useInsertionEffect(() => {\n    if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n      // clear pending URL as navigation is no longer\n      // in flight\n      window.next.__pendingUrl = undefined\n    }\n\n    const { tree, pushRef, canonicalUrl } = appRouterState\n    const historyState = {\n      ...(pushRef.preserveCustomHistoryState ? window.history.state : {}),\n      // Identifier is shortened intentionally.\n      // __NA is used to identify if the history entry can be handled by the app-router.\n      // __N is used to identify if the history entry can be handled by the old router.\n      __NA: true,\n      __PRIVATE_NEXTJS_INTERNALS_TREE: tree,\n    }\n    if (\n      pushRef.pendingPush &&\n      // Skip pushing an additional history entry if the canonicalUrl is the same as the current url.\n      // This mirrors the browser behavior for normal navigation.\n      createHrefFromUrl(new URL(window.location.href)) !== canonicalUrl\n    ) {\n      // This intentionally mutates React state, pushRef is overwritten to ensure additional push/replace calls do not trigger an additional history entry.\n      pushRef.pendingPush = false\n      window.history.pushState(historyState, '', canonicalUrl)\n    } else {\n      window.history.replaceState(historyState, '', canonicalUrl)\n    }\n  }, [appRouterState])\n\n  useEffect(() => {\n    // The Next-Url and the base tree may affect the result of a prefetch\n    // task. Re-prefetch all visible links with the updated values. In most\n    // cases, this will not result in any new network requests, only if\n    // the prefetch result actually varies on one of these inputs.\n    if (process.env.__NEXT_CLIENT_SEGMENT_CACHE) {\n      pingVisibleLinks(appRouterState.nextUrl, appRouterState.tree)\n    }\n  }, [appRouterState.nextUrl, appRouterState.tree])\n\n  return null\n}\n\nexport function createEmptyCacheNode(): CacheNode {\n  return {\n    lazyData: null,\n    rsc: null,\n    prefetchRsc: null,\n    head: null,\n    prefetchHead: null,\n    parallelRoutes: new Map(),\n    loading: null,\n  }\n}\n\n/**\n * Server response that only patches the cache and tree.\n */\nfunction useChangeByServerResponse(\n  dispatch: React.Dispatch<ReducerActions>\n): RouterChangeByServerResponse {\n  return useCallback(\n    ({ previousTree, serverResponse }) => {\n      startTransition(() => {\n        dispatch({\n          type: ACTION_SERVER_PATCH,\n          previousTree,\n          serverResponse,\n        })\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction useNavigate(dispatch: React.Dispatch<ReducerActions>): RouterNavigate {\n  return useCallback(\n    (href, navigateType, shouldScroll) => {\n      const url = new URL(addBasePath(href), location.href)\n\n      if (process.env.__NEXT_APP_NAV_FAIL_HANDLING) {\n        window.next.__pendingUrl = url\n      }\n\n      return dispatch({\n        type: ACTION_NAVIGATE,\n        url,\n        isExternalUrl: isExternalURL(url),\n        locationSearch: location.search,\n        shouldScroll: shouldScroll ?? true,\n        navigateType,\n        allowAliasing: true,\n      })\n    },\n    [dispatch]\n  )\n}\n\nfunction copyNextJsInternalHistoryState(data: any) {\n  if (data == null) data = {}\n  const currentState = window.history.state\n  const __NA = currentState?.__NA\n  if (__NA) {\n    data.__NA = __NA\n  }\n  const __PRIVATE_NEXTJS_INTERNALS_TREE =\n    currentState?.__PRIVATE_NEXTJS_INTERNALS_TREE\n  if (__PRIVATE_NEXTJS_INTERNALS_TREE) {\n    data.__PRIVATE_NEXTJS_INTERNALS_TREE = __PRIVATE_NEXTJS_INTERNALS_TREE\n  }\n\n  return data\n}\n\nfunction Head({\n  headCacheNode,\n}: {\n  headCacheNode: CacheNode | null\n}): React.ReactNode {\n  // If this segment has a `prefetchHead`, it's the statically prefetched data.\n  // We should use that on initial render instead of `head`. Then we'll switch\n  // to `head` when the dynamic response streams in.\n  const head = headCacheNode !== null ? headCacheNode.head : null\n  const prefetchHead =\n    headCacheNode !== null ? headCacheNode.prefetchHead : null\n\n  // If no prefetch data is available, then we go straight to rendering `head`.\n  const resolvedPrefetchRsc = prefetchHead !== null ? prefetchHead : head\n\n  // We use `useDeferredValue` to handle switching between the prefetched and\n  // final values. The second argument is returned on initial render, then it\n  // re-renders with the first argument.\n  return useDeferredValue(head, resolvedPrefetchRsc)\n}\n\n/**\n * The global router that wraps the application components.\n */\nfunction Router({\n  actionQueue,\n  assetPrefix,\n  globalError,\n}: {\n  actionQueue: AppRouterActionQueue\n  assetPrefix: string\n  globalError: [GlobalErrorComponent, React.ReactNode]\n}) {\n  const [state, dispatch] = useReducer(actionQueue)\n  const { canonicalUrl } = useUnwrapState(state)\n  // Add memoized pathname/query for useSearchParams and usePathname.\n  const { searchParams, pathname } = useMemo(() => {\n    const url = new URL(\n      canonicalUrl,\n      typeof window === 'undefined' ? 'http://n' : window.location.href\n    )\n\n    return {\n      // This is turned into a readonly class in `useSearchParams`\n      searchParams: url.searchParams,\n      pathname: hasBasePath(url.pathname)\n        ? removeBasePath(url.pathname)\n        : url.pathname,\n    }\n  }, [canonicalUrl])\n\n  const changeByServerResponse = useChangeByServerResponse(dispatch)\n  const navigate = useNavigate(dispatch)\n  useServerActionDispatcher(dispatch)\n\n  /**\n   * The app router that is exposed through `useRouter`. It's only concerned with dispatching actions to the reducer, does not hold state.\n   */\n  const appRouter = useMemo<AppRouterInstance>(() => {\n    const routerInstance: AppRouterInstance = {\n      back: () => window.history.back(),\n      forward: () => window.history.forward(),\n      prefetch: process.env.__NEXT_CLIENT_SEGMENT_CACHE\n        ? // Unlike the old implementation, the Segment Cache doesn't store its\n          // data in the router reducer state; it writes into a global mutable\n          // cache. So we don't need to dispatch an action.\n          (href, options) =>\n            prefetchWithSegmentCache(\n              href,\n              actionQueue.state.nextUrl,\n              actionQueue.state.tree,\n              options?.kind === PrefetchKind.FULL\n            )\n        : (href, options) => {\n            // Use the old prefetch implementation.\n            const url = createPrefetchURL(href)\n            if (url !== null) {\n              // The prefetch reducer doesn't actually update any state or\n              // trigger a rerender. It just writes to a mutable cache. So we\n              // shouldn't bother calling setState/dispatch; we can just re-run\n              // the reducer directly using the current state.\n              // TODO: Refactor this away from a \"reducer\" so it's\n              // less confusing.\n              prefetchReducer(actionQueue.state, {\n                type: ACTION_PREFETCH,\n                url,\n                kind: options?.kind ?? PrefetchKind.FULL,\n              })\n            }\n          },\n      replace: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'replace', options.scroll ?? true)\n        })\n      },\n      push: (href, options = {}) => {\n        startTransition(() => {\n          navigate(href, 'push', options.scroll ?? true)\n        })\n      },\n      refresh: () => {\n        startTransition(() => {\n          dispatch({\n            type: ACTION_REFRESH,\n            origin: window.location.origin,\n          })\n        })\n      },\n      hmrRefresh: () => {\n        if (process.env.NODE_ENV !== 'development') {\n          throw new Error(\n            'hmrRefresh can only be used in development mode. Please use refresh instead.'\n          )\n        } else {\n          startTransition(() => {\n            dispatch({\n              type: ACTION_HMR_REFRESH,\n              origin: window.location.origin,\n            })\n          })\n        }\n      },\n    }\n\n    return routerInstance\n  }, [actionQueue, dispatch, navigate])\n\n  useEffect(() => {\n    // Exists for debugging purposes. Don't use in application code.\n    if (window.next) {\n      window.next.router = appRouter\n    }\n  }, [appRouter])\n\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const { cache, prefetchCache, tree } = useUnwrapState(state)\n\n    // This hook is in a conditional but that is ok because `process.env.NODE_ENV` never changes\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    useEffect(() => {\n      // Add `window.nd` for debugging purposes.\n      // This is not meant for use in applications as concurrent rendering will affect the cache/tree/router.\n      // @ts-ignore this is for debugging\n      window.nd = {\n        router: appRouter,\n        cache,\n        prefetchCache,\n        tree,\n      }\n    }, [appRouter, cache, prefetchCache, tree])\n  }\n\n  useEffect(() => {\n    // If the app is restored from bfcache, it's possible that\n    // pushRef.mpaNavigation is true, which would mean that any re-render of this component\n    // would trigger the mpa navigation logic again from the lines below.\n    // This will restore the router to the initial state in the event that the app is restored from bfcache.\n    function handlePageShow(event: PageTransitionEvent) {\n      if (\n        !event.persisted ||\n        !window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n      ) {\n        return\n      }\n\n      // Clear the pendingMpaPath value so that a subsequent MPA navigation to the same URL can be triggered.\n      // This is necessary because if the browser restored from bfcache, the pendingMpaPath would still be set to the value\n      // of the last MPA navigation.\n      globalMutable.pendingMpaPath = undefined\n\n      dispatch({\n        type: ACTION_RESTORE,\n        url: new URL(window.location.href),\n        tree: window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n      })\n    }\n\n    window.addEventListener('pageshow', handlePageShow)\n\n    return () => {\n      window.removeEventListener('pageshow', handlePageShow)\n    }\n  }, [dispatch])\n\n  useEffect(() => {\n    // Ensure that any redirect errors that bubble up outside of the RedirectBoundary\n    // are caught and handled by the router.\n    function handleUnhandledRedirect(\n      event: ErrorEvent | PromiseRejectionEvent\n    ) {\n      const error = 'reason' in event ? event.reason : event.error\n      if (isRedirectError(error)) {\n        event.preventDefault()\n        const url = getURLFromRedirectError(error)\n        const redirectType = getRedirectTypeFromError(error)\n        if (redirectType === RedirectType.push) {\n          appRouter.push(url, {})\n        } else {\n          appRouter.replace(url, {})\n        }\n      }\n    }\n    window.addEventListener('error', handleUnhandledRedirect)\n    window.addEventListener('unhandledrejection', handleUnhandledRedirect)\n\n    return () => {\n      window.removeEventListener('error', handleUnhandledRedirect)\n      window.removeEventListener('unhandledrejection', handleUnhandledRedirect)\n    }\n  }, [appRouter])\n\n  // When mpaNavigation flag is set do a hard navigation to the new url.\n  // Infinitely suspend because we don't actually want to rerender any child\n  // components with the new URL and any entangled state updates shouldn't\n  // commit either (eg: useTransition isPending should stay true until the page\n  // unloads).\n  //\n  // This is a side effect in render. Don't try this at home, kids. It's\n  // probably safe because we know this is a singleton component and it's never\n  // in <Offscreen>. At least I hope so. (It will run twice in dev strict mode,\n  // but that's... fine?)\n  const { pushRef } = useUnwrapState(state)\n  if (pushRef.mpaNavigation) {\n    // if there's a re-render, we don't want to trigger another redirect if one is already in flight to the same URL\n    if (globalMutable.pendingMpaPath !== canonicalUrl) {\n      const location = window.location\n      if (pushRef.pendingPush) {\n        location.assign(canonicalUrl)\n      } else {\n        location.replace(canonicalUrl)\n      }\n\n      globalMutable.pendingMpaPath = canonicalUrl\n    }\n    // TODO-APP: Should we listen to navigateerror here to catch failed\n    // navigations somehow? And should we call window.stop() if a SPA navigation\n    // should interrupt an MPA one?\n    use(unresolvedThenable)\n  }\n\n  useEffect(() => {\n    const originalPushState = window.history.pushState.bind(window.history)\n    const originalReplaceState = window.history.replaceState.bind(\n      window.history\n    )\n\n    // Ensure the canonical URL in the Next.js Router is updated when the URL is changed so that `usePathname` and `useSearchParams` hold the pushed values.\n    const applyUrlFromHistoryPushReplace = (\n      url: string | URL | null | undefined\n    ) => {\n      const href = window.location.href\n      const tree: FlightRouterState | undefined =\n        window.history.state?.__PRIVATE_NEXTJS_INTERNALS_TREE\n\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(url ?? href, href),\n          tree,\n        })\n      })\n    }\n\n    /**\n     * Patch pushState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.pushState = function pushState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalPushState(data, _unused, url)\n      }\n\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n\n      return originalPushState(data, _unused, url)\n    }\n\n    /**\n     * Patch replaceState to ensure external changes to the history are reflected in the Next.js Router.\n     * Ensures Next.js internal history state is copied to the new history entry.\n     * Ensures usePathname and useSearchParams hold the newly provided url.\n     */\n    window.history.replaceState = function replaceState(\n      data: any,\n      _unused: string,\n      url?: string | URL | null\n    ): void {\n      // Avoid a loop when Next.js internals trigger pushState/replaceState\n      if (data?.__NA || data?._N) {\n        return originalReplaceState(data, _unused, url)\n      }\n      data = copyNextJsInternalHistoryState(data)\n\n      if (url) {\n        applyUrlFromHistoryPushReplace(url)\n      }\n      return originalReplaceState(data, _unused, url)\n    }\n\n    /**\n     * Handle popstate event, this is used to handle back/forward in the browser.\n     * By default dispatches ACTION_RESTORE, however if the history entry was not pushed/replaced by app-router it will reload the page.\n     * That case can happen when the old router injected the history entry.\n     */\n    const onPopState = (event: PopStateEvent) => {\n      if (!event.state) {\n        // TODO-APP: this case only happens when pushState/replaceState was called outside of Next.js. It should probably reload the page in this case.\n        return\n      }\n\n      // This case happens when the history entry was pushed by the `pages` router.\n      if (!event.state.__NA) {\n        window.location.reload()\n        return\n      }\n\n      // TODO-APP: Ideally the back button should not use startTransition as it should apply the updates synchronously\n      // Without startTransition works if the cache is there for this path\n      startTransition(() => {\n        dispatch({\n          type: ACTION_RESTORE,\n          url: new URL(window.location.href),\n          tree: event.state.__PRIVATE_NEXTJS_INTERNALS_TREE,\n        })\n      })\n    }\n\n    // Register popstate event to call onPopstate.\n    window.addEventListener('popstate', onPopState)\n    return () => {\n      window.history.pushState = originalPushState\n      window.history.replaceState = originalReplaceState\n      window.removeEventListener('popstate', onPopState)\n    }\n  }, [dispatch])\n\n  const { cache, tree, nextUrl, focusAndScrollRef } = useUnwrapState(state)\n\n  const matchingHead = useMemo(() => {\n    return findHeadInCache(cache, tree[1])\n  }, [cache, tree])\n\n  // Add memoized pathParams for useParams.\n  const pathParams = useMemo(() => {\n    return getSelectedParams(tree)\n  }, [tree])\n\n  const layoutRouterContext = useMemo(() => {\n    return {\n      parentTree: tree,\n      parentCacheNode: cache,\n      parentSegmentPath: null,\n      // Root node always has `url`\n      // Provided in AppTreeContext to ensure it can be overwritten in layout-router\n      url: canonicalUrl,\n    }\n  }, [tree, cache, canonicalUrl])\n\n  const globalLayoutRouterContext = useMemo(() => {\n    return {\n      changeByServerResponse,\n      tree,\n      focusAndScrollRef,\n      nextUrl,\n    }\n  }, [changeByServerResponse, tree, focusAndScrollRef, nextUrl])\n\n  let head\n  if (matchingHead !== null) {\n    // The head is wrapped in an extra component so we can use\n    // `useDeferredValue` to swap between the prefetched and final versions of\n    // the head. (This is what LayoutRouter does for segment data, too.)\n    //\n    // The `key` is used to remount the component whenever the head moves to\n    // a different segment.\n    const [headCacheNode, headKey] = matchingHead\n    head = <Head key={headKey} headCacheNode={headCacheNode} />\n  } else {\n    head = null\n  }\n\n  let content = (\n    <RedirectBoundary>\n      {head}\n      {cache.rsc}\n      <AppRouterAnnouncer tree={tree} />\n    </RedirectBoundary>\n  )\n\n  if (process.env.NODE_ENV !== 'production') {\n    // In development, we apply few error boundaries and hot-reloader:\n    // - DevRootHTTPAccessFallbackBoundary: avoid using navigation API like notFound() in root layout\n    // - HotReloader:\n    //  - hot-reload the app when the code changes\n    //  - render dev overlay\n    //  - catch runtime errors and display global-error when necessary\n    if (typeof window !== 'undefined') {\n      const { DevRootHTTPAccessFallbackBoundary } =\n        require('./dev-root-http-access-fallback-boundary') as typeof import('./dev-root-http-access-fallback-boundary')\n      content = (\n        <DevRootHTTPAccessFallbackBoundary>\n          {content}\n        </DevRootHTTPAccessFallbackBoundary>\n      )\n    }\n    const HotReloader: typeof import('./react-dev-overlay/app/hot-reloader-client').default =\n      require('./react-dev-overlay/app/hot-reloader-client').default\n\n    content = (\n      <HotReloader assetPrefix={assetPrefix} globalError={globalError}>\n        {content}\n      </HotReloader>\n    )\n  } else {\n    // In production, we only apply the user-customized global error boundary.\n    content = (\n      <ErrorBoundary\n        errorComponent={globalError[0]}\n        errorStyles={globalError[1]}\n      >\n        {content}\n      </ErrorBoundary>\n    )\n  }\n\n  return (\n    <>\n      <HistoryUpdater appRouterState={useUnwrapState(state)} />\n      <RuntimeStyles />\n      <PathParamsContext.Provider value={pathParams}>\n        <PathnameContext.Provider value={pathname}>\n          <SearchParamsContext.Provider value={searchParams}>\n            <GlobalLayoutRouterContext.Provider\n              value={globalLayoutRouterContext}\n            >\n              <AppRouterContext.Provider value={appRouter}>\n                <LayoutRouterContext.Provider value={layoutRouterContext}>\n                  {content}\n                </LayoutRouterContext.Provider>\n              </AppRouterContext.Provider>\n            </GlobalLayoutRouterContext.Provider>\n          </SearchParamsContext.Provider>\n        </PathnameContext.Provider>\n      </PathParamsContext.Provider>\n    </>\n  )\n}\n\nexport default function AppRouter({\n  actionQueue,\n  globalErrorComponentAndStyles: [globalErrorComponent, globalErrorStyles],\n  assetPrefix,\n}: {\n  actionQueue: AppRouterActionQueue\n  globalErrorComponentAndStyles: [GlobalErrorComponent, React.ReactNode]\n  assetPrefix: string\n}) {\n  useNavFailureHandler()\n\n  return (\n    <ErrorBoundary\n      // At the very top level, use the default GlobalError component as the final fallback.\n      // When the app router itself fails, which means the framework itself fails, we show the default error.\n      errorComponent={DefaultGlobalError}\n    >\n      <Router\n        actionQueue={actionQueue}\n        assetPrefix={assetPrefix}\n        globalError={[globalErrorComponent, globalErrorStyles]}\n      />\n    </ErrorBoundary>\n  )\n}\n\nconst runtimeStyles = new Set<string>()\nlet runtimeStyleChanged = new Set<() => void>()\n\nglobalThis._N_E_STYLE_LOAD = function (href: string) {\n  let len = runtimeStyles.size\n  runtimeStyles.add(href)\n  if (runtimeStyles.size !== len) {\n    runtimeStyleChanged.forEach((cb) => cb())\n  }\n  // TODO figure out how to get a promise here\n  // But maybe it's not necessary as react would block rendering until it's loaded\n  return Promise.resolve()\n}\n\nfunction RuntimeStyles() {\n  const [, forceUpdate] = React.useState(0)\n  const renderedStylesSize = runtimeStyles.size\n  useEffect(() => {\n    const changed = () => forceUpdate((c) => c + 1)\n    runtimeStyleChanged.add(changed)\n    if (renderedStylesSize !== runtimeStyles.size) {\n      changed()\n    }\n    return () => {\n      runtimeStyleChanged.delete(changed)\n    }\n  }, [renderedStylesSize, forceUpdate])\n\n  const dplId = process.env.NEXT_DEPLOYMENT_ID\n    ? `?dpl=${process.env.NEXT_DEPLOYMENT_ID}`\n    : ''\n  return [...runtimeStyles].map((href, i) => (\n    <link\n      key={i}\n      rel=\"stylesheet\"\n      href={`${href}${dplId}`}\n      // @ts-ignore\n      precedence=\"next\"\n      // TODO figure out crossOrigin and nonce\n      // crossOrigin={TODO}\n      // nonce={TODO}\n    />\n  ))\n}\n"], "names": ["createEmptyCacheNode", "createPrefetchURL", "AppRouter", "globalMutable", "isExternalURL", "url", "origin", "window", "location", "href", "isBot", "navigator", "userAgent", "URL", "addBasePath", "_", "Error", "process", "env", "NODE_ENV", "HistoryUpdater", "appRouterState", "useInsertionEffect", "__NEXT_APP_NAV_FAIL_HANDLING", "next", "__pendingUrl", "undefined", "tree", "pushRef", "canonicalUrl", "historyState", "preserveCustomHistoryState", "history", "state", "__NA", "__PRIVATE_NEXTJS_INTERNALS_TREE", "pendingPush", "createHrefFromUrl", "pushState", "replaceState", "useEffect", "__NEXT_CLIENT_SEGMENT_CACHE", "pingVisibleLinks", "nextUrl", "lazyData", "rsc", "prefetchRsc", "head", "prefetchHead", "parallelRoutes", "Map", "loading", "useChangeByServerResponse", "dispatch", "useCallback", "previousTree", "serverResponse", "startTransition", "type", "ACTION_SERVER_PATCH", "useNavigate", "navigateType", "shouldScroll", "ACTION_NAVIGATE", "isExternalUrl", "locationSearch", "search", "allowAliasing", "copyNextJsInternalHistoryState", "data", "currentState", "Head", "headCacheNode", "resolvedPrefetchRsc", "useDeferredValue", "Router", "actionQueue", "assetPrefix", "globalError", "useReducer", "useUnwrapState", "searchParams", "pathname", "useMemo", "has<PERSON>ase<PERSON><PERSON>", "removeBasePath", "changeByServerResponse", "navigate", "useServerActionDispatcher", "appRouter", "routerInstance", "back", "forward", "prefetch", "options", "prefetchWithSegmentCache", "kind", "PrefetchKind", "FULL", "prefetchReducer", "ACTION_PREFETCH", "replace", "scroll", "push", "refresh", "ACTION_REFRESH", "hmrRefresh", "ACTION_HMR_REFRESH", "router", "cache", "prefetchCache", "nd", "handlePageShow", "event", "persisted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ACTION_RESTORE", "addEventListener", "removeEventListener", "handleUnhandledRedirect", "error", "reason", "isRedirectError", "preventDefault", "getURLFromRedirectError", "redirectType", "getRedirectTypeFromError", "RedirectType", "mpaNavigation", "assign", "use", "unresolvedThenable", "originalPushState", "bind", "originalReplaceState", "applyUrlFromHistoryPushReplace", "_unused", "_N", "onPopState", "reload", "focusAndScrollRef", "matchingHead", "findHeadInCache", "pathParams", "getSelectedParams", "layoutRouterContext", "parentTree", "parentCacheNode", "parentSegmentPath", "globalLayoutRouterContext", "head<PERSON><PERSON>", "content", "RedirectBoundary", "AppRouterAnnouncer", "DevRootHTTPAccessFallbackBoundary", "require", "HotReloader", "default", "Error<PERSON>ou<PERSON><PERSON>", "errorComponent", "errorStyles", "RuntimeStyles", "PathParamsContext", "Provider", "value", "PathnameContext", "SearchParamsContext", "GlobalLayoutRouterContext", "AppRouterContext", "LayoutRouterContext", "globalErrorComponentAndStyles", "globalErrorComponent", "globalErrorStyles", "useNavFailureHandler", "DefaultGlobalError", "runtimeStyles", "Set", "runtimeStyleChanged", "globalThis", "_N_E_STYLE_LOAD", "len", "size", "add", "for<PERSON>ach", "cb", "Promise", "resolve", "forceUpdate", "React", "useState", "renderedStylesSize", "changed", "c", "delete", "dplId", "NEXT_DEPLOYMENT_ID", "map", "i", "link", "rel", "precedence"], "mappings": "AAAA;;;;;;;;;;;;;;;;;IA+JgBA,oBAAoB;eAApBA;;IA9EAC,iBAAiB;eAAjBA;;IAgmBhB,OAwBC;eAxBuBC;;;;;iEAvqBjB;+CAKA;oCAaA;mCAO2B;iDAK3B;4BACoC;yEAKpC;uBACe;6BACM;oCACO;kCACF;iCACD;oCACG;gCACJ;6BACH;oCACM;mCAEG;+BACK;8BAEW;0BACa;+BACpB;iCACd;uBACC;AAEjC,MAAMC,gBAEF,CAAC;AAEL,SAASC,cAAcC,GAAQ;IAC7B,OAAOA,IAAIC,MAAM,KAAKC,OAAOC,QAAQ,CAACF,MAAM;AAC9C;AASO,SAASL,kBAAkBQ,IAAY;IAC5C,kDAAkD;IAClD,IAAIC,IAAAA,YAAK,EAACH,OAAOI,SAAS,CAACC,SAAS,GAAG;QACrC,OAAO;IACT;IAEA,IAAIP;IACJ,IAAI;QACFA,MAAM,IAAIQ,IAAIC,IAAAA,wBAAW,EAACL,OAAOF,OAAOC,QAAQ,CAACC,IAAI;IACvD,EAAE,OAAOM,GAAG;QACV,2EAA2E;QAC3E,kDAAkD;QAClD,MAAM,qBAEL,CAFK,IAAIC,MACR,AAAC,sBAAmBP,OAAK,+CADrB,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,uEAAuE;IACvE,IAAIQ,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,OAAO;IACT;IAEA,qDAAqD;IACrD,IAAIf,cAAcC,MAAM;QACtB,OAAO;IACT;IAEA,OAAOA;AACT;AAEA,SAASe,eAAe,KAIvB;IAJuB,IAAA,EACtBC,cAAc,EAGf,GAJuB;IAKtBC,IAAAA,yBAAkB,EAAC;QACjB,IAAIL,QAAQC,GAAG,CAACK,4BAA4B,EAAE;YAC5C,+CAA+C;YAC/C,YAAY;YACZhB,OAAOiB,IAAI,CAACC,YAAY,GAAGC;QAC7B;QAEA,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,YAAY,EAAE,GAAGR;QACxC,MAAMS,eAAe;YACnB,GAAIF,QAAQG,0BAA0B,GAAGxB,OAAOyB,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC;YAClE,yCAAyC;YACzC,kFAAkF;YAClF,iFAAiF;YACjFC,MAAM;YACNC,iCAAiCR;QACnC;QACA,IACEC,QAAQQ,WAAW,IACnB,+FAA+F;QAC/F,2DAA2D;QAC3DC,IAAAA,oCAAiB,EAAC,IAAIxB,IAAIN,OAAOC,QAAQ,CAACC,IAAI,OAAOoB,cACrD;YACA,qJAAqJ;YACrJD,QAAQQ,WAAW,GAAG;YACtB7B,OAAOyB,OAAO,CAACM,SAAS,CAACR,cAAc,IAAID;QAC7C,OAAO;YACLtB,OAAOyB,OAAO,CAACO,YAAY,CAACT,cAAc,IAAID;QAChD;IACF,GAAG;QAACR;KAAe;IAEnBmB,IAAAA,gBAAS,EAAC;QACR,qEAAqE;QACrE,uEAAuE;QACvE,mEAAmE;QACnE,8DAA8D;QAC9D,IAAIvB,QAAQC,GAAG,CAACuB,2BAA2B,EAAE;YAC3CC,IAAAA,uBAAgB,EAACrB,eAAesB,OAAO,EAAEtB,eAAeM,IAAI;QAC9D;IACF,GAAG;QAACN,eAAesB,OAAO;QAAEtB,eAAeM,IAAI;KAAC;IAEhD,OAAO;AACT;AAEO,SAAS3B;IACd,OAAO;QACL4C,UAAU;QACVC,KAAK;QACLC,aAAa;QACbC,MAAM;QACNC,cAAc;QACdC,gBAAgB,IAAIC;QACpBC,SAAS;IACX;AACF;AAEA;;CAEC,GACD,SAASC,0BACPC,QAAwC;IAExC,OAAOC,IAAAA,kBAAW,EAChB;YAAC,EAAEC,YAAY,EAAEC,cAAc,EAAE;QAC/BC,IAAAA,sBAAe,EAAC;YACdJ,SAAS;gBACPK,MAAMC,uCAAmB;gBACzBJ;gBACAC;YACF;QACF;IACF,GACA;QAACH;KAAS;AAEd;AAEA,SAASO,YAAYP,QAAwC;IAC3D,OAAOC,IAAAA,kBAAW,EAChB,CAAC7C,MAAMoD,cAAcC;QACnB,MAAMzD,MAAM,IAAIQ,IAAIC,IAAAA,wBAAW,EAACL,OAAOD,SAASC,IAAI;QAEpD,IAAIQ,QAAQC,GAAG,CAACK,4BAA4B,EAAE;YAC5ChB,OAAOiB,IAAI,CAACC,YAAY,GAAGpB;QAC7B;QAEA,OAAOgD,SAAS;YACdK,MAAMK,mCAAe;YACrB1D;YACA2D,eAAe5D,cAAcC;YAC7B4D,gBAAgBzD,SAAS0D,MAAM;YAC/BJ,cAAcA,uBAAAA,eAAgB;YAC9BD;YACAM,eAAe;QACjB;IACF,GACA;QAACd;KAAS;AAEd;AAEA,SAASe,+BAA+BC,IAAS;IAC/C,IAAIA,QAAQ,MAAMA,OAAO,CAAC;IAC1B,MAAMC,eAAe/D,OAAOyB,OAAO,CAACC,KAAK;IACzC,MAAMC,OAAOoC,gCAAAA,aAAcpC,IAAI;IAC/B,IAAIA,MAAM;QACRmC,KAAKnC,IAAI,GAAGA;IACd;IACA,MAAMC,kCACJmC,gCAAAA,aAAcnC,+BAA+B;IAC/C,IAAIA,iCAAiC;QACnCkC,KAAKlC,+BAA+B,GAAGA;IACzC;IAEA,OAAOkC;AACT;AAEA,SAASE,KAAK,KAIb;IAJa,IAAA,EACZC,aAAa,EAGd,GAJa;IAKZ,6EAA6E;IAC7E,4EAA4E;IAC5E,kDAAkD;IAClD,MAAMzB,OAAOyB,kBAAkB,OAAOA,cAAczB,IAAI,GAAG;IAC3D,MAAMC,eACJwB,kBAAkB,OAAOA,cAAcxB,YAAY,GAAG;IAExD,6EAA6E;IAC7E,MAAMyB,sBAAsBzB,iBAAiB,OAAOA,eAAeD;IAEnE,2EAA2E;IAC3E,2EAA2E;IAC3E,sCAAsC;IACtC,OAAO2B,IAAAA,uBAAgB,EAAC3B,MAAM0B;AAChC;AAEA;;CAEC,GACD,SAASE,OAAO,KAQf;IARe,IAAA,EACdC,WAAW,EACXC,WAAW,EACXC,WAAW,EAKZ,GARe;IASd,MAAM,CAAC7C,OAAOoB,SAAS,GAAG0B,IAAAA,sBAAU,EAACH;IACrC,MAAM,EAAE/C,YAAY,EAAE,GAAGmD,IAAAA,0BAAc,EAAC/C;IACxC,mEAAmE;IACnE,MAAM,EAAEgD,YAAY,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,cAAO,EAAC;QACzC,MAAM9E,MAAM,IAAIQ,IACdgB,cACA,OAAOtB,WAAW,cAAc,aAAaA,OAAOC,QAAQ,CAACC,IAAI;QAGnE,OAAO;YACL,4DAA4D;YAC5DwE,cAAc5E,IAAI4E,YAAY;YAC9BC,UAAUE,IAAAA,wBAAW,EAAC/E,IAAI6E,QAAQ,IAC9BG,IAAAA,8BAAc,EAAChF,IAAI6E,QAAQ,IAC3B7E,IAAI6E,QAAQ;QAClB;IACF,GAAG;QAACrD;KAAa;IAEjB,MAAMyD,yBAAyBlC,0BAA0BC;IACzD,MAAMkC,WAAW3B,YAAYP;IAC7BmC,IAAAA,wCAAyB,EAACnC;IAE1B;;GAEC,GACD,MAAMoC,YAAYN,IAAAA,cAAO,EAAoB;QAC3C,MAAMO,iBAAoC;YACxCC,MAAM,IAAMpF,OAAOyB,OAAO,CAAC2D,IAAI;YAC/BC,SAAS,IAAMrF,OAAOyB,OAAO,CAAC4D,OAAO;YACrCC,UAAU5E,QAAQC,GAAG,CAACuB,2BAA2B,GAE7C,oEAAoE;YACpE,iDAAiD;YACjD,CAAChC,MAAMqF,UACLC,IAAAA,sBAAwB,EACtBtF,MACAmE,YAAY3C,KAAK,CAACU,OAAO,EACzBiC,YAAY3C,KAAK,CAACN,IAAI,EACtBmE,CAAAA,2BAAAA,QAASE,IAAI,MAAKC,gCAAY,CAACC,IAAI,IAEvC,CAACzF,MAAMqF;gBACL,uCAAuC;gBACvC,MAAMzF,MAAMJ,kBAAkBQ;gBAC9B,IAAIJ,QAAQ,MAAM;wBAURyF;oBATR,4DAA4D;oBAC5D,+DAA+D;oBAC/D,iEAAiE;oBACjE,gDAAgD;oBAChD,oDAAoD;oBACpD,kBAAkB;oBAClBK,IAAAA,gCAAe,EAACvB,YAAY3C,KAAK,EAAE;wBACjCyB,MAAM0C,mCAAe;wBACrB/F;wBACA2F,MAAMF,CAAAA,gBAAAA,2BAAAA,QAASE,IAAI,YAAbF,gBAAiBG,gCAAY,CAACC,IAAI;oBAC1C;gBACF;YACF;YACJG,SAAS,CAAC5F,MAAMqF;oBAAAA,oBAAAA,UAAU,CAAC;gBACzBrC,IAAAA,sBAAe,EAAC;wBACYqC;oBAA1BP,SAAS9E,MAAM,WAAWqF,CAAAA,kBAAAA,QAAQQ,MAAM,YAAdR,kBAAkB;gBAC9C;YACF;YACAS,MAAM,CAAC9F,MAAMqF;oBAAAA,oBAAAA,UAAU,CAAC;gBACtBrC,IAAAA,sBAAe,EAAC;wBACSqC;oBAAvBP,SAAS9E,MAAM,QAAQqF,CAAAA,kBAAAA,QAAQQ,MAAM,YAAdR,kBAAkB;gBAC3C;YACF;YACAU,SAAS;gBACP/C,IAAAA,sBAAe,EAAC;oBACdJ,SAAS;wBACPK,MAAM+C,kCAAc;wBACpBnG,QAAQC,OAAOC,QAAQ,CAACF,MAAM;oBAChC;gBACF;YACF;YACAoG,YAAY;gBACV,IAAIzF,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;oBAC1C,MAAM,qBAEL,CAFK,IAAIH,MACR,iFADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF,OAAO;oBACLyC,IAAAA,sBAAe,EAAC;wBACdJ,SAAS;4BACPK,MAAMiD,sCAAkB;4BACxBrG,QAAQC,OAAOC,QAAQ,CAACF,MAAM;wBAChC;oBACF;gBACF;YACF;QACF;QAEA,OAAOoF;IACT,GAAG;QAACd;QAAavB;QAAUkC;KAAS;IAEpC/C,IAAAA,gBAAS,EAAC;QACR,gEAAgE;QAChE,IAAIjC,OAAOiB,IAAI,EAAE;YACfjB,OAAOiB,IAAI,CAACoF,MAAM,GAAGnB;QACvB;IACF,GAAG;QAACA;KAAU;IAEd,IAAIxE,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,sDAAsD;QACtD,MAAM,EAAE0F,KAAK,EAAEC,aAAa,EAAEnF,IAAI,EAAE,GAAGqD,IAAAA,0BAAc,EAAC/C;QAEtD,4FAA4F;QAC5F,sDAAsD;QACtDO,IAAAA,gBAAS,EAAC;YACR,0CAA0C;YAC1C,uGAAuG;YACvG,mCAAmC;YACnCjC,OAAOwG,EAAE,GAAG;gBACVH,QAAQnB;gBACRoB;gBACAC;gBACAnF;YACF;QACF,GAAG;YAAC8D;YAAWoB;YAAOC;YAAenF;SAAK;IAC5C;IAEAa,IAAAA,gBAAS,EAAC;QACR,0DAA0D;QAC1D,uFAAuF;QACvF,qEAAqE;QACrE,wGAAwG;QACxG,SAASwE,eAAeC,KAA0B;gBAG7C1G;YAFH,IACE,CAAC0G,MAAMC,SAAS,IAChB,GAAC3G,wBAAAA,OAAOyB,OAAO,CAACC,KAAK,qBAApB1B,sBAAsB4B,+BAA+B,GACtD;gBACA;YACF;YAEA,uGAAuG;YACvG,qHAAqH;YACrH,8BAA8B;YAC9BhC,cAAcgH,cAAc,GAAGzF;YAE/B2B,SAAS;gBACPK,MAAM0D,kCAAc;gBACpB/G,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACC,IAAI;gBACjCkB,MAAMpB,OAAOyB,OAAO,CAACC,KAAK,CAACE,+BAA+B;YAC5D;QACF;QAEA5B,OAAO8G,gBAAgB,CAAC,YAAYL;QAEpC,OAAO;YACLzG,OAAO+G,mBAAmB,CAAC,YAAYN;QACzC;IACF,GAAG;QAAC3D;KAAS;IAEbb,IAAAA,gBAAS,EAAC;QACR,iFAAiF;QACjF,wCAAwC;QACxC,SAAS+E,wBACPN,KAAyC;YAEzC,MAAMO,QAAQ,YAAYP,QAAQA,MAAMQ,MAAM,GAAGR,MAAMO,KAAK;YAC5D,IAAIE,IAAAA,8BAAe,EAACF,QAAQ;gBAC1BP,MAAMU,cAAc;gBACpB,MAAMtH,MAAMuH,IAAAA,iCAAuB,EAACJ;gBACpC,MAAMK,eAAeC,IAAAA,kCAAwB,EAACN;gBAC9C,IAAIK,iBAAiBE,2BAAY,CAACxB,IAAI,EAAE;oBACtCd,UAAUc,IAAI,CAAClG,KAAK,CAAC;gBACvB,OAAO;oBACLoF,UAAUY,OAAO,CAAChG,KAAK,CAAC;gBAC1B;YACF;QACF;QACAE,OAAO8G,gBAAgB,CAAC,SAASE;QACjChH,OAAO8G,gBAAgB,CAAC,sBAAsBE;QAE9C,OAAO;YACLhH,OAAO+G,mBAAmB,CAAC,SAASC;YACpChH,OAAO+G,mBAAmB,CAAC,sBAAsBC;QACnD;IACF,GAAG;QAAC9B;KAAU;IAEd,sEAAsE;IACtE,0EAA0E;IAC1E,wEAAwE;IACxE,6EAA6E;IAC7E,YAAY;IACZ,EAAE;IACF,sEAAsE;IACtE,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,MAAM,EAAE7D,OAAO,EAAE,GAAGoD,IAAAA,0BAAc,EAAC/C;IACnC,IAAIL,QAAQoG,aAAa,EAAE;QACzB,gHAAgH;QAChH,IAAI7H,cAAcgH,cAAc,KAAKtF,cAAc;YACjD,MAAMrB,YAAWD,OAAOC,QAAQ;YAChC,IAAIoB,QAAQQ,WAAW,EAAE;gBACvB5B,UAASyH,MAAM,CAACpG;YAClB,OAAO;gBACLrB,UAAS6F,OAAO,CAACxE;YACnB;YAEA1B,cAAcgH,cAAc,GAAGtF;QACjC;QACA,mEAAmE;QACnE,4EAA4E;QAC5E,+BAA+B;QAC/BqG,IAAAA,UAAG,EAACC,sCAAkB;IACxB;IAEA3F,IAAAA,gBAAS,EAAC;QACR,MAAM4F,oBAAoB7H,OAAOyB,OAAO,CAACM,SAAS,CAAC+F,IAAI,CAAC9H,OAAOyB,OAAO;QACtE,MAAMsG,uBAAuB/H,OAAOyB,OAAO,CAACO,YAAY,CAAC8F,IAAI,CAC3D9H,OAAOyB,OAAO;QAGhB,wJAAwJ;QACxJ,MAAMuG,iCAAiC,CACrClI;gBAIEE;YAFF,MAAME,OAAOF,OAAOC,QAAQ,CAACC,IAAI;YACjC,MAAMkB,QACJpB,wBAAAA,OAAOyB,OAAO,CAACC,KAAK,qBAApB1B,sBAAsB4B,+BAA+B;YAEvDsB,IAAAA,sBAAe,EAAC;gBACdJ,SAAS;oBACPK,MAAM0D,kCAAc;oBACpB/G,KAAK,IAAIQ,IAAIR,cAAAA,MAAOI,MAAMA;oBAC1BkB;gBACF;YACF;QACF;QAEA;;;;KAIC,GACDpB,OAAOyB,OAAO,CAACM,SAAS,GAAG,SAASA,UAClC+B,IAAS,EACTmE,OAAe,EACfnI,GAAyB;YAEzB,qEAAqE;YACrE,IAAIgE,CAAAA,wBAAAA,KAAMnC,IAAI,MAAImC,wBAAAA,KAAMoE,EAAE,GAAE;gBAC1B,OAAOL,kBAAkB/D,MAAMmE,SAASnI;YAC1C;YAEAgE,OAAOD,+BAA+BC;YAEtC,IAAIhE,KAAK;gBACPkI,+BAA+BlI;YACjC;YAEA,OAAO+H,kBAAkB/D,MAAMmE,SAASnI;QAC1C;QAEA;;;;KAIC,GACDE,OAAOyB,OAAO,CAACO,YAAY,GAAG,SAASA,aACrC8B,IAAS,EACTmE,OAAe,EACfnI,GAAyB;YAEzB,qEAAqE;YACrE,IAAIgE,CAAAA,wBAAAA,KAAMnC,IAAI,MAAImC,wBAAAA,KAAMoE,EAAE,GAAE;gBAC1B,OAAOH,qBAAqBjE,MAAMmE,SAASnI;YAC7C;YACAgE,OAAOD,+BAA+BC;YAEtC,IAAIhE,KAAK;gBACPkI,+BAA+BlI;YACjC;YACA,OAAOiI,qBAAqBjE,MAAMmE,SAASnI;QAC7C;QAEA;;;;KAIC,GACD,MAAMqI,aAAa,CAACzB;YAClB,IAAI,CAACA,MAAMhF,KAAK,EAAE;gBAChB,+IAA+I;gBAC/I;YACF;YAEA,6EAA6E;YAC7E,IAAI,CAACgF,MAAMhF,KAAK,CAACC,IAAI,EAAE;gBACrB3B,OAAOC,QAAQ,CAACmI,MAAM;gBACtB;YACF;YAEA,gHAAgH;YAChH,oEAAoE;YACpElF,IAAAA,sBAAe,EAAC;gBACdJ,SAAS;oBACPK,MAAM0D,kCAAc;oBACpB/G,KAAK,IAAIQ,IAAIN,OAAOC,QAAQ,CAACC,IAAI;oBACjCkB,MAAMsF,MAAMhF,KAAK,CAACE,+BAA+B;gBACnD;YACF;QACF;QAEA,8CAA8C;QAC9C5B,OAAO8G,gBAAgB,CAAC,YAAYqB;QACpC,OAAO;YACLnI,OAAOyB,OAAO,CAACM,SAAS,GAAG8F;YAC3B7H,OAAOyB,OAAO,CAACO,YAAY,GAAG+F;YAC9B/H,OAAO+G,mBAAmB,CAAC,YAAYoB;QACzC;IACF,GAAG;QAACrF;KAAS;IAEb,MAAM,EAAEwD,KAAK,EAAElF,IAAI,EAAEgB,OAAO,EAAEiG,iBAAiB,EAAE,GAAG5D,IAAAA,0BAAc,EAAC/C;IAEnE,MAAM4G,eAAe1D,IAAAA,cAAO,EAAC;QAC3B,OAAO2D,IAAAA,gCAAe,EAACjC,OAAOlF,IAAI,CAAC,EAAE;IACvC,GAAG;QAACkF;QAAOlF;KAAK;IAEhB,yCAAyC;IACzC,MAAMoH,aAAa5D,IAAAA,cAAO,EAAC;QACzB,OAAO6D,IAAAA,qCAAiB,EAACrH;IAC3B,GAAG;QAACA;KAAK;IAET,MAAMsH,sBAAsB9D,IAAAA,cAAO,EAAC;QAClC,OAAO;YACL+D,YAAYvH;YACZwH,iBAAiBtC;YACjBuC,mBAAmB;YACnB,6BAA6B;YAC7B,8EAA8E;YAC9E/I,KAAKwB;QACP;IACF,GAAG;QAACF;QAAMkF;QAAOhF;KAAa;IAE9B,MAAMwH,4BAA4BlE,IAAAA,cAAO,EAAC;QACxC,OAAO;YACLG;YACA3D;YACAiH;YACAjG;QACF;IACF,GAAG;QAAC2C;QAAwB3D;QAAMiH;QAAmBjG;KAAQ;IAE7D,IAAII;IACJ,IAAI8F,iBAAiB,MAAM;QACzB,0DAA0D;QAC1D,0EAA0E;QAC1E,oEAAoE;QACpE,EAAE;QACF,wEAAwE;QACxE,uBAAuB;QACvB,MAAM,CAACrE,eAAe8E,QAAQ,GAAGT;QACjC9F,qBAAO,qBAACwB;YAAmBC,eAAeA;WAAxB8E;IACpB,OAAO;QACLvG,OAAO;IACT;IAEA,IAAIwG,wBACF,sBAACC,kCAAgB;;YACdzG;YACA8D,MAAMhE,GAAG;0BACV,qBAAC4G,sCAAkB;gBAAC9H,MAAMA;;;;IAI9B,IAAIV,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;QACzC,kEAAkE;QAClE,iGAAiG;QACjG,iBAAiB;QACjB,8CAA8C;QAC9C,wBAAwB;QACxB,kEAAkE;QAClE,IAAI,OAAOZ,WAAW,aAAa;YACjC,MAAM,EAAEmJ,iCAAiC,EAAE,GACzCC,QAAQ;YACVJ,wBACE,qBAACG;0BACEH;;QAGP;QACA,MAAMK,cACJD,QAAQ,+CAA+CE,OAAO;QAEhEN,wBACE,qBAACK;YAAY/E,aAAaA;YAAaC,aAAaA;sBACjDyE;;IAGP,OAAO;QACL,0EAA0E;QAC1EA,wBACE,qBAACO,4BAAa;YACZC,gBAAgBjF,WAAW,CAAC,EAAE;YAC9BkF,aAAalF,WAAW,CAAC,EAAE;sBAE1ByE;;IAGP;IAEA,qBACE;;0BACE,qBAACnI;gBAAeC,gBAAgB2D,IAAAA,0BAAc,EAAC/C;;0BAC/C,qBAACgI;0BACD,qBAACC,kDAAiB,CAACC,QAAQ;gBAACC,OAAOrB;0BACjC,cAAA,qBAACsB,gDAAe,CAACF,QAAQ;oBAACC,OAAOlF;8BAC/B,cAAA,qBAACoF,oDAAmB,CAACH,QAAQ;wBAACC,OAAOnF;kCACnC,cAAA,qBAACsF,wDAAyB,CAACJ,QAAQ;4BACjCC,OAAOf;sCAEP,cAAA,qBAACmB,+CAAgB,CAACL,QAAQ;gCAACC,OAAO3E;0CAChC,cAAA,qBAACgF,kDAAmB,CAACN,QAAQ;oCAACC,OAAOnB;8CAClCM;;;;;;;;;AASnB;AAEe,SAASrJ,UAAU,KAQjC;IARiC,IAAA,EAChC0E,WAAW,EACX8F,+BAA+B,CAACC,sBAAsBC,kBAAkB,EACxE/F,WAAW,EAKZ,GARiC;IAShCgG,IAAAA,uCAAoB;IAEpB,qBACE,qBAACf,4BAAa;QACZ,sFAAsF;QACtF,uGAAuG;QACvGC,gBAAgBe,sBAAkB;kBAElC,cAAA,qBAACnG;YACCC,aAAaA;YACbC,aAAaA;YACbC,aAAa;gBAAC6F;gBAAsBC;aAAkB;;;AAI9D;AAEA,MAAMG,gBAAgB,IAAIC;AAC1B,IAAIC,sBAAsB,IAAID;AAE9BE,WAAWC,eAAe,GAAG,SAAU1K,IAAY;IACjD,IAAI2K,MAAML,cAAcM,IAAI;IAC5BN,cAAcO,GAAG,CAAC7K;IAClB,IAAIsK,cAAcM,IAAI,KAAKD,KAAK;QAC9BH,oBAAoBM,OAAO,CAAC,CAACC,KAAOA;IACtC;IACA,4CAA4C;IAC5C,gFAAgF;IAChF,OAAOC,QAAQC,OAAO;AACxB;AAEA,SAASzB;IACP,MAAM,GAAG0B,YAAY,GAAGC,cAAK,CAACC,QAAQ,CAAC;IACvC,MAAMC,qBAAqBf,cAAcM,IAAI;IAC7C7I,IAAAA,gBAAS,EAAC;QACR,MAAMuJ,UAAU,IAAMJ,YAAY,CAACK,IAAMA,IAAI;QAC7Cf,oBAAoBK,GAAG,CAACS;QACxB,IAAID,uBAAuBf,cAAcM,IAAI,EAAE;YAC7CU;QACF;QACA,OAAO;YACLd,oBAAoBgB,MAAM,CAACF;QAC7B;IACF,GAAG;QAACD;QAAoBH;KAAY;IAEpC,MAAMO,QAAQjL,QAAQC,GAAG,CAACiL,kBAAkB,GACxC,AAAC,UAAOlL,QAAQC,GAAG,CAACiL,kBAAkB,GACtC;IACJ,OAAO;WAAIpB;KAAc,CAACqB,GAAG,CAAC,CAAC3L,MAAM4L,kBACnC,qBAACC;YAECC,KAAI;YACJ9L,MAAM,AAAC,KAAEA,OAAOyL;YAChB,aAAa;YACbM,YAAW;WAJNH;AAUX"}