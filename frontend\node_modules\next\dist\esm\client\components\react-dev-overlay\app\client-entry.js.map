{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/app/client-entry.tsx"], "sourcesContent": ["import React from 'react'\nimport { AppDevOverlay } from './app-dev-overlay'\nimport { getSocketUrl } from '../utils/get-socket-url'\nimport { INITIAL_OVERLAY_STATE } from '../shared'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport GlobalError from '../../error-boundary'\n\n// if an error is thrown while rendering an RSC stream, this will catch it in dev\n// and show the error overlay\nexport function createRootLevelDevOverlayElement(reactEl: React.ReactElement) {\n  const rootLayoutMissingTags = window.__next_root_layout_missing_tags\n  const hasMissingTags = !!rootLayoutMissingTags?.length\n  const socketUrl = getSocketUrl(process.env.__NEXT_ASSET_PREFIX || '')\n  const socket = new window.WebSocket(`${socketUrl}/_next/webpack-hmr`)\n\n  // add minimal \"hot reload\" support for RSC errors\n  const handler = (event: MessageEvent) => {\n    let obj\n    try {\n      obj = JSON.parse(event.data)\n    } catch {}\n\n    if (!obj || !('action' in obj)) {\n      return\n    }\n\n    if (obj.action === HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES) {\n      window.location.reload()\n    }\n  }\n\n  socket.addEventListener('message', handler)\n\n  const FallbackLayout = hasMissingTags\n    ? ({ children }: { children: React.ReactNode }) => (\n        <html id=\"__next_error__\">\n          <body>{children}</body>\n        </html>\n      )\n    : React.Fragment\n\n  return (\n    <FallbackLayout>\n      <AppDevOverlay\n        state={{\n          ...INITIAL_OVERLAY_STATE,\n          rootLayoutMissingTags,\n          routerType: 'app',\n        }}\n        globalError={[GlobalError, null]}\n      >\n        {reactEl}\n      </AppDevOverlay>\n    </FallbackLayout>\n  )\n}\n"], "names": ["React", "AppDevOverlay", "getSocketUrl", "INITIAL_OVERLAY_STATE", "HMR_ACTIONS_SENT_TO_BROWSER", "GlobalError", "createRootLevelDevOverlayElement", "reactEl", "rootLayoutMissingTags", "window", "__next_root_layout_missing_tags", "hasMissingTags", "length", "socketUrl", "process", "env", "__NEXT_ASSET_PREFIX", "socket", "WebSocket", "handler", "event", "obj", "JSON", "parse", "data", "action", "SERVER_COMPONENT_CHANGES", "location", "reload", "addEventListener", "FallbackLayout", "children", "html", "id", "body", "Fragment", "state", "routerType", "globalError"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SAASC,aAAa,QAAQ,oBAAmB;AACjD,SAASC,YAAY,QAAQ,0BAAyB;AACtD,SAASC,qBAAqB,QAAQ,YAAW;AACjD,SAASC,2BAA2B,QAAQ,4CAA2C;AACvF,OAAOC,iBAAiB,uBAAsB;AAE9C,iFAAiF;AACjF,6BAA6B;AAC7B,OAAO,SAASC,iCAAiCC,OAA2B;IAC1E,MAAMC,wBAAwBC,OAAOC,+BAA+B;IACpE,MAAMC,iBAAiB,CAAC,EAACH,yCAAAA,sBAAuBI,MAAM;IACtD,MAAMC,YAAYX,aAAaY,QAAQC,GAAG,CAACC,mBAAmB,IAAI;IAClE,MAAMC,SAAS,IAAIR,OAAOS,SAAS,CAAC,AAAC,KAAEL,YAAU;IAEjD,kDAAkD;IAClD,MAAMM,UAAU,CAACC;QACf,IAAIC;QACJ,IAAI;YACFA,MAAMC,KAAKC,KAAK,CAACH,MAAMI,IAAI;QAC7B,EAAE,UAAM,CAAC;QAET,IAAI,CAACH,OAAO,CAAE,CAAA,YAAYA,GAAE,GAAI;YAC9B;QACF;QAEA,IAAIA,IAAII,MAAM,KAAKrB,4BAA4BsB,wBAAwB,EAAE;YACvEjB,OAAOkB,QAAQ,CAACC,MAAM;QACxB;IACF;IAEAX,OAAOY,gBAAgB,CAAC,WAAWV;IAEnC,MAAMW,iBAAiBnB,iBACnB;YAAC,EAAEoB,QAAQ,EAAiC;6BAC1C,KAACC;YAAKC,IAAG;sBACP,cAAA,KAACC;0BAAMH;;;QAGX/B,MAAMmC,QAAQ;IAElB,qBACE,KAACL;kBACC,cAAA,KAAC7B;YACCmC,OAAO;gBACL,GAAGjC,qBAAqB;gBACxBK;gBACA6B,YAAY;YACd;YACAC,aAAa;gBAACjC;gBAAa;aAAK;sBAE/BE;;;AAIT"}