"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-latest";
exports.ids = ["vendor-chunks/use-latest"];
exports.modules = {

/***/ "(ssr)/./node_modules/use-latest/dist/use-latest.esm.js":
/*!********************************************************!*\
  !*** ./node_modules/use-latest/dist/use-latest.esm.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ useLatest)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! use-isomorphic-layout-effect */ \"(ssr)/./node_modules/use-isomorphic-layout-effect/dist/use-isomorphic-layout-effect.esm.js\");\n\n\n\nvar useLatest = function useLatest(value) {\n  var ref = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(value);\n  (0,use_isomorphic_layout_effect__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(function () {\n    ref.current = value;\n  });\n  return ref;\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXNlLWxhdGVzdC9kaXN0L3VzZS1sYXRlc3QuZXNtLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEI7QUFDMkM7O0FBRXJFO0FBQ0EsWUFBWSxtREFBWTtBQUN4QixFQUFFLHdFQUF5QjtBQUMzQjtBQUNBLEdBQUc7QUFDSDtBQUNBOztBQUVnQyIsInNvdXJjZXMiOlsiRzpcXEdyYWR1YXRpb24gcHJvamVjdCAyMDI1XFxhcHAgKDIpXFxhcHBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHVzZS1sYXRlc3RcXGRpc3RcXHVzZS1sYXRlc3QuZXNtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdCBmcm9tICd1c2UtaXNvbW9ycGhpYy1sYXlvdXQtZWZmZWN0JztcblxudmFyIHVzZUxhdGVzdCA9IGZ1bmN0aW9uIHVzZUxhdGVzdCh2YWx1ZSkge1xuICB2YXIgcmVmID0gUmVhY3QudXNlUmVmKHZhbHVlKTtcbiAgdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdChmdW5jdGlvbiAoKSB7XG4gICAgcmVmLmN1cnJlbnQgPSB2YWx1ZTtcbiAgfSk7XG4gIHJldHVybiByZWY7XG59O1xuXG5leHBvcnQgeyB1c2VMYXRlc3QgYXMgZGVmYXVsdCB9O1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/use-latest/dist/use-latest.esm.js\n");

/***/ })

};
;