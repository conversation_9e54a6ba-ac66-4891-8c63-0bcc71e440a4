"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/scroll-into-view-if-needed";
exports.ids = ["vendor-chunks/scroll-into-view-if-needed"];
exports.modules = {

/***/ "(ssr)/./node_modules/scroll-into-view-if-needed/dist/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/scroll-into-view-if-needed/dist/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ t)\n/* harmony export */ });\n/* harmony import */ var compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! compute-scroll-into-view */ \"(ssr)/./node_modules/compute-scroll-into-view/dist/index.js\");\nconst o=e=>!1===e?{block:\"end\",inline:\"nearest\"}:(e=>e===Object(e)&&0!==Object.keys(e).length)(e)?e:{block:\"start\",inline:\"nearest\"};function t(t,n){if(!t.isConnected||!(e=>{let o=e;for(;o&&o.parentNode;){if(o.parentNode===document)return!0;o=o.parentNode instanceof ShadowRoot?o.parentNode.host:o.parentNode}return!1})(t))return;if((e=>\"object\"==typeof e&&\"function\"==typeof e.behavior)(n))return n.behavior((0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__.compute)(t,n));const r=\"boolean\"==typeof n||null==n?void 0:n.behavior;for(const{el:i,top:a,left:l}of (0,compute_scroll_into_view__WEBPACK_IMPORTED_MODULE_0__.compute)(t,o(n)))i.scroll({top:a,left:l,behavior:r})}//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvc2Nyb2xsLWludG8tdmlldy1pZi1uZWVkZWQvZGlzdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFtRCxtQkFBbUIsNkJBQTZCLHFEQUFxRCxnQ0FBZ0MsZ0JBQWdCLHlCQUF5QixRQUFRLEtBQUssZ0JBQWdCLEVBQUUsb0NBQW9DLG9FQUFvRSxTQUFTLFlBQVksK0VBQStFLGlFQUFDLE9BQU8sdURBQXVELFVBQVUsa0JBQWtCLEdBQUcsaUVBQUMsbUJBQW1CLHdCQUF3QixFQUF1QiIsInNvdXJjZXMiOlsiRzpcXEdyYWR1YXRpb24gcHJvamVjdCAyMDI1XFxhcHAgKDIpXFxhcHBcXGZyb250ZW5kXFxub2RlX21vZHVsZXNcXHNjcm9sbC1pbnRvLXZpZXctaWYtbmVlZGVkXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnR7Y29tcHV0ZSBhcyBlfWZyb21cImNvbXB1dGUtc2Nyb2xsLWludG8tdmlld1wiO2NvbnN0IG89ZT0+ITE9PT1lP3tibG9jazpcImVuZFwiLGlubGluZTpcIm5lYXJlc3RcIn06KGU9PmU9PT1PYmplY3QoZSkmJjAhPT1PYmplY3Qua2V5cyhlKS5sZW5ndGgpKGUpP2U6e2Jsb2NrOlwic3RhcnRcIixpbmxpbmU6XCJuZWFyZXN0XCJ9O2Z1bmN0aW9uIHQodCxuKXtpZighdC5pc0Nvbm5lY3RlZHx8IShlPT57bGV0IG89ZTtmb3IoO28mJm8ucGFyZW50Tm9kZTspe2lmKG8ucGFyZW50Tm9kZT09PWRvY3VtZW50KXJldHVybiEwO289by5wYXJlbnROb2RlIGluc3RhbmNlb2YgU2hhZG93Um9vdD9vLnBhcmVudE5vZGUuaG9zdDpvLnBhcmVudE5vZGV9cmV0dXJuITF9KSh0KSlyZXR1cm47aWYoKGU9Plwib2JqZWN0XCI9PXR5cGVvZiBlJiZcImZ1bmN0aW9uXCI9PXR5cGVvZiBlLmJlaGF2aW9yKShuKSlyZXR1cm4gbi5iZWhhdmlvcihlKHQsbikpO2NvbnN0IHI9XCJib29sZWFuXCI9PXR5cGVvZiBufHxudWxsPT1uP3ZvaWQgMDpuLmJlaGF2aW9yO2Zvcihjb25zdHtlbDppLHRvcDphLGxlZnQ6bH1vZiBlKHQsbyhuKSkpaS5zY3JvbGwoe3RvcDphLGxlZnQ6bCxiZWhhdmlvcjpyfSl9ZXhwb3J0e3QgYXMgZGVmYXVsdH07Ly8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/scroll-into-view-if-needed/dist/index.js\n");

/***/ })

};
;