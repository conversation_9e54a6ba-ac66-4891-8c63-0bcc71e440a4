(()=>{var e={};e.id=27,e.ids=[27],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22179:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=r(65239),s=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["(home)",{children:["previous",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,88681)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\previous\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,89282)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout.tsx"],error:[()=>Promise.resolve().then(r.bind(r,54431)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\previous\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(home)/previous/page",pathname:"/previous",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48454:(e,t,r)=>{Promise.resolve().then(r.bind(r,59632))},59632:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>H});var a=r(60687),s=r(43210),n=r(63227),o=r(31390),i=r(71496),l=r(36424),d=r(63257),c=r(86760),u=r(72199),m=r(26109),p=r(54514),h=(0,m.Rf)((e,t)=>{let{as:r,activePage:s,...n}=e,o=(0,p.zD)(t);return(0,a.jsx)(r||"span",{ref:o,"aria-hidden":!0,...n,children:s})});h.displayName="NextUI.PaginationCursor";var v=r(1172),x=r(16060),f=r(66775),g=r(25381),b=r(72406),w=r(73094),y=r(58285),j=r(40182),N=r(6409),k=(0,m.Rf)((e,t)=>{let{Component:r,children:n,getItemProps:o}=function(e){let{as:t,ref:r,value:a,children:n,isActive:o,isDisabled:i,onPress:l,onClick:d,getAriaLabel:c,className:u,...m}=e,h=!!(null==e?void 0:e.href),k=t||h?"a":"li",P="string"==typeof k,A=(0,p.zD)(r),C=(0,f.rd)(),E=(0,s.useMemo)(()=>o?`${null==c?void 0:c(a)} active`:null==c?void 0:c(a),[a,o]),{isPressed:S,pressProps:G}=(0,y.d)({isDisabled:i,onPress:l}),{focusProps:I,isFocused:_,isFocusVisible:R}=(0,N.o)({}),{isHovered:T,hoverProps:D}=(0,j.M)({isDisabled:i});return{Component:k,children:n,ariaLabel:E,isFocused:_,isFocusVisible:R,getItemProps:(e={})=>({ref:A,role:"button",tabIndex:i?-1:0,"aria-label":E,"aria-current":(0,v.sE)(o),"aria-disabled":(0,v.sE)(i),"data-disabled":(0,v.sE)(i),"data-active":(0,v.sE)(o),"data-focus":(0,v.sE)(_),"data-hover":(0,v.sE)(T),"data-pressed":(0,v.sE)(S),"data-focus-visible":(0,v.sE)(R),...(0,g.v)(e,G,I,D,(0,w.$)(m,{enabled:P})),className:(0,x.$)(u,e.className),onClick:t=>{(0,b.c)(null==G?void 0:G.onClick,d)(t),!C.isNative&&t.currentTarget instanceof HTMLAnchorElement&&t.currentTarget.href&&!t.isDefaultPrevented()&&(0,f.sU)(t.currentTarget,t)&&e.href&&(t.preventDefault(),C.open(t.currentTarget,t,e.href,e.routerOptions))}})}}({...e,ref:t});return(0,a.jsx)(r,{...o(),children:n})});k.displayName="NextUI.PaginationItem";var P=r(82432),A=r(30900),C=r(10334),E=(e=>(e.DOTS="dots",e.PREV="prev",e.NEXT="next",e))(E||{}),S=r(55150);r(46474);var G=r(85044),I=r(72926),_=r(65146),R=(0,I.tv)({slots:{base:["p-2.5","-m-2.5","overflow-x-scroll","scrollbar-hide"],wrapper:["flex","flex-nowrap","h-fit","max-w-fit","relative","gap-1","items-center","overflow-visible"],item:["tap-highlight-transparent","select-none","touch-none"],prev:"",next:"",cursor:["absolute","flex","overflow-visible","items-center","justify-center","origin-center","left-0","select-none","touch-none","pointer-events-none","z-20"],forwardIcon:["hidden","group-hover:block","group-data-[focus-visible=true]:block","data-[before=true]:rotate-180"],ellipsis:"group-hover:hidden group-data-[focus-visible=true]:hidden",chevronNext:"rotate-180"},variants:{variant:{bordered:{item:["border-medium","border-default","bg-transparent","data-[hover=true]:bg-default-100"]},light:{item:"bg-transparent"},flat:{},faded:{item:["border-medium","border-default"]}},color:{default:{cursor:G.k.solid.default},primary:{cursor:G.k.solid.primary},secondary:{cursor:G.k.solid.secondary},success:{cursor:G.k.solid.success},warning:{cursor:G.k.solid.warning},danger:{cursor:G.k.solid.danger}},size:{sm:{},md:{},lg:{}},radius:{none:{},sm:{},md:{},lg:{},full:{}},isCompact:{true:{wrapper:"gap-0 shadow-sm",item:["shadow-none","first-of-type:rounded-e-none","last-of-type:rounded-s-none","[&:not(:first-of-type):not(:last-of-type)]:rounded-none"],prev:"!rounded-e-none",next:"!rounded-s-none"}},isDisabled:{true:{base:"opacity-disabled pointer-events-none"}},showShadow:{true:{}},disableCursorAnimation:{true:{cursor:"hidden"}},disableAnimation:{true:{item:"transition-none",cursor:"transition-none"},false:{item:["data-[pressed=true]:scale-[0.97]","transition-transform-background"],cursor:["data-[moving=true]:transition-transform","!data-[moving=true]:duration-300","opacity-0","data-[moving]:opacity-100"]}}},defaultVariants:{variant:"flat",color:"primary",size:"md",radius:"md",isCompact:!1,isDisabled:!1,showShadow:!1,disableCursorAnimation:!1},compoundVariants:[{showShadow:!0,color:"default",class:{cursor:[G.k.shadow.default,"shadow-md"]}},{showShadow:!0,color:"primary",class:{cursor:[G.k.shadow.primary,"shadow-md"]}},{showShadow:!0,color:"secondary",class:{cursor:[G.k.shadow.secondary,"shadow-md"]}},{showShadow:!0,color:"success",class:{cursor:[G.k.shadow.success,"shadow-md"]}},{showShadow:!0,color:"warning",class:{cursor:[G.k.shadow.warning,"shadow-md"]}},{showShadow:!0,color:"danger",class:{cursor:[G.k.shadow.danger,"shadow-md"]}},{isCompact:!0,variant:"bordered",class:{item:"[&:not(:first-of-type)]:ms-[calc(theme(borderWidth.2)*-1)]"}},{disableCursorAnimation:!0,color:"default",class:{item:["data-[active=true]:bg-default-400","data-[active=true]:border-default-400","data-[active=true]:text-default-foreground"]}},{disableCursorAnimation:!0,color:"primary",class:{item:["data-[active=true]:bg-primary","data-[active=true]:border-primary","data-[active=true]:text-primary-foreground"]}},{disableCursorAnimation:!0,color:"secondary",class:{item:["data-[active=true]:bg-secondary","data-[active=true]:border-secondary","data-[active=true]:text-secondary-foreground"]}},{disableCursorAnimation:!0,color:"success",class:{item:["data-[active=true]:bg-success","data-[active=true]:border-success","data-[active=true]:text-success-foreground"]}},{disableCursorAnimation:!0,color:"warning",class:{item:["data-[active=true]:bg-warning","data-[active=true]:border-warning","data-[active=true]:text-warning-foreground"]}},{disableCursorAnimation:!0,color:"danger",class:{item:["data-[active=true]:bg-danger","data-[active=true]:border-danger","data-[active=true]:text-danger-foreground"]}},{disableCursorAnimation:!0,showShadow:!0,color:"default",class:{item:["data-[active=true]:shadow-md","data-[active=true]:shadow-default/50"]}},{disableCursorAnimation:!0,showShadow:!0,color:"primary",class:{item:["data-[active=true]:shadow-md","data-[active=true]:shadow-primary/40"]}},{disableCursorAnimation:!0,showShadow:!0,color:"secondary",class:{item:["data-[active=true]:shadow-md","data-[active=true]:shadow-secondary/40"]}},{disableCursorAnimation:!0,showShadow:!0,color:"success",class:{item:["data-[active=true]:shadow-md","data-[active=true]:shadow-success/40"]}},{disableCursorAnimation:!0,showShadow:!0,color:"warning",class:{item:["data-[active=true]:shadow-md","data-[active=true]:shadow-warning/40"]}},{disableCursorAnimation:!0,showShadow:!0,color:"danger",class:{item:["data-[active=true]:shadow-md","data-[active=true]:shadow-danger/40"]}}],compoundSlots:[{slots:["item","prev","next"],class:["flex","flex-wrap","truncate","box-border","outline-none","items-center","justify-center","text-default-foreground",..._.zb,"data-[disabled=true]:text-default-300","data-[disabled=true]:pointer-events-none"]},{slots:["item","prev","next"],variant:["flat","bordered","faded"],class:["shadow-sm"]},{slots:["item","prev","next"],variant:"flat",class:["bg-default-100","[&[data-hover=true]:not([data-active=true])]:bg-default-200","active:bg-default-300"]},{slots:["item","prev","next"],variant:"faded",class:["bg-default-50","[&[data-hover=true]:not([data-active=true])]:bg-default-100","active:bg-default-200"]},{slots:["item","prev","next"],variant:"light",class:["[&[data-hover=true]:not([data-active=true])]:bg-default-100","active:bg-default-200"]},{slots:["item","cursor","prev","next"],size:"sm",class:"min-w-8 w-8 h-8 text-tiny"},{slots:["item","cursor","prev","next"],size:"md",class:"min-w-9 w-9 h-9 text-small"},{slots:["item","cursor","prev","next"],size:"lg",class:"min-w-10 w-10 h-10 text-medium"},{slots:["wrapper","item","cursor","prev","next"],radius:"none",class:"rounded-none"},{slots:["wrapper","item","cursor","prev","next"],radius:"sm",class:"rounded-small"},{slots:["wrapper","item","cursor","prev","next"],radius:"md",class:"rounded-medium"},{slots:["wrapper","item","cursor","prev","next"],radius:"lg",class:"rounded-large"},{slots:["wrapper","item","cursor","prev","next"],radius:"full",class:"rounded-full"}]}),T=e=>(0,a.jsx)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",viewBox:"0 0 24 24",width:"1em",...e,children:(0,a.jsx)("path",{d:"M15.5 19l-7-7 7-7",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5"})}),D=e=>(0,a.jsxs)("svg",{"aria-hidden":"true",fill:"none",height:"1em",shapeRendering:"geometricPrecision",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[(0,a.jsx)("circle",{cx:"12",cy:"12",fill:"currentColor",r:"1"}),(0,a.jsx)("circle",{cx:"19",cy:"12",fill:"currentColor",r:"1"}),(0,a.jsx)("circle",{cx:"5",cy:"12",fill:"currentColor",r:"1"})]}),z=e=>(0,a.jsxs)("svg",{"aria-hidden":"true",fill:"none",focusable:"false",height:"1em",role:"presentation",shapeRendering:"geometricPrecision",stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",viewBox:"0 0 24 24",width:"1em",...e,children:[(0,a.jsx)("path",{d:"M13 17l5-5-5-5"}),(0,a.jsx)("path",{d:"M6 17l5-5-5-5"})]}),M=(0,m.Rf)((e,t)=>{let{Component:r,dotsJump:n,slots:o,classNames:i,total:l,range:d,loop:c,activePage:u,disableCursorAnimation:f,disableAnimation:g,renderItem:b,onNext:w,onPrevious:y,setPage:j,getItemAriaLabel:N,getItemRef:G,getBaseProps:I,getWrapperProps:_,getItemProps:M,getCursorProps:$}=function(e){var t,r,a,n;let o=(0,S.o)(),[i,l]=(0,m.rE)(e,R.variantKeys),{as:d,ref:c,classNames:u,dotsJump:h=5,loop:f=!1,showControls:g=!1,total:b=1,initialPage:w=1,page:y,siblings:j,boundaries:N,onChange:k,className:G,renderItem:I,getItemAriaLabel:_,...T}=i,D=(0,p.zD)(c),z=(0,s.useRef)(null),M=(0,s.useRef)();(0,s.useRef)();let{direction:$}=(0,A.Y)(),L="rtl"===$,O=null!=(r=null!=(t=null==e?void 0:e.disableAnimation)?t:null==o?void 0:o.disableAnimation)&&r,q=null!=(n=null!=(a=null==e?void 0:e.disableCursorAnimation)?a:O)&&n;function V(e,t){let r=(M.current||(M.current=new Map),M.current);e?r.set(t,e):r.delete(t)}let{range:X,activePage:U,setPage:W,previous:B,next:F,first:K,last:Y}=function(e){let{page:t,total:r,siblings:a=1,boundaries:n=1,initialPage:o=1,showControls:i=!1,onChange:l}=e,[d,c]=(0,s.useState)(t||o),{direction:u}=(0,A.Y)(),m="rtl"===u,p=e=>{c(e),l&&l(e)};(0,s.useEffect)(()=>{t&&t!==d&&c(t)},[t]);let h=(0,s.useCallback)(e=>{e<=0?p(1):e>r?p(r):p(e)},[r,d,p]),v=(0,s.useCallback)(e=>i?m?["next",...e,"prev"]:["prev",...e,"next"]:e,[m,i]);return{range:(0,s.useMemo)(()=>{if(2*a+3+2*n>=r)return v((0,C.y1)(1,r));let e=Math.max(d-a,n),t=Math.min(d+a,r-n),s=e>n+2,o=t<r-(n+1);if(!s&&o){let e=2*a+n+2;return v([...(0,C.y1)(1,e),"dots",...(0,C.y1)(r-(n-1),r)])}if(s&&!o){let e=n+1+2*a;return v([...(0,C.y1)(1,n),"dots",...(0,C.y1)(r-e,r)])}return v([...(0,C.y1)(1,n),"dots",...(0,C.y1)(e,t),"dots",...(0,C.y1)(r-n+1,r)])},[r,d,a,n,v]),activePage:d,setPage:h,next:()=>m?h(d-1):h(d+1),previous:()=>m?h(d+1):h(d-1),first:()=>m?h(r):h(1),last:()=>m?h(1):h(r)}}({page:y,total:b,initialPage:w,siblings:j,boundaries:N,showControls:g,onChange:k}),[J,H]=function({threshold:e=0,root:t=null,rootMargin:r="0%",isEnabled:a=!0,freezeOnceVisible:n=!1,initialIsIntersecting:o=!1,onChange:i}={}){var l;let[d,c]=(0,s.useState)(null),[u,m]=(0,s.useState)(()=>({isIntersecting:o,entry:void 0})),p=(0,s.useRef)();p.current=i;let h=(null==(l=u.entry)?void 0:l.isIntersecting)&&n;(0,s.useEffect)(()=>{let s;if(!a||!d||!("IntersectionObserver"in window)||h)return;let o=new IntersectionObserver(e=>{let t=Array.isArray(o.thresholds)?o.thresholds:[o.thresholds];e.forEach(e=>{let r=e.isIntersecting&&t.some(t=>e.intersectionRatio>=t);m({isIntersecting:r,entry:e}),p.current&&p.current(r,e),r&&n&&s&&(s(),s=void 0)})},{threshold:e,root:t,rootMargin:r});return o.observe(d),()=>{o.disconnect()}},[d,a,JSON.stringify(e),t,r,h,n]);let v=(0,s.useRef)(null);(0,s.useEffect)(()=>{var e;d||null==(e=u.entry)||!e.target||n||h||v.current===u.entry.target||(v.current=u.entry.target,m({isIntersecting:o,entry:void 0}))},[d,u.entry,n,h,o]);let x=[c,!!u.isIntersecting,u.entry];return x.ref=x[0],x.isIntersecting=x[1],x.entry=x[2],x}();(0,s.useRef)(U);let Z=(0,s.useMemo)(()=>R({...l,disableAnimation:O,disableCursorAnimation:q}),[(0,P.t6)(l),q,O]),Q=(0,x.$)(null==u?void 0:u.base,G);return{Component:d||"nav",showControls:g,dotsJump:h,slots:Z,classNames:u,loop:f,total:b,range:X,activePage:U,getItemRef:V,disableAnimation:O,disableCursorAnimation:q,setPage:W,onPrevious:()=>f&&U===(L?b:1)?Y():B(),onNext:()=>f&&U===(L?1:b)?K():F(),renderItem:I,getBaseProps:(e={})=>({...e,ref:D,role:"navigation","aria-label":e["aria-label"]||"pagination navigation","data-slot":"base","data-controls":(0,v.sE)(g),"data-loop":(0,v.sE)(f),"data-dots-jump":h,"data-total":b,"data-active-page":U,className:Z.base({class:(0,x.$)(Q,null==e?void 0:e.className)}),...T}),getWrapperProps:(e={})=>({...e,"data-slot":"wrapper",className:Z.wrapper({class:(0,x.$)(null==u?void 0:u.wrapper,null==e?void 0:e.className)})}),getItemProps:(e={})=>({...e,ref:t=>V(t,e.value),"data-slot":"item",isActive:e.value===U,className:Z.item({class:(0,x.$)(null==u?void 0:u.item,null==e?void 0:e.className)}),onPress:()=>{e.value!==U&&W(e.value)}}),getCursorProps:(e={})=>({...e,ref:z,activePage:U,"data-slot":"cursor",className:Z.cursor({class:(0,x.$)(null==u?void 0:u.cursor,null==e?void 0:e.className)})}),getItemAriaLabel:e=>{if(e){if(_)return _(e);switch(e){case E.DOTS:return"dots element";case E.PREV:return"previous page button";case E.NEXT:return"next page button";case"first":return"first page button";case"last":return"last page button";default:return`pagination item ${e}`}}}}}({...e,ref:t}),{direction:L}=(0,A.Y)(),O="rtl"===L,q=(0,s.useCallback)((e,t)=>{let r=t<d.indexOf(u);if(b&&"function"==typeof b){let s="number"==typeof e?e:t;e===E.NEXT&&(s=u+1),e===E.PREV&&(s=u-1),e===E.DOTS&&(s=r?u-n>=1?u-n:1:u+n<=l?u+n:l);let d={[E.PREV]:(0,a.jsx)(T,{}),[E.NEXT]:(0,a.jsx)(T,{className:o.chevronNext({class:null==i?void 0:i.chevronNext})}),[E.DOTS]:(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(D,{className:null==o?void 0:o.ellipsis({class:null==i?void 0:i.ellipsis})}),(0,a.jsx)(z,{className:null==o?void 0:o.forwardIcon({class:null==i?void 0:i.forwardIcon}),"data-before":(0,v.sE)(r)})]})};return b({value:e,index:t,key:`${e}-${t}`,page:s,total:l,children:"number"==typeof e?e:d[e],activePage:u,dotsJump:n,isBefore:r,isActive:e===u,isPrevious:e===u-1,isNext:e===u+1,isFirst:1===e,isLast:e===l,onNext:w,onPrevious:y,setPage:j,onPress:()=>j(s),ref:"number"==typeof e?t=>G(t,e):void 0,className:o.item({class:null==i?void 0:i.item}),getAriaLabel:N})}return e===E.PREV?(0,a.jsx)(k,{className:o.prev({class:null==i?void 0:i.prev}),"data-slot":"prev",getAriaLabel:N,isDisabled:!c&&u===(O?l:1),value:e,onPress:y,children:(0,a.jsx)(T,{})},E.PREV):e===E.NEXT?(0,a.jsx)(k,{className:o.next({class:(0,x.$)(null==i?void 0:i.next)}),"data-slot":"next",getAriaLabel:N,isDisabled:!c&&u===(O?1:l),value:e,onPress:w,children:(0,a.jsx)(T,{className:o.chevronNext({class:null==i?void 0:i.chevronNext})})},E.NEXT):e===E.DOTS?(0,a.jsxs)(k,{className:o.item({class:(0,x.$)(null==i?void 0:i.item,"group")}),"data-slot":"item",getAriaLabel:N,value:e,onPress:()=>r?j(u-n>=1?u-n:1):j(u+n<=l?u+n:l),children:[(0,a.jsx)(D,{className:null==o?void 0:o.ellipsis({class:null==i?void 0:i.ellipsis})}),(0,a.jsx)(z,{className:null==o?void 0:o.forwardIcon({class:null==i?void 0:i.forwardIcon}),"data-before":(0,v.sE)(O?!r:r)})]},E.DOTS+r):(0,s.createElement)(k,{...M({value:e}),key:e,getAriaLabel:N},e)},[O,u,n,M,c,d,b,o,i,l]);return(0,a.jsx)(r,{...I(),children:(0,a.jsxs)("ul",{..._(),children:[!f&&!g&&(0,a.jsx)(h,{...$()}),d.map(q)]})})});M.displayName="NextUI.Pagination";var $=r(43649),L=r(67760),O=r(99891),q=r(48730),V=r(58869),X=r(97992),U=r(4780),W=r(71356),B=r(52326);let F=[{key:"all",label:"جميع الإشعارات",icon:$.A},{key:"O",label:"طلب مساعدة",icon:L.A},{key:"M",label:"طبية",icon:O.A},{key:"D",label:"خطر",icon:$.A}],K=e=>{switch(e){case"O":return"طلب مساعدة";case"M":return"طبية";case"D":return"خطر";default:return"غير محدد"}},Y=e=>{switch(e){case"O":return"primary";case"M":return"success";case"D":return"danger";default:return"default"}},J=e=>new Date(e).toLocaleDateString("ar-EG",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"});function H(){let[e,t]=(0,s.useState)([]),[r,m]=(0,s.useState)(!0),[p,h]=(0,s.useState)(null),[v,x]=(0,s.useState)(1),[f,g]=(0,s.useState)(1),[b,w]=(0,s.useState)("all"),[y,j]=(0,s.useState)(0),N=async(e=1,r="all")=>{try{m(!0),h(null);let a=`/emergency/?page=${e}&page_size=12`;"all"!==r&&(a+=`&emergency_type=${r}`);let s=await (0,U.G)(a,null,"GET");if(!s.ok)throw Error(`خطأ في الشبكة: ${s.status}`);let n=await s.text(),o=n?JSON.parse(n):{count:0,results:[],next:null,previous:null};t(o.results||[]),j(o.count||0),g(Math.ceil((o.count||0)/12))}catch(e){console.error("Error fetching notifications:",e),h("حدث خطأ أثناء تحميل الإشعارات. يرجى المحاولة مرة أخرى."),t([])}finally{m(!1)}},k=e=>{w(e),x(1)};return r&&0===e.length?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.o,{size:"lg"}),(0,a.jsx)("p",{className:"mt-4 text-gray-600",children:"جارٍ تحميل الإشعارات..."})]})}):(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 max-w-7xl",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-center mb-2",children:"الإشعارات السابقة"}),(0,a.jsxs)("p",{className:"text-gray-600 text-center",children:["عرض جميع إشعارات الطوارئ المرسلة سابقاً (",y," إشعار)"]})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(o.r,{selectedKey:b,onSelectionChange:e=>k(e),className:"w-full",classNames:{tabList:"w-full",tab:"flex-1"},children:F.map(e=>{let t=e.icon;return(0,a.jsx)(i.i,{title:(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)(t,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:e.label})]})},e.key)})})}),p&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:[(0,a.jsx)("p",{className:"text-red-600 text-center",children:p}),(0,a.jsx)(l.T,{color:"danger",variant:"light",className:"mt-2 mx-auto block",onPress:()=>N(v,b),children:"إعادة المحاولة"})]}),0!==e.length||r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8",children:e.map(e=>(0,a.jsxs)(W.aF,{children:[(0,a.jsx)(W.g6,{children:(0,a.jsx)(d.Z,{className:"hover:shadow-lg transition-shadow cursor-pointer h-full",children:(0,a.jsxs)(c.U,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-3",children:[(0,a.jsx)(u.R,{color:Y(e.emergency_type),size:"sm",variant:"flat",children:K(e.emergency_type)}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,a.jsx)(q.A,{className:"w-3 h-3"}),J(e.created_at)]})]}),(0,a.jsxs)("div",{className:"space-y-2 mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm",children:[(0,a.jsx)(V.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.user_first_name," ",e.user_last_name]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,a.jsx)(X.A,{className:"w-4 h-4 text-gray-500"}),(0,a.jsx)("span",{children:e.location})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-700 line-clamp-3",children:e.description}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-100",children:(0,a.jsx)(l.T,{size:"sm",color:"primary",variant:"light",className:"w-full",children:"عرض التفاصيل"})})]})})}),(0,a.jsx)(W.cw,{children:(0,a.jsx)(W.$m,{children:(0,a.jsx)(B.A,{id:e.id.toString()})})})]},e.id))}),f>1&&(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(M,{total:f,page:v,onChange:e=>{x(e)},showControls:!0,className:"gap-2",classNames:{wrapper:"gap-0 overflow-visible h-8",item:"w-8 h-8 text-small rounded-none bg-transparent",cursor:"bg-primary text-white font-bold"}})}),r&&e.length>0&&(0,a.jsx)("div",{className:"flex justify-center mt-4",children:(0,a.jsx)(n.o,{size:"sm"})})]}):(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)($.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-600 mb-2",children:"لا توجد إشعارات"}),(0,a.jsx)("p",{className:"text-gray-500",children:"all"===b?"لم يتم العثور على أي إشعارات طوارئ":`لم يتم العثور على إشعارات من نوع "${K(b)}"`})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65758:(e,t,r)=>{Promise.resolve().then(r.bind(r,88681))},79551:e=>{"use strict";e.exports=require("url")},88681:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\(home)\\\\previous\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\previous\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,172,443,966,416,513,301,381,317,876,159],()=>r(22179));module.exports=a})();