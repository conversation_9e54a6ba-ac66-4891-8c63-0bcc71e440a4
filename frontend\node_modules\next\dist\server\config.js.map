{"version": 3, "sources": ["../../src/server/config.ts"], "sourcesContent": ["import { existsSync } from 'fs'\nimport { basename, extname, join, relative, isAbsolute, resolve } from 'path'\nimport { pathToFileURL } from 'url'\nimport findUp from 'next/dist/compiled/find-up'\nimport * as Log from '../build/output/log'\nimport { CONFIG_FILES, PHASE_DEVELOPMENT_SERVER } from '../shared/lib/constants'\nimport { defaultConfig, normalizeConfig } from './config-shared'\nimport type {\n  ExperimentalConfig,\n  NextConfigComplete,\n  NextConfig,\n  TurboLoaderItem,\n} from './config-shared'\n\nimport { loadWebpackHook } from './config-utils'\nimport { imageConfigDefault } from '../shared/lib/image-config'\nimport type { ImageConfig } from '../shared/lib/image-config'\nimport { loadEnvConfig, updateInitialEnv } from '@next/env'\nimport { flushAndExit } from '../telemetry/flush-and-exit'\nimport { findRootDir } from '../lib/find-root'\nimport { setHttpClientAndAgentOptions } from './setup-http-agent-env'\nimport { pathHasPrefix } from '../shared/lib/router/utils/path-has-prefix'\nimport { matchRemotePattern } from '../shared/lib/match-remote-pattern'\n\nimport type { ZodError } from 'next/dist/compiled/zod'\nimport { hasNextSupport } from '../server/ci-info'\nimport { transpileConfig } from '../build/next-config-ts/transpile-config'\nimport { dset } from '../shared/lib/dset'\nimport { normalizeZodErrors } from '../shared/lib/zod'\nimport { HTML_LIMITED_BOT_UA_RE_STRING } from '../shared/lib/router/utils/is-bot'\nimport { findDir } from '../lib/find-pages-dir'\nimport { CanaryOnlyError, isStableBuild } from '../shared/lib/canary-only'\n\nexport { normalizeConfig } from './config-shared'\nexport type { DomainLocale, NextConfig } from './config-shared'\n\nfunction normalizeNextConfigZodErrors(\n  error: ZodError<NextConfig>\n): [errorMessages: string[], shouldExit: boolean] {\n  let shouldExit = false\n  const issues = normalizeZodErrors(error)\n  return [\n    issues.flatMap(({ issue, message }) => {\n      if (issue.path[0] === 'images') {\n        // We exit the build when encountering an error in the images config\n        shouldExit = true\n      }\n\n      return message\n    }),\n    shouldExit,\n  ]\n}\n\nexport function warnOptionHasBeenDeprecated(\n  config: NextConfig,\n  nestedPropertyKey: string,\n  reason: string,\n  silent: boolean\n): boolean {\n  let hasWarned = false\n  if (!silent) {\n    let current = config\n    let found = true\n    const nestedPropertyKeys = nestedPropertyKey.split('.')\n    for (const key of nestedPropertyKeys) {\n      if (current[key] !== undefined) {\n        current = current[key]\n      } else {\n        found = false\n        break\n      }\n    }\n    if (found) {\n      Log.warnOnce(reason)\n      hasWarned = true\n    }\n  }\n  return hasWarned\n}\n\nexport function warnOptionHasBeenMovedOutOfExperimental(\n  config: NextConfig,\n  oldExperimentalKey: string,\n  newKey: string,\n  configFileName: string,\n  silent: boolean\n) {\n  if (config.experimental && oldExperimentalKey in config.experimental) {\n    if (!silent) {\n      Log.warn(\n        `\\`experimental.${oldExperimentalKey}\\` has been moved to \\`${newKey}\\`. ` +\n          `Please update your ${configFileName} file accordingly.`\n      )\n    }\n\n    let current = config\n    const newKeys = newKey.split('.')\n    while (newKeys.length > 1) {\n      const key = newKeys.shift()!\n      current[key] = current[key] || {}\n      current = current[key]\n    }\n    current[newKeys.shift()!] = (config.experimental as any)[oldExperimentalKey]\n  }\n\n  return config\n}\n\nfunction warnCustomizedOption(\n  config: NextConfig,\n  key: string,\n  defaultValue: any,\n  customMessage: string,\n  configFileName: string,\n  silent: boolean\n) {\n  const segs = key.split('.')\n  let current = config\n\n  while (segs.length >= 1) {\n    const seg = segs.shift()!\n    if (!(seg in current)) {\n      return\n    }\n    current = current[seg]\n  }\n\n  if (!silent && current !== defaultValue) {\n    Log.warn(\n      `The \"${key}\" option has been modified. ${customMessage ? customMessage + '. ' : ''}It should be removed from your ${configFileName}.`\n    )\n  }\n}\n\nfunction assignDefaults(\n  dir: string,\n  userConfig: { [key: string]: any },\n  silent: boolean\n) {\n  const configFileName = userConfig.configFileName\n  if (typeof userConfig.exportTrailingSlash !== 'undefined') {\n    if (!silent) {\n      Log.warn(\n        `The \"exportTrailingSlash\" option has been renamed to \"trailingSlash\". Please update your ${configFileName}.`\n      )\n    }\n    if (typeof userConfig.trailingSlash === 'undefined') {\n      userConfig.trailingSlash = userConfig.exportTrailingSlash\n    }\n    delete userConfig.exportTrailingSlash\n  }\n\n  const config = Object.keys(userConfig).reduce<{ [key: string]: any }>(\n    (currentConfig, key) => {\n      const value = userConfig[key]\n\n      if (value === undefined || value === null) {\n        return currentConfig\n      }\n\n      if (key === 'distDir') {\n        if (typeof value !== 'string') {\n          throw new Error(\n            `Specified distDir is not a string, found type \"${typeof value}\"`\n          )\n        }\n        const userDistDir = value.trim()\n\n        // don't allow public as the distDir as this is a reserved folder for\n        // public files\n        if (userDistDir === 'public') {\n          throw new Error(\n            `The 'public' directory is reserved in Next.js and can not be set as the 'distDir'. https://nextjs.org/docs/messages/can-not-output-to-public`\n          )\n        }\n        // make sure distDir isn't an empty string as it can result in the provided\n        // directory being deleted in development mode\n        if (userDistDir.length === 0) {\n          throw new Error(\n            `Invalid distDir provided, distDir can not be an empty string. Please remove this config or set it to undefined`\n          )\n        }\n      }\n\n      if (key === 'pageExtensions') {\n        if (!Array.isArray(value)) {\n          throw new Error(\n            `Specified pageExtensions is not an array of strings, found \"${value}\". Please update this config or remove it.`\n          )\n        }\n\n        if (!value.length) {\n          throw new Error(\n            `Specified pageExtensions is an empty array. Please update it with the relevant extensions or remove it.`\n          )\n        }\n\n        value.forEach((ext) => {\n          if (typeof ext !== 'string') {\n            throw new Error(\n              `Specified pageExtensions is not an array of strings, found \"${ext}\" of type \"${typeof ext}\". Please update this config or remove it.`\n            )\n          }\n        })\n      }\n\n      if (!!value && value.constructor === Object) {\n        currentConfig[key] = {\n          ...defaultConfig[key],\n          ...Object.keys(value).reduce<any>((c, k) => {\n            const v = value[k]\n            if (v !== undefined && v !== null) {\n              c[k] = v\n            }\n            return c\n          }, {}),\n        }\n      } else {\n        currentConfig[key] = value\n      }\n\n      return currentConfig\n    },\n    {}\n  )\n\n  // TODO: remove these once we've made PPR default\n  // If this was defaulted to true, it implies that the configuration was\n  // overridden for testing to be defaulted on.\n  if (defaultConfig.experimental?.ppr) {\n    Log.warn(\n      `\\`experimental.ppr\\` has been defaulted to \\`true\\` because \\`__NEXT_EXPERIMENTAL_PPR\\` was set to \\`true\\` during testing.`\n    )\n  }\n\n  const result = { ...defaultConfig, ...config }\n\n  if (\n    result.experimental?.allowDevelopmentBuild &&\n    process.env.NODE_ENV !== 'development'\n  ) {\n    throw new Error(\n      `The experimental.allowDevelopmentBuild option requires NODE_ENV to be explicitly set to 'development'.`\n    )\n  }\n\n  if (isStableBuild()) {\n    // Prevents usage of certain experimental features outside of canary\n    if (result.experimental?.ppr) {\n      throw new CanaryOnlyError({ feature: 'experimental.ppr' })\n    } else if (result.experimental?.dynamicIO) {\n      throw new CanaryOnlyError({ feature: 'experimental.dynamicIO' })\n    } else if (result.experimental?.turbo?.unstablePersistentCaching) {\n      throw new CanaryOnlyError({\n        feature: 'experimental.turbo.unstablePersistentCaching',\n      })\n    } else if (result.experimental?.nodeMiddleware) {\n      throw new CanaryOnlyError({ feature: 'experimental.nodeMiddleware' })\n    }\n  }\n\n  if (result.output === 'export') {\n    if (result.i18n) {\n      throw new Error(\n        'Specified \"i18n\" cannot be used with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-i18n'\n      )\n    }\n\n    if (!hasNextSupport) {\n      if (result.rewrites) {\n        Log.warn(\n          'Specified \"rewrites\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.redirects) {\n        Log.warn(\n          'Specified \"redirects\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n      if (result.headers) {\n        Log.warn(\n          'Specified \"headers\" will not automatically work with \"output: export\". See more info here: https://nextjs.org/docs/messages/export-no-custom-routes'\n        )\n      }\n    }\n  }\n\n  if (typeof result.assetPrefix !== 'string') {\n    throw new Error(\n      `Specified assetPrefix is not a string, found type \"${typeof result.assetPrefix}\" https://nextjs.org/docs/messages/invalid-assetprefix`\n    )\n  }\n\n  if (typeof result.basePath !== 'string') {\n    throw new Error(\n      `Specified basePath is not a string, found type \"${typeof result.basePath}\"`\n    )\n  }\n\n  if (result.basePath !== '') {\n    if (result.basePath === '/') {\n      throw new Error(\n        `Specified basePath /. basePath has to be either an empty string or a path prefix\"`\n      )\n    }\n\n    if (!result.basePath.startsWith('/')) {\n      throw new Error(\n        `Specified basePath has to start with a /, found \"${result.basePath}\"`\n      )\n    }\n\n    if (result.basePath !== '/') {\n      if (result.basePath.endsWith('/')) {\n        throw new Error(\n          `Specified basePath should not end with /, found \"${result.basePath}\"`\n        )\n      }\n\n      if (result.assetPrefix === '') {\n        result.assetPrefix = result.basePath\n      }\n\n      if (result.amp?.canonicalBase === '') {\n        result.amp.canonicalBase = result.basePath\n      }\n    }\n  }\n\n  if (result?.images) {\n    const images: ImageConfig = result.images\n\n    if (typeof images !== 'object') {\n      throw new Error(\n        `Specified images should be an object received ${typeof images}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n      )\n    }\n\n    if (images.localPatterns) {\n      if (!Array.isArray(images.localPatterns)) {\n        throw new Error(\n          `Specified images.localPatterns should be an Array received ${typeof images.localPatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n      // avoid double-pushing the same pattern if it already exists\n      const hasMatch = images.localPatterns.some(\n        (pattern) =>\n          pattern.pathname === '/_next/static/media/**' && pattern.search === ''\n      )\n      if (!hasMatch) {\n        // static import images are automatically allowed\n        images.localPatterns.push({\n          pathname: '/_next/static/media/**',\n          search: '',\n        })\n      }\n    }\n\n    if (images.remotePatterns) {\n      if (!Array.isArray(images.remotePatterns)) {\n        throw new Error(\n          `Specified images.remotePatterns should be an Array received ${typeof images.remotePatterns}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n\n      // static images are automatically prefixed with assetPrefix\n      // so we need to ensure _next/image allows downloading from\n      // this resource\n      if (config.assetPrefix?.startsWith('http')) {\n        try {\n          const url = new URL(config.assetPrefix)\n          const hasMatchForAssetPrefix = images.remotePatterns.some((pattern) =>\n            matchRemotePattern(pattern, url)\n          )\n\n          // avoid double-pushing the same pattern if it already can be matched\n          if (!hasMatchForAssetPrefix) {\n            images.remotePatterns.push({\n              hostname: url.hostname,\n              protocol: url.protocol.replace(/:$/, '') as 'http' | 'https',\n              port: url.port,\n            })\n          }\n        } catch (error) {\n          throw new Error(\n            `Invalid assetPrefix provided. Original error: ${error}`\n          )\n        }\n      }\n    }\n\n    if (images.domains) {\n      if (!Array.isArray(images.domains)) {\n        throw new Error(\n          `Specified images.domains should be an Array received ${typeof images.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-images-config`\n        )\n      }\n    }\n\n    if (!images.loader) {\n      images.loader = 'default'\n    }\n\n    if (\n      images.loader !== 'default' &&\n      images.loader !== 'custom' &&\n      images.path === imageConfigDefault.path\n    ) {\n      throw new Error(\n        `Specified images.loader property (${images.loader}) also requires images.path property to be assigned to a URL prefix.\\nSee more info here: https://nextjs.org/docs/api-reference/next/legacy/image#loader-configuration`\n      )\n    }\n\n    if (\n      images.path === imageConfigDefault.path &&\n      result.basePath &&\n      !pathHasPrefix(images.path, result.basePath)\n    ) {\n      images.path = `${result.basePath}${images.path}`\n    }\n\n    // Append trailing slash for non-default loaders and when trailingSlash is set\n    if (\n      images.path &&\n      !images.path.endsWith('/') &&\n      (images.loader !== 'default' || result.trailingSlash)\n    ) {\n      images.path += '/'\n    }\n\n    if (images.loaderFile) {\n      if (images.loader !== 'default' && images.loader !== 'custom') {\n        throw new Error(\n          `Specified images.loader property (${images.loader}) cannot be used with images.loaderFile property. Please set images.loader to \"custom\".`\n        )\n      }\n      const absolutePath = join(dir, images.loaderFile)\n      if (!existsSync(absolutePath)) {\n        throw new Error(\n          `Specified images.loaderFile does not exist at \"${absolutePath}\".`\n        )\n      }\n      images.loaderFile = absolutePath\n    }\n  }\n\n  warnCustomizedOption(\n    result,\n    'experimental.esmExternals',\n    true,\n    'experimental.esmExternals is not recommended to be modified as it may disrupt module resolution',\n    configFileName,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'experimental.instrumentationHook',\n    `\\`experimental.instrumentationHook\\` is no longer needed, because \\`instrumentation.js\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'experimental.after',\n    `\\`experimental.after\\` is no longer needed, because \\`after\\` is available by default. You can remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.appIsrStatus',\n    `\\`devIndicators.appIsrStatus\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.buildActivity',\n    `\\`devIndicators.buildActivity\\` is deprecated and no longer configurable. Please remove it from ${configFileName}.`,\n    silent\n  )\n\n  const hasWarnedBuildActivityPosition = warnOptionHasBeenDeprecated(\n    result,\n    'devIndicators.buildActivityPosition',\n    `\\`devIndicators.buildActivityPosition\\` has been renamed to \\`devIndicators.position\\`. Please update your ${configFileName} file accordingly.`,\n    silent\n  )\n  if (\n    hasWarnedBuildActivityPosition &&\n    result.devIndicators !== false &&\n    result.devIndicators?.buildActivityPosition &&\n    result.devIndicators.buildActivityPosition !== result.devIndicators.position\n  ) {\n    Log.warnOnce(\n      `The \\`devIndicators\\` option \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") conflicts with \\`position\\` (\"${result.devIndicators.position}\"). Using \\`buildActivityPosition\\` (\"${result.devIndicators.buildActivityPosition}\") for backward compatibility.`\n    )\n    result.devIndicators.position = result.devIndicators.buildActivityPosition\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'bundlePagesExternals',\n    'bundlePagesRouterDependencies',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'serverComponentsExternalPackages',\n    'serverExternalPackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'relay',\n    'compiler.relay',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'styledComponents',\n    'compiler.styledComponents',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'emotion',\n    'compiler.emotion',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'reactRemoveProperties',\n    'compiler.reactRemoveProperties',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'removeConsole',\n    'compiler.removeConsole',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'swrDelta',\n    'expireTime',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingRoot',\n    'outputFileTracingRoot',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingIncludes',\n    'outputFileTracingIncludes',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'outputFileTracingExcludes',\n    'outputFileTracingExcludes',\n    configFileName,\n    silent\n  )\n\n  if ((result.experimental as any).outputStandalone) {\n    if (!silent) {\n      Log.warn(\n        `experimental.outputStandalone has been renamed to \"output: 'standalone'\", please move the config.`\n      )\n    }\n    result.output = 'standalone'\n  }\n\n  if (\n    typeof result.experimental?.serverActions?.bodySizeLimit !== 'undefined'\n  ) {\n    const value = parseInt(\n      result.experimental.serverActions?.bodySizeLimit.toString()\n    )\n    if (isNaN(value) || value < 1) {\n      throw new Error(\n        'Server Actions Size Limit must be a valid number or filesize format larger than 1MB: https://nextjs.org/docs/app/api-reference/next-config-js/serverActions#bodysizelimit'\n      )\n    }\n  }\n\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'transpilePackages',\n    'transpilePackages',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipMiddlewareUrlNormalize',\n    'skipMiddlewareUrlNormalize',\n    configFileName,\n    silent\n  )\n  warnOptionHasBeenMovedOutOfExperimental(\n    result,\n    'skipTrailingSlashRedirect',\n    'skipTrailingSlashRedirect',\n    configFileName,\n    silent\n  )\n\n  if (\n    result?.outputFileTracingRoot &&\n    !isAbsolute(result.outputFileTracingRoot)\n  ) {\n    result.outputFileTracingRoot = resolve(result.outputFileTracingRoot)\n    if (!silent) {\n      Log.warn(\n        `outputFileTracingRoot should be absolute, using: ${result.outputFileTracingRoot}`\n      )\n    }\n  }\n\n  if (\n    result?.experimental?.turbo?.root &&\n    !isAbsolute(result.experimental.turbo.root)\n  ) {\n    result.experimental.turbo.root = resolve(result.experimental.turbo.root)\n    if (!silent) {\n      Log.warn(\n        `experimental.turbo.root should be absolute, using: ${result.experimental.turbo.root}`\n      )\n    }\n  }\n\n  // only leverage deploymentId\n  if (process.env.NEXT_DEPLOYMENT_ID) {\n    result.deploymentId = process.env.NEXT_DEPLOYMENT_ID\n  }\n\n  if (result?.outputFileTracingRoot && !result?.experimental?.turbo?.root) {\n    dset(\n      result,\n      ['experimental', 'turbo', 'root'],\n      result.outputFileTracingRoot\n    )\n  }\n\n  // use the closest lockfile as tracing root\n  if (!result?.outputFileTracingRoot || !result?.experimental?.turbo?.root) {\n    let rootDir = findRootDir(dir)\n\n    if (rootDir) {\n      if (!result?.outputFileTracingRoot) {\n        result.outputFileTracingRoot = rootDir\n        defaultConfig.outputFileTracingRoot = result.outputFileTracingRoot\n      }\n\n      if (!result?.experimental?.turbo?.root) {\n        dset(result, ['experimental', 'turbo', 'root'], rootDir)\n        dset(defaultConfig, ['experimental', 'turbo', 'root'], rootDir)\n      }\n    }\n  }\n\n  setHttpClientAndAgentOptions(result || defaultConfig)\n\n  if (result.i18n) {\n    const hasAppDir = Boolean(findDir(dir, 'app'))\n\n    if (hasAppDir) {\n      warnOptionHasBeenDeprecated(\n        result,\n        'i18n',\n        `i18n configuration in ${configFileName} is unsupported in App Router.\\nLearn more about internationalization in App Router: https://nextjs.org/docs/app/building-your-application/routing/internationalization`,\n        silent\n      )\n    }\n\n    const { i18n } = result\n    const i18nType = typeof i18n\n\n    if (i18nType !== 'object') {\n      throw new Error(\n        `Specified i18n should be an object received ${i18nType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales should be an Array received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.locales.length > 100 && !silent) {\n      Log.warn(\n        `Received ${i18n.locales.length} i18n.locales items which exceeds the recommended max of 100.\\nSee more info here: https://nextjs.org/docs/advanced-features/i18n-routing#how-does-this-work-with-static-generation`\n      )\n    }\n\n    const defaultLocaleType = typeof i18n.defaultLocale\n\n    if (!i18n.defaultLocale || defaultLocaleType !== 'string') {\n      throw new Error(\n        `Specified i18n.defaultLocale should be a string.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (typeof i18n.domains !== 'undefined' && !Array.isArray(i18n.domains)) {\n      throw new Error(\n        `Specified i18n.domains must be an array of domain objects e.g. [ { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] } ] received ${typeof i18n.domains}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    if (i18n.domains) {\n      const invalidDomainItems = i18n.domains.filter((item) => {\n        if (!item || typeof item !== 'object') return true\n        if (!item.defaultLocale) return true\n        if (!item.domain || typeof item.domain !== 'string') return true\n\n        if (item.domain.includes(':')) {\n          console.warn(\n            `i18n domain: \"${item.domain}\" is invalid it should be a valid domain without protocol (https://) or port (:3000) e.g. example.vercel.sh`\n          )\n          return true\n        }\n\n        const defaultLocaleDuplicate = i18n.domains?.find(\n          (altItem) =>\n            altItem.defaultLocale === item.defaultLocale &&\n            altItem.domain !== item.domain\n        )\n\n        if (!silent && defaultLocaleDuplicate) {\n          console.warn(\n            `Both ${item.domain} and ${defaultLocaleDuplicate.domain} configured the defaultLocale ${item.defaultLocale} but only one can. Change one item's default locale to continue`\n          )\n          return true\n        }\n\n        let hasInvalidLocale = false\n\n        if (Array.isArray(item.locales)) {\n          for (const locale of item.locales) {\n            if (typeof locale !== 'string') hasInvalidLocale = true\n\n            for (const domainItem of i18n.domains || []) {\n              if (domainItem === item) continue\n              if (domainItem.locales && domainItem.locales.includes(locale)) {\n                console.warn(\n                  `Both ${item.domain} and ${domainItem.domain} configured the locale (${locale}) but only one can. Remove it from one i18n.domains config to continue`\n                )\n                hasInvalidLocale = true\n                break\n              }\n            }\n          }\n        }\n\n        return hasInvalidLocale\n      })\n\n      if (invalidDomainItems.length > 0) {\n        throw new Error(\n          `Invalid i18n.domains values:\\n${invalidDomainItems\n            .map((item: any) => JSON.stringify(item))\n            .join(\n              '\\n'\n            )}\\n\\ndomains value must follow format { domain: 'example.fr', defaultLocale: 'fr', locales: ['fr'] }.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n        )\n      }\n    }\n\n    if (!Array.isArray(i18n.locales)) {\n      throw new Error(\n        `Specified i18n.locales must be an array of locale strings e.g. [\"en-US\", \"nl-NL\"] received ${typeof i18n.locales}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const invalidLocales = i18n.locales.filter(\n      (locale: any) => typeof locale !== 'string'\n    )\n\n    if (invalidLocales.length > 0) {\n      throw new Error(\n        `Specified i18n.locales contains invalid values (${invalidLocales\n          .map(String)\n          .join(\n            ', '\n          )}), locales must be valid locale tags provided as strings e.g. \"en-US\".\\n` +\n          `See here for list of valid language sub-tags: http://www.iana.org/assignments/language-subtag-registry/language-subtag-registry`\n      )\n    }\n\n    if (!i18n.locales.includes(i18n.defaultLocale)) {\n      throw new Error(\n        `Specified i18n.defaultLocale should be included in i18n.locales.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    const normalizedLocales = new Set()\n    const duplicateLocales = new Set()\n\n    i18n.locales.forEach((locale) => {\n      const localeLower = locale.toLowerCase()\n      if (normalizedLocales.has(localeLower)) {\n        duplicateLocales.add(locale)\n      }\n      normalizedLocales.add(localeLower)\n    })\n\n    if (duplicateLocales.size > 0) {\n      throw new Error(\n        `Specified i18n.locales contains the following duplicate locales:\\n` +\n          `${[...duplicateLocales].join(', ')}\\n` +\n          `Each locale should be listed only once.\\n` +\n          `See more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n\n    // make sure default Locale is at the front\n    i18n.locales = [\n      i18n.defaultLocale,\n      ...i18n.locales.filter((locale) => locale !== i18n.defaultLocale),\n    ]\n\n    const localeDetectionType = typeof i18n.localeDetection\n\n    if (\n      localeDetectionType !== 'boolean' &&\n      localeDetectionType !== 'undefined'\n    ) {\n      throw new Error(\n        `Specified i18n.localeDetection should be undefined or a boolean received ${localeDetectionType}.\\nSee more info here: https://nextjs.org/docs/messages/invalid-i18n-config`\n      )\n    }\n  }\n\n  if (result.devIndicators !== false && result.devIndicators?.position) {\n    const { position } = result.devIndicators\n    const allowedValues = [\n      'top-left',\n      'top-right',\n      'bottom-left',\n      'bottom-right',\n    ]\n\n    if (!allowedValues.includes(position)) {\n      throw new Error(\n        `Invalid \"devIndicator.position\" provided, expected one of ${allowedValues.join(\n          ', '\n        )}, received ${position}`\n      )\n    }\n  }\n\n  if (result.experimental) {\n    result.experimental.cacheLife = {\n      ...defaultConfig.experimental?.cacheLife,\n      ...result.experimental.cacheLife,\n    }\n    const defaultDefault = defaultConfig.experimental?.cacheLife?.['default']\n    if (\n      !defaultDefault ||\n      defaultDefault.revalidate === undefined ||\n      defaultDefault.expire === undefined ||\n      !defaultConfig.experimental?.staleTimes?.static\n    ) {\n      throw new Error('No default cacheLife profile.')\n    }\n    const defaultCacheLifeProfile = result.experimental.cacheLife['default']\n    if (!defaultCacheLifeProfile) {\n      result.experimental.cacheLife['default'] = defaultDefault\n    } else {\n      if (defaultCacheLifeProfile.stale === undefined) {\n        const staticStaleTime = result.experimental.staleTimes?.static\n        defaultCacheLifeProfile.stale =\n          staticStaleTime ?? defaultConfig.experimental?.staleTimes?.static\n      }\n      if (defaultCacheLifeProfile.revalidate === undefined) {\n        defaultCacheLifeProfile.revalidate = defaultDefault.revalidate\n      }\n      if (defaultCacheLifeProfile.expire === undefined) {\n        defaultCacheLifeProfile.expire =\n          result.expireTime ?? defaultDefault.expire\n      }\n    }\n    // This is the most dynamic cache life profile.\n    const secondsCacheLifeProfile = result.experimental.cacheLife['seconds']\n    if (\n      secondsCacheLifeProfile &&\n      secondsCacheLifeProfile.stale === undefined\n    ) {\n      // We default this to whatever stale time you had configured for dynamic content.\n      // Since this is basically a dynamic cache life profile.\n      const dynamicStaleTime = result.experimental.staleTimes?.dynamic\n      secondsCacheLifeProfile.stale =\n        dynamicStaleTime ?? defaultConfig.experimental?.staleTimes?.dynamic\n    }\n  }\n\n  if (result.experimental?.cacheHandlers) {\n    const allowedHandlerNameRegex = /[a-z-]/\n\n    if (typeof result.experimental.cacheHandlers !== 'object') {\n      throw new Error(\n        `Invalid \"experimental.cacheHandlers\" provided, expected an object e.g. { default: '/my-handler.js' }, received ${JSON.stringify(result.experimental.cacheHandlers)}`\n      )\n    }\n\n    const handlerKeys = Object.keys(result.experimental.cacheHandlers)\n    const invalidHandlerItems: Array<{ key: string; reason: string }> = []\n\n    for (const key of handlerKeys) {\n      if (!allowedHandlerNameRegex.test(key)) {\n        invalidHandlerItems.push({\n          key,\n          reason: 'key must only use characters a-z and -',\n        })\n      } else {\n        const handlerPath = result.experimental.cacheHandlers[key]\n\n        if (handlerPath && !existsSync(handlerPath)) {\n          invalidHandlerItems.push({\n            key,\n            reason: `cache handler path provided does not exist, received ${handlerPath}`,\n          })\n        }\n      }\n      if (invalidHandlerItems.length) {\n        throw new Error(\n          `Invalid handler fields configured for \"experimental.cacheHandler\":\\n${invalidHandlerItems.map((item) => `${key}: ${item.reason}`).join('\\n')}`\n        )\n      }\n    }\n  }\n\n  const userProvidedModularizeImports = result.modularizeImports\n  // Unfortunately these packages end up re-exporting 10600 modules, for example: https://unpkg.com/browse/@mui/icons-material@5.11.16/esm/index.js.\n  // Leveraging modularizeImports tremendously reduces compile times for these.\n  result.modularizeImports = {\n    ...(userProvidedModularizeImports || {}),\n    // This is intentionally added after the user-provided modularizeImports config.\n    '@mui/icons-material': {\n      transform: '@mui/icons-material/{{member}}',\n    },\n    lodash: {\n      transform: 'lodash/{{member}}',\n    },\n  }\n\n  const userProvidedOptimizePackageImports =\n    result.experimental?.optimizePackageImports || []\n  if (!result.experimental) {\n    result.experimental = {}\n  }\n\n  result.experimental.optimizePackageImports = [\n    ...new Set([\n      ...userProvidedOptimizePackageImports,\n      'lucide-react',\n      'date-fns',\n      'lodash-es',\n      'ramda',\n      'antd',\n      'react-bootstrap',\n      'ahooks',\n      '@ant-design/icons',\n      '@headlessui/react',\n      '@headlessui-float/react',\n      '@heroicons/react/20/solid',\n      '@heroicons/react/24/solid',\n      '@heroicons/react/24/outline',\n      '@visx/visx',\n      '@tremor/react',\n      'rxjs',\n      '@mui/material',\n      '@mui/icons-material',\n      'recharts',\n      'react-use',\n      'effect',\n      '@effect/schema',\n      '@effect/platform',\n      '@effect/platform-node',\n      '@effect/platform-browser',\n      '@effect/platform-bun',\n      '@effect/sql',\n      '@effect/sql-mssql',\n      '@effect/sql-mysql2',\n      '@effect/sql-pg',\n      '@effect/sql-squlite-node',\n      '@effect/sql-squlite-bun',\n      '@effect/sql-squlite-wasm',\n      '@effect/sql-squlite-react-native',\n      '@effect/sql-squlite-wasm',\n      '@effect/rpc',\n      '@effect/rpc-http',\n      '@effect/typeclass',\n      '@effect/experimental',\n      '@effect/opentelemetry',\n      '@material-ui/core',\n      '@material-ui/icons',\n      '@tabler/icons-react',\n      'mui-core',\n      // We don't support wildcard imports for these configs, e.g. `react-icons/*`\n      // so we need to add them manually.\n      // In the future, we should consider automatically detecting packages that\n      // need to be optimized.\n      'react-icons/ai',\n      'react-icons/bi',\n      'react-icons/bs',\n      'react-icons/cg',\n      'react-icons/ci',\n      'react-icons/di',\n      'react-icons/fa',\n      'react-icons/fa6',\n      'react-icons/fc',\n      'react-icons/fi',\n      'react-icons/gi',\n      'react-icons/go',\n      'react-icons/gr',\n      'react-icons/hi',\n      'react-icons/hi2',\n      'react-icons/im',\n      'react-icons/io',\n      'react-icons/io5',\n      'react-icons/lia',\n      'react-icons/lib',\n      'react-icons/lu',\n      'react-icons/md',\n      'react-icons/pi',\n      'react-icons/ri',\n      'react-icons/rx',\n      'react-icons/si',\n      'react-icons/sl',\n      'react-icons/tb',\n      'react-icons/tfi',\n      'react-icons/ti',\n      'react-icons/vsc',\n      'react-icons/wi',\n    ]),\n  ]\n\n  if (!result.htmlLimitedBots) {\n    // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n    result.htmlLimitedBots = HTML_LIMITED_BOT_UA_RE_STRING\n  }\n\n  // \"use cache\" was originally implicitly enabled with the dynamicIO flag, so\n  // we transfer the value for dynamicIO to the explicit useCache flag to ensure\n  // backwards compatibility.\n  if (result.experimental.useCache === undefined) {\n    result.experimental.useCache = result.experimental.dynamicIO\n  }\n\n  return result\n}\n\nexport default async function loadConfig(\n  phase: string,\n  dir: string,\n  {\n    customConfig,\n    rawConfig,\n    silent = true,\n    onLoadUserConfig,\n    reactProductionProfiling,\n  }: {\n    customConfig?: object | null\n    rawConfig?: boolean\n    silent?: boolean\n    onLoadUserConfig?: (conf: NextConfig) => void\n    reactProductionProfiling?: boolean\n  } = {}\n): Promise<NextConfigComplete> {\n  if (!process.env.__NEXT_PRIVATE_RENDER_WORKER) {\n    try {\n      loadWebpackHook()\n    } catch (err) {\n      // this can fail in standalone mode as the files\n      // aren't traced/included\n      if (!process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n        throw err\n      }\n    }\n  }\n\n  if (process.env.__NEXT_PRIVATE_STANDALONE_CONFIG) {\n    return JSON.parse(process.env.__NEXT_PRIVATE_STANDALONE_CONFIG)\n  }\n\n  const curLog = silent\n    ? {\n        warn: () => {},\n        info: () => {},\n        error: () => {},\n      }\n    : Log\n\n  loadEnvConfig(dir, phase === PHASE_DEVELOPMENT_SERVER, curLog)\n\n  let configFileName = 'next.config.js'\n\n  if (customConfig) {\n    return assignDefaults(\n      dir,\n      {\n        configOrigin: 'server',\n        configFileName,\n        ...customConfig,\n      },\n      silent\n    ) as NextConfigComplete\n  }\n\n  const path = await findUp(CONFIG_FILES, { cwd: dir })\n\n  if (process.env.__NEXT_TEST_MODE) {\n    if (path) {\n      Log.info(`Loading config from ${path}`)\n    } else {\n      Log.info('No config file found')\n    }\n  }\n\n  // If config file was found\n  if (path?.length) {\n    configFileName = basename(path)\n\n    let userConfigModule: any\n    try {\n      const envBefore = Object.assign({}, process.env)\n\n      // `import()` expects url-encoded strings, so the path must be properly\n      // escaped and (especially on Windows) absolute paths must pe prefixed\n      // with the `file://` protocol\n      if (process.env.__NEXT_TEST_MODE === 'jest') {\n        // dynamic import does not currently work inside of vm which\n        // jest relies on so we fall back to require for this case\n        // https://github.com/nodejs/node/issues/35889\n        userConfigModule = require(path)\n      } else if (configFileName === 'next.config.ts') {\n        userConfigModule = await transpileConfig({\n          nextConfigPath: path,\n          cwd: dir,\n        })\n      } else {\n        userConfigModule = await import(pathToFileURL(path).href)\n      }\n      const newEnv: typeof process.env = {} as any\n\n      for (const key of Object.keys(process.env)) {\n        if (envBefore[key] !== process.env[key]) {\n          newEnv[key] = process.env[key]\n        }\n      }\n      updateInitialEnv(newEnv)\n\n      if (rawConfig) {\n        return userConfigModule\n      }\n    } catch (err) {\n      // TODO: Modify docs to add cases of failing next.config.ts transformation\n      curLog.error(\n        `Failed to load ${configFileName}, see more info here https://nextjs.org/docs/messages/next-config-error`\n      )\n      throw err\n    }\n\n    const userConfig = (await normalizeConfig(\n      phase,\n      userConfigModule.default || userConfigModule\n    )) as NextConfig\n\n    if (!process.env.NEXT_MINIMAL) {\n      // We only validate the config against schema in non minimal mode\n      const { configSchema } =\n        require('./config-schema') as typeof import('./config-schema')\n      const state = configSchema.safeParse(userConfig)\n\n      if (state.success === false) {\n        // error message header\n        const messages = [`Invalid ${configFileName} options detected: `]\n\n        const [errorMessages, shouldExit] = normalizeNextConfigZodErrors(\n          state.error\n        )\n        // ident list item\n        for (const error of errorMessages) {\n          messages.push(`    ${error}`)\n        }\n\n        // error message footer\n        messages.push(\n          'See more info here: https://nextjs.org/docs/messages/invalid-next-config'\n        )\n\n        if (shouldExit) {\n          for (const message of messages) {\n            console.error(message)\n          }\n          await flushAndExit(1)\n        } else {\n          for (const message of messages) {\n            curLog.warn(message)\n          }\n        }\n      }\n    }\n\n    if (userConfig.target && userConfig.target !== 'server') {\n      throw new Error(\n        `The \"target\" property is no longer supported in ${configFileName}.\\n` +\n          'See more info here https://nextjs.org/docs/messages/deprecated-target-config'\n      )\n    }\n\n    if (userConfig.amp?.canonicalBase) {\n      const { canonicalBase } = userConfig.amp || ({} as any)\n      userConfig.amp = userConfig.amp || {}\n      userConfig.amp.canonicalBase =\n        (canonicalBase?.endsWith('/')\n          ? canonicalBase.slice(0, -1)\n          : canonicalBase) || ''\n    }\n\n    if (reactProductionProfiling) {\n      userConfig.reactProductionProfiling = reactProductionProfiling\n    }\n\n    if (\n      userConfig.experimental?.turbo?.loaders &&\n      !userConfig.experimental?.turbo?.rules\n    ) {\n      curLog.warn(\n        'experimental.turbo.loaders is now deprecated. Please update next.config.js to use experimental.turbo.rules as soon as possible.\\n' +\n          'The new option is similar, but the key should be a glob instead of an extension.\\n' +\n          'Example: loaders: { \".mdx\": [\"mdx-loader\"] } -> rules: { \"*.mdx\": [\"mdx-loader\"] }\" }\\n' +\n          'See more info here https://nextjs.org/docs/app/api-reference/next-config-js/turbo'\n      )\n\n      const rules: Record<string, TurboLoaderItem[]> = {}\n      for (const [ext, loaders] of Object.entries(\n        userConfig.experimental.turbo.loaders\n      )) {\n        rules['*' + ext] = loaders as TurboLoaderItem[]\n      }\n\n      userConfig.experimental.turbo.rules = rules\n    }\n\n    if (userConfig.experimental?.useLightningcss) {\n      const { loadBindings } = require('next/dist/build/swc')\n      const isLightningSupported = (await loadBindings())?.css?.lightning\n\n      if (!isLightningSupported) {\n        curLog.warn(\n          `experimental.useLightningcss is set, but the setting is disabled because next-swc/wasm does not support it yet.`\n        )\n        userConfig.experimental.useLightningcss = false\n      }\n    }\n\n    // serialize the regex config into string\n    if (userConfig?.htmlLimitedBots instanceof RegExp) {\n      // @ts-expect-error: override the htmlLimitedBots with default string, type covert: RegExp -> string\n      userConfig.htmlLimitedBots = userConfig.htmlLimitedBots.source\n    }\n\n    onLoadUserConfig?.(userConfig)\n    const completeConfig = assignDefaults(\n      dir,\n      {\n        configOrigin: relative(dir, path),\n        configFile: path,\n        configFileName,\n        ...userConfig,\n      },\n      silent\n    ) as NextConfigComplete\n    return completeConfig\n  } else {\n    const configBaseName = basename(CONFIG_FILES[0], extname(CONFIG_FILES[0]))\n    const unsupportedConfig = findUp.sync(\n      [\n        `${configBaseName}.cjs`,\n        `${configBaseName}.cts`,\n        `${configBaseName}.mts`,\n        `${configBaseName}.json`,\n        `${configBaseName}.jsx`,\n        `${configBaseName}.tsx`,\n      ],\n      { cwd: dir }\n    )\n    if (unsupportedConfig?.length) {\n      throw new Error(\n        `Configuring Next.js via '${basename(\n          unsupportedConfig\n        )}' is not supported. Please replace the file with 'next.config.js', 'next.config.mjs', or 'next.config.ts'.`\n      )\n    }\n  }\n\n  // always call assignDefaults to ensure settings like\n  // reactRoot can be updated correctly even with no next.config.js\n  const completeConfig = assignDefaults(\n    dir,\n    defaultConfig,\n    silent\n  ) as NextConfigComplete\n  completeConfig.configFileName = configFileName\n  setHttpClientAndAgentOptions(completeConfig)\n  return completeConfig\n}\n\nexport type ConfiguredExperimentalFeature =\n  | { name: keyof ExperimentalConfig; type: 'boolean'; value: boolean }\n  | { name: keyof ExperimentalConfig; type: 'number'; value: number }\n  | { name: keyof ExperimentalConfig; type: 'other' }\n\nexport function getConfiguredExperimentalFeatures(\n  userNextConfigExperimental: NextConfig['experimental']\n) {\n  const configuredExperimentalFeatures: ConfiguredExperimentalFeature[] = []\n\n  if (!userNextConfigExperimental) {\n    return configuredExperimentalFeatures\n  }\n\n  // defaultConfig.experimental is predefined and will never be undefined\n  // This is only a type guard for the typescript\n  if (defaultConfig.experimental) {\n    for (const name of Object.keys(\n      userNextConfigExperimental\n    ) as (keyof ExperimentalConfig)[]) {\n      const value = userNextConfigExperimental[name]\n\n      if (name === 'turbo' && !process.env.TURBOPACK) {\n        // Ignore any Turbopack config if Turbopack is not enabled\n        continue\n      }\n\n      if (\n        name in defaultConfig.experimental &&\n        value !== defaultConfig.experimental[name]\n      ) {\n        configuredExperimentalFeatures.push(\n          typeof value === 'boolean'\n            ? { name, type: 'boolean', value }\n            : typeof value === 'number'\n              ? { name, type: 'number', value }\n              : { name, type: 'other' }\n        )\n      }\n    }\n  }\n  return configuredExperimentalFeatures\n}\n"], "names": ["loadConfig", "getConfiguredExperimentalFeatures", "normalizeConfig", "warnOptionHasBeenDeprecated", "warnOptionHasBeenMovedOutOfExperimental", "normalizeNextConfigZodErrors", "error", "shouldExit", "issues", "normalizeZodErrors", "flatMap", "issue", "message", "path", "config", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "reason", "silent", "hasWarned", "current", "found", "nestedPropertyKeys", "split", "key", "undefined", "Log", "warnOnce", "oldExperimentalKey", "new<PERSON>ey", "configFileName", "experimental", "warn", "newKeys", "length", "shift", "warnCustomizedOption", "defaultValue", "customMessage", "segs", "seg", "assignDefaults", "dir", "userConfig", "defaultConfig", "result", "exportTrailingSlash", "trailingSlash", "Object", "keys", "reduce", "currentConfig", "value", "Error", "userDistDir", "trim", "Array", "isArray", "for<PERSON>ach", "ext", "constructor", "c", "k", "v", "ppr", "allowDevelopmentBuild", "process", "env", "NODE_ENV", "isStableBuild", "CanaryOnlyError", "feature", "dynamicIO", "turbo", "unstablePersistentCaching", "nodeMiddleware", "output", "i18n", "hasNextSupport", "rewrites", "redirects", "headers", "assetPrefix", "basePath", "startsWith", "endsWith", "amp", "canonicalBase", "images", "localPatterns", "hasMatch", "some", "pattern", "pathname", "search", "push", "remotePatterns", "url", "URL", "hasMatchForAssetPrefix", "matchRemotePattern", "hostname", "protocol", "replace", "port", "domains", "loader", "imageConfigDefault", "pathHasPrefix", "loaderFile", "absolutePath", "join", "existsSync", "hasWarnedBuildActivityPosition", "devIndicators", "buildActivityPosition", "position", "outputStandalone", "serverActions", "bodySizeLimit", "parseInt", "toString", "isNaN", "outputFileTracingRoot", "isAbsolute", "resolve", "root", "NEXT_DEPLOYMENT_ID", "deploymentId", "dset", "rootDir", "findRootDir", "setHttpClientAndAgentOptions", "hasAppDir", "Boolean", "findDir", "i18nType", "locales", "defaultLocaleType", "defaultLocale", "invalidDomainItems", "filter", "item", "domain", "includes", "console", "defaultLocaleDuplicate", "find", "altItem", "hasInvalidLocale", "locale", "domainItem", "map", "JSON", "stringify", "invalidLocales", "String", "normalizedLocales", "Set", "duplicateLocales", "localeLower", "toLowerCase", "has", "add", "size", "localeDetectionType", "localeDetection", "<PERSON><PERSON><PERSON><PERSON>", "cacheLife", "defaultDefault", "revalidate", "expire", "staleTimes", "static", "defaultCacheLifeProfile", "stale", "staticStaleTime", "expireTime", "secondsCacheLifeProfile", "dynamicStaleTime", "dynamic", "cacheHandlers", "allowedHandlerNameRegex", "handler<PERSON>eys", "invalidHandlerItems", "test", "handler<PERSON><PERSON>", "userProvidedModularizeImports", "modularizeImports", "transform", "lodash", "userProvidedOptimizePackageImports", "optimizePackageImports", "htmlLimitedBots", "HTML_LIMITED_BOT_UA_RE_STRING", "useCache", "phase", "customConfig", "rawConfig", "onLoadUserConfig", "reactProductionProfiling", "__NEXT_PRIVATE_RENDER_WORKER", "loadWebpackHook", "err", "__NEXT_PRIVATE_STANDALONE_CONFIG", "parse", "curLog", "info", "loadEnvConfig", "PHASE_DEVELOPMENT_SERVER", "config<PERSON><PERSON><PERSON>", "findUp", "CONFIG_FILES", "cwd", "__NEXT_TEST_MODE", "basename", "userConfigModule", "envBefore", "assign", "require", "transpileConfig", "nextConfigPath", "pathToFileURL", "href", "newEnv", "updateInitialEnv", "default", "NEXT_MINIMAL", "configSchema", "state", "safeParse", "success", "messages", "errorMessages", "flushAndExit", "target", "slice", "loaders", "rules", "entries", "useLightningcss", "loadBindings", "isLightningSupported", "css", "lightning", "RegExp", "source", "completeConfig", "relative", "configFile", "configBaseName", "extname", "unsupportedConfig", "sync", "userNextConfigExperimental", "configuredExperimentalFeatures", "name", "TURBOPACK", "type"], "mappings": ";;;;;;;;;;;;;;;;;;IA+iCA,OA+PC;eA/P6BA;;IAsQdC,iCAAiC;eAAjCA;;IApxCPC,eAAe;eAAfA,6BAAe;;IAqBRC,2BAA2B;eAA3BA;;IA2BAC,uCAAuC;eAAvCA;;;oBAjFW;sBAC4C;qBACzC;+DACX;6DACE;2BACkC;8BACR;6BAQf;6BACG;qBAEa;8BACnB;0BACD;mCACiB;+BACf;oCACK;wBAGJ;iCACC;sBACX;qBACc;uBACW;8BACtB;4BACuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAK/C,SAASC,6BACPC,KAA2B;IAE3B,IAAIC,aAAa;IACjB,MAAMC,SAASC,IAAAA,uBAAkB,EAACH;IAClC,OAAO;QACLE,OAAOE,OAAO,CAAC,CAAC,EAAEC,KAAK,EAAEC,OAAO,EAAE;YAChC,IAAID,MAAME,IAAI,CAAC,EAAE,KAAK,UAAU;gBAC9B,oEAAoE;gBACpEN,aAAa;YACf;YAEA,OAAOK;QACT;QACAL;KACD;AACH;AAEO,SAASJ,4BACdW,MAAkB,EAClBC,iBAAyB,EACzBC,MAAc,EACdC,MAAe;IAEf,IAAIC,YAAY;IAChB,IAAI,CAACD,QAAQ;QACX,IAAIE,UAAUL;QACd,IAAIM,QAAQ;QACZ,MAAMC,qBAAqBN,kBAAkBO,KAAK,CAAC;QACnD,KAAK,MAAMC,OAAOF,mBAAoB;YACpC,IAAIF,OAAO,CAACI,IAAI,KAAKC,WAAW;gBAC9BL,UAAUA,OAAO,CAACI,IAAI;YACxB,OAAO;gBACLH,QAAQ;gBACR;YACF;QACF;QACA,IAAIA,OAAO;YACTK,KAAIC,QAAQ,CAACV;YACbE,YAAY;QACd;IACF;IACA,OAAOA;AACT;AAEO,SAASd,wCACdU,MAAkB,EAClBa,kBAA0B,EAC1BC,MAAc,EACdC,cAAsB,EACtBZ,MAAe;IAEf,IAAIH,OAAOgB,YAAY,IAAIH,sBAAsBb,OAAOgB,YAAY,EAAE;QACpE,IAAI,CAACb,QAAQ;YACXQ,KAAIM,IAAI,CACN,CAAC,eAAe,EAAEJ,mBAAmB,uBAAuB,EAAEC,OAAO,IAAI,CAAC,GACxE,CAAC,mBAAmB,EAAEC,eAAe,kBAAkB,CAAC;QAE9D;QAEA,IAAIV,UAAUL;QACd,MAAMkB,UAAUJ,OAAON,KAAK,CAAC;QAC7B,MAAOU,QAAQC,MAAM,GAAG,EAAG;YACzB,MAAMV,MAAMS,QAAQE,KAAK;YACzBf,OAAO,CAACI,IAAI,GAAGJ,OAAO,CAACI,IAAI,IAAI,CAAC;YAChCJ,UAAUA,OAAO,CAACI,IAAI;QACxB;QACAJ,OAAO,CAACa,QAAQE,KAAK,GAAI,GAAG,AAACpB,OAAOgB,YAAY,AAAQ,CAACH,mBAAmB;IAC9E;IAEA,OAAOb;AACT;AAEA,SAASqB,qBACPrB,MAAkB,EAClBS,GAAW,EACXa,YAAiB,EACjBC,aAAqB,EACrBR,cAAsB,EACtBZ,MAAe;IAEf,MAAMqB,OAAOf,IAAID,KAAK,CAAC;IACvB,IAAIH,UAAUL;IAEd,MAAOwB,KAAKL,MAAM,IAAI,EAAG;QACvB,MAAMM,MAAMD,KAAKJ,KAAK;QACtB,IAAI,CAAEK,CAAAA,OAAOpB,OAAM,GAAI;YACrB;QACF;QACAA,UAAUA,OAAO,CAACoB,IAAI;IACxB;IAEA,IAAI,CAACtB,UAAUE,YAAYiB,cAAc;QACvCX,KAAIM,IAAI,CACN,CAAC,KAAK,EAAER,IAAI,4BAA4B,EAAEc,gBAAgBA,gBAAgB,OAAO,GAAG,+BAA+B,EAAER,eAAe,CAAC,CAAC;IAE1I;AACF;AAEA,SAASW,eACPC,GAAW,EACXC,UAAkC,EAClCzB,MAAe;QA4FX0B,6BASFC,sBA8PAA,uBAiGOA,oCAAAA,uBA+CPA,4BAAAA,uBAgBoCA,6BAAAA,uBASCA,6BAAAA,uBA6LDA,wBA+DlCA,uBAmDFA;IAzzBF,MAAMf,iBAAiBa,WAAWb,cAAc;IAChD,IAAI,OAAOa,WAAWG,mBAAmB,KAAK,aAAa;QACzD,IAAI,CAAC5B,QAAQ;YACXQ,KAAIM,IAAI,CACN,CAAC,yFAAyF,EAAEF,eAAe,CAAC,CAAC;QAEjH;QACA,IAAI,OAAOa,WAAWI,aAAa,KAAK,aAAa;YACnDJ,WAAWI,aAAa,GAAGJ,WAAWG,mBAAmB;QAC3D;QACA,OAAOH,WAAWG,mBAAmB;IACvC;IAEA,MAAM/B,SAASiC,OAAOC,IAAI,CAACN,YAAYO,MAAM,CAC3C,CAACC,eAAe3B;QACd,MAAM4B,QAAQT,UAAU,CAACnB,IAAI;QAE7B,IAAI4B,UAAU3B,aAAa2B,UAAU,MAAM;YACzC,OAAOD;QACT;QAEA,IAAI3B,QAAQ,WAAW;YACrB,IAAI,OAAO4B,UAAU,UAAU;gBAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,+CAA+C,EAAE,OAAOD,MAAM,CAAC,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAME,cAAcF,MAAMG,IAAI;YAE9B,qEAAqE;YACrE,eAAe;YACf,IAAID,gBAAgB,UAAU;gBAC5B,MAAM,qBAEL,CAFK,IAAID,MACR,CAAC,4IAA4I,CAAC,GAD1I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,2EAA2E;YAC3E,8CAA8C;YAC9C,IAAIC,YAAYpB,MAAM,KAAK,GAAG;gBAC5B,MAAM,qBAEL,CAFK,IAAImB,MACR,CAAC,8GAA8G,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI7B,QAAQ,kBAAkB;YAC5B,IAAI,CAACgC,MAAMC,OAAO,CAACL,QAAQ;gBACzB,MAAM,qBAEL,CAFK,IAAIC,MACR,CAAC,4DAA4D,EAAED,MAAM,0CAA0C,CAAC,GAD5G,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAI,CAACA,MAAMlB,MAAM,EAAE;gBACjB,MAAM,qBAEL,CAFK,IAAImB,MACR,CAAC,uGAAuG,CAAC,GADrG,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEAD,MAAMM,OAAO,CAAC,CAACC;gBACb,IAAI,OAAOA,QAAQ,UAAU;oBAC3B,MAAM,qBAEL,CAFK,IAAIN,MACR,CAAC,4DAA4D,EAAEM,IAAI,WAAW,EAAE,OAAOA,IAAI,0CAA0C,CAAC,GADlI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAI,CAAC,CAACP,SAASA,MAAMQ,WAAW,KAAKZ,QAAQ;YAC3CG,aAAa,CAAC3B,IAAI,GAAG;gBACnB,GAAGoB,2BAAa,CAACpB,IAAI;gBACrB,GAAGwB,OAAOC,IAAI,CAACG,OAAOF,MAAM,CAAM,CAACW,GAAGC;oBACpC,MAAMC,IAAIX,KAAK,CAACU,EAAE;oBAClB,IAAIC,MAAMtC,aAAasC,MAAM,MAAM;wBACjCF,CAAC,CAACC,EAAE,GAAGC;oBACT;oBACA,OAAOF;gBACT,GAAG,CAAC,EAAE;YACR;QACF,OAAO;YACLV,aAAa,CAAC3B,IAAI,GAAG4B;QACvB;QAEA,OAAOD;IACT,GACA,CAAC;IAGH,iDAAiD;IACjD,uEAAuE;IACvE,6CAA6C;IAC7C,KAAIP,8BAAAA,2BAAa,CAACb,YAAY,qBAA1Ba,4BAA4BoB,GAAG,EAAE;QACnCtC,KAAIM,IAAI,CACN,CAAC,2HAA2H,CAAC;IAEjI;IAEA,MAAMa,SAAS;QAAE,GAAGD,2BAAa;QAAE,GAAG7B,MAAM;IAAC;IAE7C,IACE8B,EAAAA,uBAAAA,OAAOd,YAAY,qBAAnBc,qBAAqBoB,qBAAqB,KAC1CC,QAAQC,GAAG,CAACC,QAAQ,KAAK,eACzB;QACA,MAAM,qBAEL,CAFK,IAAIf,MACR,CAAC,sGAAsG,CAAC,GADpG,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAIgB,IAAAA,yBAAa,KAAI;YAEfxB,uBAEOA,uBAEAA,6BAAAA,uBAIAA;QATX,oEAAoE;QACpE,KAAIA,wBAAAA,OAAOd,YAAY,qBAAnBc,sBAAqBmB,GAAG,EAAE;YAC5B,MAAM,qBAAoD,CAApD,IAAIM,2BAAe,CAAC;gBAAEC,SAAS;YAAmB,IAAlD,qBAAA;uBAAA;4BAAA;8BAAA;YAAmD;QAC3D,OAAO,KAAI1B,wBAAAA,OAAOd,YAAY,qBAAnBc,sBAAqB2B,SAAS,EAAE;YACzC,MAAM,qBAA0D,CAA1D,IAAIF,2BAAe,CAAC;gBAAEC,SAAS;YAAyB,IAAxD,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE,OAAO,KAAI1B,wBAAAA,OAAOd,YAAY,sBAAnBc,8BAAAA,sBAAqB4B,KAAK,qBAA1B5B,4BAA4B6B,yBAAyB,EAAE;YAChE,MAAM,qBAEJ,CAFI,IAAIJ,2BAAe,CAAC;gBACxBC,SAAS;YACX,IAFM,qBAAA;uBAAA;4BAAA;8BAAA;YAEL;QACH,OAAO,KAAI1B,yBAAAA,OAAOd,YAAY,qBAAnBc,uBAAqB8B,cAAc,EAAE;YAC9C,MAAM,qBAA+D,CAA/D,IAAIL,2BAAe,CAAC;gBAAEC,SAAS;YAA8B,IAA7D,qBAAA;uBAAA;4BAAA;8BAAA;YAA8D;QACtE;IACF;IAEA,IAAI1B,OAAO+B,MAAM,KAAK,UAAU;QAC9B,IAAI/B,OAAOgC,IAAI,EAAE;YACf,MAAM,qBAEL,CAFK,IAAIxB,MACR,+HADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACyB,sBAAc,EAAE;YACnB,IAAIjC,OAAOkC,QAAQ,EAAE;gBACnBrD,KAAIM,IAAI,CACN;YAEJ;YACA,IAAIa,OAAOmC,SAAS,EAAE;gBACpBtD,KAAIM,IAAI,CACN;YAEJ;YACA,IAAIa,OAAOoC,OAAO,EAAE;gBAClBvD,KAAIM,IAAI,CACN;YAEJ;QACF;IACF;IAEA,IAAI,OAAOa,OAAOqC,WAAW,KAAK,UAAU;QAC1C,MAAM,qBAEL,CAFK,IAAI7B,MACR,CAAC,mDAAmD,EAAE,OAAOR,OAAOqC,WAAW,CAAC,sDAAsD,CAAC,GADnI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAI,OAAOrC,OAAOsC,QAAQ,KAAK,UAAU;QACvC,MAAM,qBAEL,CAFK,IAAI9B,MACR,CAAC,gDAAgD,EAAE,OAAOR,OAAOsC,QAAQ,CAAC,CAAC,CAAC,GADxE,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,IAAItC,OAAOsC,QAAQ,KAAK,IAAI;QAC1B,IAAItC,OAAOsC,QAAQ,KAAK,KAAK;YAC3B,MAAM,qBAEL,CAFK,IAAI9B,MACR,CAAC,iFAAiF,CAAC,GAD/E,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAACR,OAAOsC,QAAQ,CAACC,UAAU,CAAC,MAAM;YACpC,MAAM,qBAEL,CAFK,IAAI/B,MACR,CAAC,iDAAiD,EAAER,OAAOsC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAItC,OAAOsC,QAAQ,KAAK,KAAK;gBAWvBtC;YAVJ,IAAIA,OAAOsC,QAAQ,CAACE,QAAQ,CAAC,MAAM;gBACjC,MAAM,qBAEL,CAFK,IAAIhC,MACR,CAAC,iDAAiD,EAAER,OAAOsC,QAAQ,CAAC,CAAC,CAAC,GADlE,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,IAAItC,OAAOqC,WAAW,KAAK,IAAI;gBAC7BrC,OAAOqC,WAAW,GAAGrC,OAAOsC,QAAQ;YACtC;YAEA,IAAItC,EAAAA,cAAAA,OAAOyC,GAAG,qBAAVzC,YAAY0C,aAAa,MAAK,IAAI;gBACpC1C,OAAOyC,GAAG,CAACC,aAAa,GAAG1C,OAAOsC,QAAQ;YAC5C;QACF;IACF;IAEA,IAAItC,0BAAAA,OAAQ2C,MAAM,EAAE;QAClB,MAAMA,SAAsB3C,OAAO2C,MAAM;QAEzC,IAAI,OAAOA,WAAW,UAAU;YAC9B,MAAM,qBAEL,CAFK,IAAInC,MACR,CAAC,8CAA8C,EAAE,OAAOmC,OAAO,6EAA6E,CAAC,GADzI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAIA,OAAOC,aAAa,EAAE;YACxB,IAAI,CAACjC,MAAMC,OAAO,CAAC+B,OAAOC,aAAa,GAAG;gBACxC,MAAM,qBAEL,CAFK,IAAIpC,MACR,CAAC,2DAA2D,EAAE,OAAOmC,OAAOC,aAAa,CAAC,6EAA6E,CAAC,GADpK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,6DAA6D;YAC7D,MAAMC,WAAWF,OAAOC,aAAa,CAACE,IAAI,CACxC,CAACC,UACCA,QAAQC,QAAQ,KAAK,4BAA4BD,QAAQE,MAAM,KAAK;YAExE,IAAI,CAACJ,UAAU;gBACb,iDAAiD;gBACjDF,OAAOC,aAAa,CAACM,IAAI,CAAC;oBACxBF,UAAU;oBACVC,QAAQ;gBACV;YACF;QACF;QAEA,IAAIN,OAAOQ,cAAc,EAAE;gBAUrBjF;YATJ,IAAI,CAACyC,MAAMC,OAAO,CAAC+B,OAAOQ,cAAc,GAAG;gBACzC,MAAM,qBAEL,CAFK,IAAI3C,MACR,CAAC,4DAA4D,EAAE,OAAOmC,OAAOQ,cAAc,CAAC,6EAA6E,CAAC,GADtK,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YAEA,4DAA4D;YAC5D,2DAA2D;YAC3D,gBAAgB;YAChB,KAAIjF,sBAAAA,OAAOmE,WAAW,qBAAlBnE,oBAAoBqE,UAAU,CAAC,SAAS;gBAC1C,IAAI;oBACF,MAAMa,MAAM,IAAIC,IAAInF,OAAOmE,WAAW;oBACtC,MAAMiB,yBAAyBX,OAAOQ,cAAc,CAACL,IAAI,CAAC,CAACC,UACzDQ,IAAAA,sCAAkB,EAACR,SAASK;oBAG9B,qEAAqE;oBACrE,IAAI,CAACE,wBAAwB;wBAC3BX,OAAOQ,cAAc,CAACD,IAAI,CAAC;4BACzBM,UAAUJ,IAAII,QAAQ;4BACtBC,UAAUL,IAAIK,QAAQ,CAACC,OAAO,CAAC,MAAM;4BACrCC,MAAMP,IAAIO,IAAI;wBAChB;oBACF;gBACF,EAAE,OAAOjG,OAAO;oBACd,MAAM,qBAEL,CAFK,IAAI8C,MACR,CAAC,8CAA8C,EAAE9C,OAAO,GADpD,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;YACF;QACF;QAEA,IAAIiF,OAAOiB,OAAO,EAAE;YAClB,IAAI,CAACjD,MAAMC,OAAO,CAAC+B,OAAOiB,OAAO,GAAG;gBAClC,MAAM,qBAEL,CAFK,IAAIpD,MACR,CAAC,qDAAqD,EAAE,OAAOmC,OAAOiB,OAAO,CAAC,6EAA6E,CAAC,GADxJ,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;QAEA,IAAI,CAACjB,OAAOkB,MAAM,EAAE;YAClBlB,OAAOkB,MAAM,GAAG;QAClB;QAEA,IACElB,OAAOkB,MAAM,KAAK,aAClBlB,OAAOkB,MAAM,KAAK,YAClBlB,OAAO1E,IAAI,KAAK6F,+BAAkB,CAAC7F,IAAI,EACvC;YACA,MAAM,qBAEL,CAFK,IAAIuC,MACR,CAAC,kCAAkC,EAAEmC,OAAOkB,MAAM,CAAC,sKAAsK,CAAC,GADtN,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IACElB,OAAO1E,IAAI,KAAK6F,+BAAkB,CAAC7F,IAAI,IACvC+B,OAAOsC,QAAQ,IACf,CAACyB,IAAAA,4BAAa,EAACpB,OAAO1E,IAAI,EAAE+B,OAAOsC,QAAQ,GAC3C;YACAK,OAAO1E,IAAI,GAAG,GAAG+B,OAAOsC,QAAQ,GAAGK,OAAO1E,IAAI,EAAE;QAClD;QAEA,8EAA8E;QAC9E,IACE0E,OAAO1E,IAAI,IACX,CAAC0E,OAAO1E,IAAI,CAACuE,QAAQ,CAAC,QACrBG,CAAAA,OAAOkB,MAAM,KAAK,aAAa7D,OAAOE,aAAa,AAAD,GACnD;YACAyC,OAAO1E,IAAI,IAAI;QACjB;QAEA,IAAI0E,OAAOqB,UAAU,EAAE;YACrB,IAAIrB,OAAOkB,MAAM,KAAK,aAAalB,OAAOkB,MAAM,KAAK,UAAU;gBAC7D,MAAM,qBAEL,CAFK,IAAIrD,MACR,CAAC,kCAAkC,EAAEmC,OAAOkB,MAAM,CAAC,uFAAuF,CAAC,GADvI,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACA,MAAMI,eAAeC,IAAAA,UAAI,EAACrE,KAAK8C,OAAOqB,UAAU;YAChD,IAAI,CAACG,IAAAA,cAAU,EAACF,eAAe;gBAC7B,MAAM,qBAEL,CAFK,IAAIzD,MACR,CAAC,+CAA+C,EAAEyD,aAAa,EAAE,CAAC,GAD9D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;YACAtB,OAAOqB,UAAU,GAAGC;QACtB;IACF;IAEA1E,qBACES,QACA,6BACA,MACA,mGACAf,gBACAZ;IAGFd,4BACEyC,QACA,oCACA,CAAC,yIAAyI,EAAEf,eAAe,CAAC,CAAC,EAC7JZ;IAGFd,4BACEyC,QACA,sBACA,CAAC,8GAA8G,EAAEf,eAAe,CAAC,CAAC,EAClIZ;IAGFd,4BACEyC,QACA,8BACA,CAAC,+FAA+F,EAAEf,eAAe,CAAC,CAAC,EACnHZ;IAGFd,4BACEyC,QACA,+BACA,CAAC,gGAAgG,EAAEf,eAAe,CAAC,CAAC,EACpHZ;IAGF,MAAM+F,iCAAiC7G,4BACrCyC,QACA,uCACA,CAAC,2GAA2G,EAAEf,eAAe,kBAAkB,CAAC,EAChJZ;IAEF,IACE+F,kCACApE,OAAOqE,aAAa,KAAK,WACzBrE,wBAAAA,OAAOqE,aAAa,qBAApBrE,sBAAsBsE,qBAAqB,KAC3CtE,OAAOqE,aAAa,CAACC,qBAAqB,KAAKtE,OAAOqE,aAAa,CAACE,QAAQ,EAC5E;QACA1F,KAAIC,QAAQ,CACV,CAAC,yDAAyD,EAAEkB,OAAOqE,aAAa,CAACC,qBAAqB,CAAC,iCAAiC,EAAEtE,OAAOqE,aAAa,CAACE,QAAQ,CAAC,sCAAsC,EAAEvE,OAAOqE,aAAa,CAACC,qBAAqB,CAAC,8BAA8B,CAAC;QAE5RtE,OAAOqE,aAAa,CAACE,QAAQ,GAAGvE,OAAOqE,aAAa,CAACC,qBAAqB;IAC5E;IAEA9G,wCACEwC,QACA,wBACA,iCACAf,gBACAZ;IAEFb,wCACEwC,QACA,oCACA,0BACAf,gBACAZ;IAEFb,wCACEwC,QACA,SACA,kBACAf,gBACAZ;IAEFb,wCACEwC,QACA,oBACA,6BACAf,gBACAZ;IAEFb,wCACEwC,QACA,WACA,oBACAf,gBACAZ;IAEFb,wCACEwC,QACA,yBACA,kCACAf,gBACAZ;IAEFb,wCACEwC,QACA,iBACA,0BACAf,gBACAZ;IAEFb,wCACEwC,QACA,YACA,cACAf,gBACAZ;IAEFb,wCACEwC,QACA,yBACA,yBACAf,gBACAZ;IAEFb,wCACEwC,QACA,6BACA,6BACAf,gBACAZ;IAEFb,wCACEwC,QACA,6BACA,6BACAf,gBACAZ;IAGF,IAAI,AAAC2B,OAAOd,YAAY,CAASsF,gBAAgB,EAAE;QACjD,IAAI,CAACnG,QAAQ;YACXQ,KAAIM,IAAI,CACN,CAAC,iGAAiG,CAAC;QAEvG;QACAa,OAAO+B,MAAM,GAAG;IAClB;IAEA,IACE,SAAO/B,wBAAAA,OAAOd,YAAY,sBAAnBc,qCAAAA,sBAAqByE,aAAa,qBAAlCzE,mCAAoC0E,aAAa,MAAK,aAC7D;YAEE1E;QADF,MAAMO,QAAQoE,UACZ3E,sCAAAA,OAAOd,YAAY,CAACuF,aAAa,qBAAjCzE,oCAAmC0E,aAAa,CAACE,QAAQ;QAE3D,IAAIC,MAAMtE,UAAUA,QAAQ,GAAG;YAC7B,MAAM,qBAEL,CAFK,IAAIC,MACR,8KADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEAhD,wCACEwC,QACA,qBACA,qBACAf,gBACAZ;IAEFb,wCACEwC,QACA,8BACA,8BACAf,gBACAZ;IAEFb,wCACEwC,QACA,6BACA,6BACAf,gBACAZ;IAGF,IACE2B,CAAAA,0BAAAA,OAAQ8E,qBAAqB,KAC7B,CAACC,IAAAA,gBAAU,EAAC/E,OAAO8E,qBAAqB,GACxC;QACA9E,OAAO8E,qBAAqB,GAAGE,IAAAA,aAAO,EAAChF,OAAO8E,qBAAqB;QACnE,IAAI,CAACzG,QAAQ;YACXQ,KAAIM,IAAI,CACN,CAAC,iDAAiD,EAAEa,OAAO8E,qBAAqB,EAAE;QAEtF;IACF;IAEA,IACE9E,CAAAA,2BAAAA,wBAAAA,OAAQd,YAAY,sBAApBc,6BAAAA,sBAAsB4B,KAAK,qBAA3B5B,2BAA6BiF,IAAI,KACjC,CAACF,IAAAA,gBAAU,EAAC/E,OAAOd,YAAY,CAAC0C,KAAK,CAACqD,IAAI,GAC1C;QACAjF,OAAOd,YAAY,CAAC0C,KAAK,CAACqD,IAAI,GAAGD,IAAAA,aAAO,EAAChF,OAAOd,YAAY,CAAC0C,KAAK,CAACqD,IAAI;QACvE,IAAI,CAAC5G,QAAQ;YACXQ,KAAIM,IAAI,CACN,CAAC,mDAAmD,EAAEa,OAAOd,YAAY,CAAC0C,KAAK,CAACqD,IAAI,EAAE;QAE1F;IACF;IAEA,6BAA6B;IAC7B,IAAI5D,QAAQC,GAAG,CAAC4D,kBAAkB,EAAE;QAClClF,OAAOmF,YAAY,GAAG9D,QAAQC,GAAG,CAAC4D,kBAAkB;IACtD;IAEA,IAAIlF,CAAAA,0BAAAA,OAAQ8E,qBAAqB,KAAI,EAAC9E,2BAAAA,wBAAAA,OAAQd,YAAY,sBAApBc,8BAAAA,sBAAsB4B,KAAK,qBAA3B5B,4BAA6BiF,IAAI,GAAE;QACvEG,IAAAA,UAAI,EACFpF,QACA;YAAC;YAAgB;YAAS;SAAO,EACjCA,OAAO8E,qBAAqB;IAEhC;IAEA,2CAA2C;IAC3C,IAAI,EAAC9E,0BAAAA,OAAQ8E,qBAAqB,KAAI,EAAC9E,2BAAAA,wBAAAA,OAAQd,YAAY,sBAApBc,8BAAAA,sBAAsB4B,KAAK,qBAA3B5B,4BAA6BiF,IAAI,GAAE;QACxE,IAAII,UAAUC,IAAAA,qBAAW,EAACzF;QAE1B,IAAIwF,SAAS;gBAMNrF,6BAAAA;YALL,IAAI,EAACA,0BAAAA,OAAQ8E,qBAAqB,GAAE;gBAClC9E,OAAO8E,qBAAqB,GAAGO;gBAC/BtF,2BAAa,CAAC+E,qBAAqB,GAAG9E,OAAO8E,qBAAqB;YACpE;YAEA,IAAI,EAAC9E,2BAAAA,yBAAAA,OAAQd,YAAY,sBAApBc,8BAAAA,uBAAsB4B,KAAK,qBAA3B5B,4BAA6BiF,IAAI,GAAE;gBACtCG,IAAAA,UAAI,EAACpF,QAAQ;oBAAC;oBAAgB;oBAAS;iBAAO,EAAEqF;gBAChDD,IAAAA,UAAI,EAACrF,2BAAa,EAAE;oBAAC;oBAAgB;oBAAS;iBAAO,EAAEsF;YACzD;QACF;IACF;IAEAE,IAAAA,+CAA4B,EAACvF,UAAUD,2BAAa;IAEpD,IAAIC,OAAOgC,IAAI,EAAE;QACf,MAAMwD,YAAYC,QAAQC,IAAAA,qBAAO,EAAC7F,KAAK;QAEvC,IAAI2F,WAAW;YACbjI,4BACEyC,QACA,QACA,CAAC,sBAAsB,EAAEf,eAAe,uKAAuK,CAAC,EAChNZ;QAEJ;QAEA,MAAM,EAAE2D,IAAI,EAAE,GAAGhC;QACjB,MAAM2F,WAAW,OAAO3D;QAExB,IAAI2D,aAAa,UAAU;YACzB,MAAM,qBAEL,CAFK,IAAInF,MACR,CAAC,4CAA4C,EAAEmF,SAAS,2EAA2E,CAAC,GADhI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,CAAChF,MAAMC,OAAO,CAACoB,KAAK4D,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIpF,MACR,CAAC,mDAAmD,EAAE,OAAOwB,KAAK4D,OAAO,CAAC,2EAA2E,CAAC,GADlJ,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI5D,KAAK4D,OAAO,CAACvG,MAAM,GAAG,OAAO,CAAChB,QAAQ;YACxCQ,KAAIM,IAAI,CACN,CAAC,SAAS,EAAE6C,KAAK4D,OAAO,CAACvG,MAAM,CAAC,mLAAmL,CAAC;QAExN;QAEA,MAAMwG,oBAAoB,OAAO7D,KAAK8D,aAAa;QAEnD,IAAI,CAAC9D,KAAK8D,aAAa,IAAID,sBAAsB,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAIrF,MACR,CAAC,0HAA0H,CAAC,GADxH,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI,OAAOwB,KAAK4B,OAAO,KAAK,eAAe,CAACjD,MAAMC,OAAO,CAACoB,KAAK4B,OAAO,GAAG;YACvE,MAAM,qBAEL,CAFK,IAAIpD,MACR,CAAC,2IAA2I,EAAE,OAAOwB,KAAK4B,OAAO,CAAC,2EAA2E,CAAC,GAD1O,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,IAAI5B,KAAK4B,OAAO,EAAE;YAChB,MAAMmC,qBAAqB/D,KAAK4B,OAAO,CAACoC,MAAM,CAAC,CAACC;oBAYfjE;gBAX/B,IAAI,CAACiE,QAAQ,OAAOA,SAAS,UAAU,OAAO;gBAC9C,IAAI,CAACA,KAAKH,aAAa,EAAE,OAAO;gBAChC,IAAI,CAACG,KAAKC,MAAM,IAAI,OAAOD,KAAKC,MAAM,KAAK,UAAU,OAAO;gBAE5D,IAAID,KAAKC,MAAM,CAACC,QAAQ,CAAC,MAAM;oBAC7BC,QAAQjH,IAAI,CACV,CAAC,cAAc,EAAE8G,KAAKC,MAAM,CAAC,2GAA2G,CAAC;oBAE3I,OAAO;gBACT;gBAEA,MAAMG,0BAAyBrE,gBAAAA,KAAK4B,OAAO,qBAAZ5B,cAAcsE,IAAI,CAC/C,CAACC,UACCA,QAAQT,aAAa,KAAKG,KAAKH,aAAa,IAC5CS,QAAQL,MAAM,KAAKD,KAAKC,MAAM;gBAGlC,IAAI,CAAC7H,UAAUgI,wBAAwB;oBACrCD,QAAQjH,IAAI,CACV,CAAC,KAAK,EAAE8G,KAAKC,MAAM,CAAC,KAAK,EAAEG,uBAAuBH,MAAM,CAAC,8BAA8B,EAAED,KAAKH,aAAa,CAAC,+DAA+D,CAAC;oBAE9K,OAAO;gBACT;gBAEA,IAAIU,mBAAmB;gBAEvB,IAAI7F,MAAMC,OAAO,CAACqF,KAAKL,OAAO,GAAG;oBAC/B,KAAK,MAAMa,UAAUR,KAAKL,OAAO,CAAE;wBACjC,IAAI,OAAOa,WAAW,UAAUD,mBAAmB;wBAEnD,KAAK,MAAME,cAAc1E,KAAK4B,OAAO,IAAI,EAAE,CAAE;4BAC3C,IAAI8C,eAAeT,MAAM;4BACzB,IAAIS,WAAWd,OAAO,IAAIc,WAAWd,OAAO,CAACO,QAAQ,CAACM,SAAS;gCAC7DL,QAAQjH,IAAI,CACV,CAAC,KAAK,EAAE8G,KAAKC,MAAM,CAAC,KAAK,EAAEQ,WAAWR,MAAM,CAAC,wBAAwB,EAAEO,OAAO,sEAAsE,CAAC;gCAEvJD,mBAAmB;gCACnB;4BACF;wBACF;oBACF;gBACF;gBAEA,OAAOA;YACT;YAEA,IAAIT,mBAAmB1G,MAAM,GAAG,GAAG;gBACjC,MAAM,qBAML,CANK,IAAImB,MACR,CAAC,8BAA8B,EAAEuF,mBAC9BY,GAAG,CAAC,CAACV,OAAcW,KAAKC,SAAS,CAACZ,OAClC/B,IAAI,CACH,MACA,8KAA8K,CAAC,GAL/K,qBAAA;2BAAA;gCAAA;kCAAA;gBAMN;YACF;QACF;QAEA,IAAI,CAACvD,MAAMC,OAAO,CAACoB,KAAK4D,OAAO,GAAG;YAChC,MAAM,qBAEL,CAFK,IAAIpF,MACR,CAAC,2FAA2F,EAAE,OAAOwB,KAAK4D,OAAO,CAAC,2EAA2E,CAAC,GAD1L,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMkB,iBAAiB9E,KAAK4D,OAAO,CAACI,MAAM,CACxC,CAACS,SAAgB,OAAOA,WAAW;QAGrC,IAAIK,eAAezH,MAAM,GAAG,GAAG;YAC7B,MAAM,qBAOL,CAPK,IAAImB,MACR,CAAC,gDAAgD,EAAEsG,eAChDH,GAAG,CAACI,QACJ7C,IAAI,CACH,MACA,wEAAwE,CAAC,GAC3E,CAAC,+HAA+H,CAAC,GAN/H,qBAAA;uBAAA;4BAAA;8BAAA;YAON;QACF;QAEA,IAAI,CAAClC,KAAK4D,OAAO,CAACO,QAAQ,CAACnE,KAAK8D,aAAa,GAAG;YAC9C,MAAM,qBAEL,CAFK,IAAItF,MACR,CAAC,0IAA0I,CAAC,GADxI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAMwG,oBAAoB,IAAIC;QAC9B,MAAMC,mBAAmB,IAAID;QAE7BjF,KAAK4D,OAAO,CAAC/E,OAAO,CAAC,CAAC4F;YACpB,MAAMU,cAAcV,OAAOW,WAAW;YACtC,IAAIJ,kBAAkBK,GAAG,CAACF,cAAc;gBACtCD,iBAAiBI,GAAG,CAACb;YACvB;YACAO,kBAAkBM,GAAG,CAACH;QACxB;QAEA,IAAID,iBAAiBK,IAAI,GAAG,GAAG;YAC7B,MAAM,qBAKL,CALK,IAAI/G,MACR,CAAC,kEAAkE,CAAC,GAClE,GAAG;mBAAI0G;aAAiB,CAAChD,IAAI,CAAC,MAAM,EAAE,CAAC,GACvC,CAAC,yCAAyC,CAAC,GAC3C,CAAC,wEAAwE,CAAC,GAJxE,qBAAA;uBAAA;4BAAA;8BAAA;YAKN;QACF;QAEA,2CAA2C;QAC3ClC,KAAK4D,OAAO,GAAG;YACb5D,KAAK8D,aAAa;eACf9D,KAAK4D,OAAO,CAACI,MAAM,CAAC,CAACS,SAAWA,WAAWzE,KAAK8D,aAAa;SACjE;QAED,MAAM0B,sBAAsB,OAAOxF,KAAKyF,eAAe;QAEvD,IACED,wBAAwB,aACxBA,wBAAwB,aACxB;YACA,MAAM,qBAEL,CAFK,IAAIhH,MACR,CAAC,yEAAyE,EAAEgH,oBAAoB,2EAA2E,CAAC,GADxK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAIxH,OAAOqE,aAAa,KAAK,WAASrE,yBAAAA,OAAOqE,aAAa,qBAApBrE,uBAAsBuE,QAAQ,GAAE;QACpE,MAAM,EAAEA,QAAQ,EAAE,GAAGvE,OAAOqE,aAAa;QACzC,MAAMqD,gBAAgB;YACpB;YACA;YACA;YACA;SACD;QAED,IAAI,CAACA,cAAcvB,QAAQ,CAAC5B,WAAW;YACrC,MAAM,qBAIL,CAJK,IAAI/D,MACR,CAAC,0DAA0D,EAAEkH,cAAcxD,IAAI,CAC7E,MACA,WAAW,EAAEK,UAAU,GAHrB,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,IAAIvE,OAAOd,YAAY,EAAE;YAElBa,8BAGkBA,uCAAAA,8BAKpBA,wCAAAA;QATHC,OAAOd,YAAY,CAACyI,SAAS,GAAG;gBAC3B5H,+BAAAA,2BAAa,CAACb,YAAY,qBAA1Ba,6BAA4B4H,SAAS,AAAxC;YACA,GAAG3H,OAAOd,YAAY,CAACyI,SAAS;QAClC;QACA,MAAMC,kBAAiB7H,+BAAAA,2BAAa,CAACb,YAAY,sBAA1Ba,wCAAAA,6BAA4B4H,SAAS,qBAArC5H,qCAAuC,CAAC,UAAU;QACzE,IACE,CAAC6H,kBACDA,eAAeC,UAAU,KAAKjJ,aAC9BgJ,eAAeE,MAAM,KAAKlJ,aAC1B,GAACmB,+BAAAA,2BAAa,CAACb,YAAY,sBAA1Ba,yCAAAA,6BAA4BgI,UAAU,qBAAtChI,uCAAwCiI,MAAM,GAC/C;YACA,MAAM,qBAA0C,CAA1C,IAAIxH,MAAM,kCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyC;QACjD;QACA,MAAMyH,0BAA0BjI,OAAOd,YAAY,CAACyI,SAAS,CAAC,UAAU;QACxE,IAAI,CAACM,yBAAyB;YAC5BjI,OAAOd,YAAY,CAACyI,SAAS,CAAC,UAAU,GAAGC;QAC7C,OAAO;YACL,IAAIK,wBAAwBC,KAAK,KAAKtJ,WAAW;oBACvBoB,iCAEHD,yCAAAA;gBAFrB,MAAMoI,mBAAkBnI,kCAAAA,OAAOd,YAAY,CAAC6I,UAAU,qBAA9B/H,gCAAgCgI,MAAM;gBAC9DC,wBAAwBC,KAAK,GAC3BC,qBAAmBpI,+BAAAA,2BAAa,CAACb,YAAY,sBAA1Ba,0CAAAA,6BAA4BgI,UAAU,qBAAtChI,wCAAwCiI,MAAM;YACrE;YACA,IAAIC,wBAAwBJ,UAAU,KAAKjJ,WAAW;gBACpDqJ,wBAAwBJ,UAAU,GAAGD,eAAeC,UAAU;YAChE;YACA,IAAII,wBAAwBH,MAAM,KAAKlJ,WAAW;gBAChDqJ,wBAAwBH,MAAM,GAC5B9H,OAAOoI,UAAU,IAAIR,eAAeE,MAAM;YAC9C;QACF;QACA,+CAA+C;QAC/C,MAAMO,0BAA0BrI,OAAOd,YAAY,CAACyI,SAAS,CAAC,UAAU;QACxE,IACEU,2BACAA,wBAAwBH,KAAK,KAAKtJ,WAClC;gBAGyBoB,kCAEHD,yCAAAA;YAJtB,iFAAiF;YACjF,wDAAwD;YACxD,MAAMuI,oBAAmBtI,mCAAAA,OAAOd,YAAY,CAAC6I,UAAU,qBAA9B/H,iCAAgCuI,OAAO;YAChEF,wBAAwBH,KAAK,GAC3BI,sBAAoBvI,+BAAAA,2BAAa,CAACb,YAAY,sBAA1Ba,0CAAAA,6BAA4BgI,UAAU,qBAAtChI,wCAAwCwI,OAAO;QACvE;IACF;IAEA,KAAIvI,wBAAAA,OAAOd,YAAY,qBAAnBc,sBAAqBwI,aAAa,EAAE;QACtC,MAAMC,0BAA0B;QAEhC,IAAI,OAAOzI,OAAOd,YAAY,CAACsJ,aAAa,KAAK,UAAU;YACzD,MAAM,qBAEL,CAFK,IAAIhI,MACR,CAAC,+GAA+G,EAAEoG,KAAKC,SAAS,CAAC7G,OAAOd,YAAY,CAACsJ,aAAa,GAAG,GADjK,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA,MAAME,cAAcvI,OAAOC,IAAI,CAACJ,OAAOd,YAAY,CAACsJ,aAAa;QACjE,MAAMG,sBAA8D,EAAE;QAEtE,KAAK,MAAMhK,OAAO+J,YAAa;YAC7B,IAAI,CAACD,wBAAwBG,IAAI,CAACjK,MAAM;gBACtCgK,oBAAoBzF,IAAI,CAAC;oBACvBvE;oBACAP,QAAQ;gBACV;YACF,OAAO;gBACL,MAAMyK,cAAc7I,OAAOd,YAAY,CAACsJ,aAAa,CAAC7J,IAAI;gBAE1D,IAAIkK,eAAe,CAAC1E,IAAAA,cAAU,EAAC0E,cAAc;oBAC3CF,oBAAoBzF,IAAI,CAAC;wBACvBvE;wBACAP,QAAQ,CAAC,qDAAqD,EAAEyK,aAAa;oBAC/E;gBACF;YACF;YACA,IAAIF,oBAAoBtJ,MAAM,EAAE;gBAC9B,MAAM,qBAEL,CAFK,IAAImB,MACR,CAAC,oEAAoE,EAAEmI,oBAAoBhC,GAAG,CAAC,CAACV,OAAS,GAAGtH,IAAI,EAAE,EAAEsH,KAAK7H,MAAM,EAAE,EAAE8F,IAAI,CAAC,OAAO,GAD3I,qBAAA;2BAAA;gCAAA;kCAAA;gBAEN;YACF;QACF;IACF;IAEA,MAAM4E,gCAAgC9I,OAAO+I,iBAAiB;IAC9D,kJAAkJ;IAClJ,6EAA6E;IAC7E/I,OAAO+I,iBAAiB,GAAG;QACzB,GAAID,iCAAiC,CAAC,CAAC;QACvC,gFAAgF;QAChF,uBAAuB;YACrBE,WAAW;QACb;QACAC,QAAQ;YACND,WAAW;QACb;IACF;IAEA,MAAME,qCACJlJ,EAAAA,wBAAAA,OAAOd,YAAY,qBAAnBc,sBAAqBmJ,sBAAsB,KAAI,EAAE;IACnD,IAAI,CAACnJ,OAAOd,YAAY,EAAE;QACxBc,OAAOd,YAAY,GAAG,CAAC;IACzB;IAEAc,OAAOd,YAAY,CAACiK,sBAAsB,GAAG;WACxC,IAAIlC,IAAI;eACNiC;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA,4EAA4E;YAC5E,mCAAmC;YACnC,0EAA0E;YAC1E,wBAAwB;YACxB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;KACF;IAED,IAAI,CAAClJ,OAAOoJ,eAAe,EAAE;QAC3B,oGAAoG;QACpGpJ,OAAOoJ,eAAe,GAAGC,oCAA6B;IACxD;IAEA,4EAA4E;IAC5E,8EAA8E;IAC9E,2BAA2B;IAC3B,IAAIrJ,OAAOd,YAAY,CAACoK,QAAQ,KAAK1K,WAAW;QAC9CoB,OAAOd,YAAY,CAACoK,QAAQ,GAAGtJ,OAAOd,YAAY,CAACyC,SAAS;IAC9D;IAEA,OAAO3B;AACT;AAEe,eAAe5C,WAC5BmM,KAAa,EACb1J,GAAW,EACX,EACE2J,YAAY,EACZC,SAAS,EACTpL,SAAS,IAAI,EACbqL,gBAAgB,EAChBC,wBAAwB,EAOzB,GAAG,CAAC,CAAC;IAEN,IAAI,CAACtI,QAAQC,GAAG,CAACsI,4BAA4B,EAAE;QAC7C,IAAI;YACFC,IAAAA,4BAAe;QACjB,EAAE,OAAOC,KAAK;YACZ,gDAAgD;YAChD,yBAAyB;YACzB,IAAI,CAACzI,QAAQC,GAAG,CAACyI,gCAAgC,EAAE;gBACjD,MAAMD;YACR;QACF;IACF;IAEA,IAAIzI,QAAQC,GAAG,CAACyI,gCAAgC,EAAE;QAChD,OAAOnD,KAAKoD,KAAK,CAAC3I,QAAQC,GAAG,CAACyI,gCAAgC;IAChE;IAEA,MAAME,SAAS5L,SACX;QACEc,MAAM,KAAO;QACb+K,MAAM,KAAO;QACbxM,OAAO,KAAO;IAChB,IACAmB;IAEJsL,IAAAA,kBAAa,EAACtK,KAAK0J,UAAUa,mCAAwB,EAAEH;IAEvD,IAAIhL,iBAAiB;IAErB,IAAIuK,cAAc;QAChB,OAAO5J,eACLC,KACA;YACEwK,cAAc;YACdpL;YACA,GAAGuK,YAAY;QACjB,GACAnL;IAEJ;IAEA,MAAMJ,OAAO,MAAMqM,IAAAA,eAAM,EAACC,uBAAY,EAAE;QAAEC,KAAK3K;IAAI;IAEnD,IAAIwB,QAAQC,GAAG,CAACmJ,gBAAgB,EAAE;QAChC,IAAIxM,MAAM;YACRY,KAAIqL,IAAI,CAAC,CAAC,oBAAoB,EAAEjM,MAAM;QACxC,OAAO;YACLY,KAAIqL,IAAI,CAAC;QACX;IACF;IAEA,2BAA2B;IAC3B,IAAIjM,wBAAAA,KAAMoB,MAAM,EAAE;YA2FZS,iBAcFA,gCAAAA,0BACCA,iCAAAA,2BAmBCA;QA5HJb,iBAAiByL,IAAAA,cAAQ,EAACzM;QAE1B,IAAI0M;QACJ,IAAI;YACF,MAAMC,YAAYzK,OAAO0K,MAAM,CAAC,CAAC,GAAGxJ,QAAQC,GAAG;YAE/C,uEAAuE;YACvE,sEAAsE;YACtE,8BAA8B;YAC9B,IAAID,QAAQC,GAAG,CAACmJ,gBAAgB,KAAK,QAAQ;gBAC3C,4DAA4D;gBAC5D,0DAA0D;gBAC1D,8CAA8C;gBAC9CE,mBAAmBG,QAAQ7M;YAC7B,OAAO,IAAIgB,mBAAmB,kBAAkB;gBAC9C0L,mBAAmB,MAAMI,IAAAA,gCAAe,EAAC;oBACvCC,gBAAgB/M;oBAChBuM,KAAK3K;gBACP;YACF,OAAO;gBACL8K,mBAAmB,MAAM,MAAM,CAACM,IAAAA,kBAAa,EAAChN,MAAMiN,IAAI;YAC1D;YACA,MAAMC,SAA6B,CAAC;YAEpC,KAAK,MAAMxM,OAAOwB,OAAOC,IAAI,CAACiB,QAAQC,GAAG,EAAG;gBAC1C,IAAIsJ,SAAS,CAACjM,IAAI,KAAK0C,QAAQC,GAAG,CAAC3C,IAAI,EAAE;oBACvCwM,MAAM,CAACxM,IAAI,GAAG0C,QAAQC,GAAG,CAAC3C,IAAI;gBAChC;YACF;YACAyM,IAAAA,qBAAgB,EAACD;YAEjB,IAAI1B,WAAW;gBACb,OAAOkB;YACT;QACF,EAAE,OAAOb,KAAK;YACZ,0EAA0E;YAC1EG,OAAOvM,KAAK,CACV,CAAC,eAAe,EAAEuB,eAAe,uEAAuE,CAAC;YAE3G,MAAM6K;QACR;QAEA,MAAMhK,aAAc,MAAMxC,IAAAA,6BAAe,EACvCiM,OACAoB,iBAAiBU,OAAO,IAAIV;QAG9B,IAAI,CAACtJ,QAAQC,GAAG,CAACgK,YAAY,EAAE;YAC7B,iEAAiE;YACjE,MAAM,EAAEC,YAAY,EAAE,GACpBT,QAAQ;YACV,MAAMU,QAAQD,aAAaE,SAAS,CAAC3L;YAErC,IAAI0L,MAAME,OAAO,KAAK,OAAO;gBAC3B,uBAAuB;gBACvB,MAAMC,WAAW;oBAAC,CAAC,QAAQ,EAAE1M,eAAe,mBAAmB,CAAC;iBAAC;gBAEjE,MAAM,CAAC2M,eAAejO,WAAW,GAAGF,6BAClC+N,MAAM9N,KAAK;gBAEb,kBAAkB;gBAClB,KAAK,MAAMA,SAASkO,cAAe;oBACjCD,SAASzI,IAAI,CAAC,CAAC,IAAI,EAAExF,OAAO;gBAC9B;gBAEA,uBAAuB;gBACvBiO,SAASzI,IAAI,CACX;gBAGF,IAAIvF,YAAY;oBACd,KAAK,MAAMK,WAAW2N,SAAU;wBAC9BvF,QAAQ1I,KAAK,CAACM;oBAChB;oBACA,MAAM6N,IAAAA,0BAAY,EAAC;gBACrB,OAAO;oBACL,KAAK,MAAM7N,WAAW2N,SAAU;wBAC9B1B,OAAO9K,IAAI,CAACnB;oBACd;gBACF;YACF;QACF;QAEA,IAAI8B,WAAWgM,MAAM,IAAIhM,WAAWgM,MAAM,KAAK,UAAU;YACvD,MAAM,qBAGL,CAHK,IAAItL,MACR,CAAC,gDAAgD,EAAEvB,eAAe,GAAG,CAAC,GACpE,iFAFE,qBAAA;uBAAA;4BAAA;8BAAA;YAGN;QACF;QAEA,KAAIa,kBAAAA,WAAW2C,GAAG,qBAAd3C,gBAAgB4C,aAAa,EAAE;YACjC,MAAM,EAAEA,aAAa,EAAE,GAAG5C,WAAW2C,GAAG,IAAK,CAAC;YAC9C3C,WAAW2C,GAAG,GAAG3C,WAAW2C,GAAG,IAAI,CAAC;YACpC3C,WAAW2C,GAAG,CAACC,aAAa,GAC1B,AAACA,CAAAA,CAAAA,iCAAAA,cAAeF,QAAQ,CAAC,QACrBE,cAAcqJ,KAAK,CAAC,GAAG,CAAC,KACxBrJ,aAAY,KAAM;QAC1B;QAEA,IAAIiH,0BAA0B;YAC5B7J,WAAW6J,wBAAwB,GAAGA;QACxC;QAEA,IACE7J,EAAAA,2BAAAA,WAAWZ,YAAY,sBAAvBY,iCAAAA,yBAAyB8B,KAAK,qBAA9B9B,+BAAgCkM,OAAO,KACvC,GAAClM,4BAAAA,WAAWZ,YAAY,sBAAvBY,kCAAAA,0BAAyB8B,KAAK,qBAA9B9B,gCAAgCmM,KAAK,GACtC;YACAhC,OAAO9K,IAAI,CACT,sIACE,uFACA,4FACA;YAGJ,MAAM8M,QAA2C,CAAC;YAClD,KAAK,MAAM,CAACnL,KAAKkL,QAAQ,IAAI7L,OAAO+L,OAAO,CACzCpM,WAAWZ,YAAY,CAAC0C,KAAK,CAACoK,OAAO,EACpC;gBACDC,KAAK,CAAC,MAAMnL,IAAI,GAAGkL;YACrB;YAEAlM,WAAWZ,YAAY,CAAC0C,KAAK,CAACqK,KAAK,GAAGA;QACxC;QAEA,KAAInM,4BAAAA,WAAWZ,YAAY,qBAAvBY,0BAAyBqM,eAAe,EAAE;gBAEf,MAAC;YAD9B,MAAM,EAAEC,YAAY,EAAE,GAAGtB,QAAQ;YACjC,MAAMuB,wBAAwB,QAAA,MAAMD,oCAAP,OAAA,AAAC,MAAuBE,GAAG,qBAA3B,KAA6BC,SAAS;YAEnE,IAAI,CAACF,sBAAsB;gBACzBpC,OAAO9K,IAAI,CACT,CAAC,+GAA+G,CAAC;gBAEnHW,WAAWZ,YAAY,CAACiN,eAAe,GAAG;YAC5C;QACF;QAEA,yCAAyC;QACzC,IAAIrM,CAAAA,8BAAAA,WAAYsJ,eAAe,aAAYoD,QAAQ;YACjD,oGAAoG;YACpG1M,WAAWsJ,eAAe,GAAGtJ,WAAWsJ,eAAe,CAACqD,MAAM;QAChE;QAEA/C,oCAAAA,iBAAmB5J;QACnB,MAAM4M,iBAAiB9M,eACrBC,KACA;YACEwK,cAAcsC,IAAAA,cAAQ,EAAC9M,KAAK5B;YAC5B2O,YAAY3O;YACZgB;YACA,GAAGa,UAAU;QACf,GACAzB;QAEF,OAAOqO;IACT,OAAO;QACL,MAAMG,iBAAiBnC,IAAAA,cAAQ,EAACH,uBAAY,CAAC,EAAE,EAAEuC,IAAAA,aAAO,EAACvC,uBAAY,CAAC,EAAE;QACxE,MAAMwC,oBAAoBzC,eAAM,CAAC0C,IAAI,CACnC;YACE,GAAGH,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,KAAK,CAAC;YACxB,GAAGA,eAAe,IAAI,CAAC;YACvB,GAAGA,eAAe,IAAI,CAAC;SACxB,EACD;YAAErC,KAAK3K;QAAI;QAEb,IAAIkN,qCAAAA,kBAAmB1N,MAAM,EAAE;YAC7B,MAAM,qBAIL,CAJK,IAAImB,MACR,CAAC,yBAAyB,EAAEkK,IAAAA,cAAQ,EAClCqC,mBACA,0GAA0G,CAAC,GAHzG,qBAAA;uBAAA;4BAAA;8BAAA;YAIN;QACF;IACF;IAEA,qDAAqD;IACrD,iEAAiE;IACjE,MAAML,iBAAiB9M,eACrBC,KACAE,2BAAa,EACb1B;IAEFqO,eAAezN,cAAc,GAAGA;IAChCsG,IAAAA,+CAA4B,EAACmH;IAC7B,OAAOA;AACT;AAOO,SAASrP,kCACd4P,0BAAsD;IAEtD,MAAMC,iCAAkE,EAAE;IAE1E,IAAI,CAACD,4BAA4B;QAC/B,OAAOC;IACT;IAEA,uEAAuE;IACvE,+CAA+C;IAC/C,IAAInN,2BAAa,CAACb,YAAY,EAAE;QAC9B,KAAK,MAAMiO,QAAQhN,OAAOC,IAAI,CAC5B6M,4BACiC;YACjC,MAAM1M,QAAQ0M,0BAA0B,CAACE,KAAK;YAE9C,IAAIA,SAAS,WAAW,CAAC9L,QAAQC,GAAG,CAAC8L,SAAS,EAAE;gBAE9C;YACF;YAEA,IACED,QAAQpN,2BAAa,CAACb,YAAY,IAClCqB,UAAUR,2BAAa,CAACb,YAAY,CAACiO,KAAK,EAC1C;gBACAD,+BAA+BhK,IAAI,CACjC,OAAO3C,UAAU,YACb;oBAAE4M;oBAAME,MAAM;oBAAW9M;gBAAM,IAC/B,OAAOA,UAAU,WACf;oBAAE4M;oBAAME,MAAM;oBAAU9M;gBAAM,IAC9B;oBAAE4M;oBAAME,MAAM;gBAAQ;YAEhC;QACF;IACF;IACA,OAAOH;AACT"}