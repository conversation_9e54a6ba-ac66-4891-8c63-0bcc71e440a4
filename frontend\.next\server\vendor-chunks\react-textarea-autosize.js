"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-textarea-autosize";
exports.ids = ["vendor-chunks/react-textarea-autosize"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js":
/*!**********************************************************************************************!*\
  !*** ./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js ***!
  \**********************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ index)\n/* harmony export */ });\n/* harmony import */ var _babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/esm/extends */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/extends.js\");\n/* harmony import */ var _babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @babel/runtime/helpers/esm/objectWithoutPropertiesLoose */ \"(ssr)/./node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var use_latest__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-latest */ \"(ssr)/./node_modules/use-latest/dist/use-latest.esm.js\");\n/* harmony import */ var use_composed_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! use-composed-ref */ \"(ssr)/./node_modules/use-composed-ref/dist/use-composed-ref.esm.js\");\n\n\n\n\n\n\nvar isBrowser = typeof document !== 'undefined';\n\nvar HIDDEN_TEXTAREA_STYLE = {\n  'min-height': '0',\n  'max-height': 'none',\n  height: '0',\n  visibility: 'hidden',\n  overflow: 'hidden',\n  position: 'absolute',\n  'z-index': '-1000',\n  top: '0',\n  right: '0',\n  display: 'block'\n};\nvar forceHiddenStyles = function forceHiddenStyles(node) {\n  Object.keys(HIDDEN_TEXTAREA_STYLE).forEach(function (key) {\n    node.style.setProperty(key, HIDDEN_TEXTAREA_STYLE[key], 'important');\n  });\n};\nvar forceHiddenStyles$1 = forceHiddenStyles;\n\nvar hiddenTextarea = null;\nvar getHeight = function getHeight(node, sizingData) {\n  var height = node.scrollHeight;\n  if (sizingData.sizingStyle.boxSizing === 'border-box') {\n    // border-box: add border, since height = content + padding + border\n    return height + sizingData.borderSize;\n  }\n\n  // remove padding, since height = content\n  return height - sizingData.paddingSize;\n};\nfunction calculateNodeHeight(sizingData, value, minRows, maxRows) {\n  if (minRows === void 0) {\n    minRows = 1;\n  }\n  if (maxRows === void 0) {\n    maxRows = Infinity;\n  }\n  if (!hiddenTextarea) {\n    hiddenTextarea = document.createElement('textarea');\n    hiddenTextarea.setAttribute('tabindex', '-1');\n    hiddenTextarea.setAttribute('aria-hidden', 'true');\n    forceHiddenStyles$1(hiddenTextarea);\n  }\n  if (hiddenTextarea.parentNode === null) {\n    document.body.appendChild(hiddenTextarea);\n  }\n  var paddingSize = sizingData.paddingSize,\n    borderSize = sizingData.borderSize,\n    sizingStyle = sizingData.sizingStyle;\n  var boxSizing = sizingStyle.boxSizing;\n  Object.keys(sizingStyle).forEach(function (_key) {\n    var key = _key;\n    hiddenTextarea.style[key] = sizingStyle[key];\n  });\n  forceHiddenStyles$1(hiddenTextarea);\n  hiddenTextarea.value = value;\n  var height = getHeight(hiddenTextarea, sizingData);\n  // Double set and calc due to Firefox bug: https://bugzilla.mozilla.org/show_bug.cgi?id=1795904\n  hiddenTextarea.value = value;\n  height = getHeight(hiddenTextarea, sizingData);\n\n  // measure height of a textarea with a single row\n  hiddenTextarea.value = 'x';\n  var rowHeight = hiddenTextarea.scrollHeight - paddingSize;\n  var minHeight = rowHeight * minRows;\n  if (boxSizing === 'border-box') {\n    minHeight = minHeight + paddingSize + borderSize;\n  }\n  height = Math.max(minHeight, height);\n  var maxHeight = rowHeight * maxRows;\n  if (boxSizing === 'border-box') {\n    maxHeight = maxHeight + paddingSize + borderSize;\n  }\n  height = Math.min(maxHeight, height);\n  return [height, rowHeight];\n}\n\nvar noop = function noop() {};\nvar pick = function pick(props, obj) {\n  return props.reduce(function (acc, prop) {\n    acc[prop] = obj[prop];\n    return acc;\n  }, {});\n};\n\nvar SIZING_STYLE = ['borderBottomWidth', 'borderLeftWidth', 'borderRightWidth', 'borderTopWidth', 'boxSizing', 'fontFamily', 'fontSize', 'fontStyle', 'fontWeight', 'letterSpacing', 'lineHeight', 'paddingBottom', 'paddingLeft', 'paddingRight', 'paddingTop',\n// non-standard\n'tabSize', 'textIndent',\n// non-standard\n'textRendering', 'textTransform', 'width', 'wordBreak', 'wordSpacing', 'scrollbarGutter'];\nvar isIE = isBrowser ? !!document.documentElement.currentStyle : false;\nvar getSizingData = function getSizingData(node) {\n  var style = window.getComputedStyle(node);\n  if (style === null) {\n    return null;\n  }\n  var sizingStyle = pick(SIZING_STYLE, style);\n  var boxSizing = sizingStyle.boxSizing;\n\n  // probably node is detached from DOM, can't read computed dimensions\n  if (boxSizing === '') {\n    return null;\n  }\n\n  // IE (Edge has already correct behaviour) returns content width as computed width\n  // so we need to add manually padding and border widths\n  if (isIE && boxSizing === 'border-box') {\n    sizingStyle.width = parseFloat(sizingStyle.width) + parseFloat(sizingStyle.borderRightWidth) + parseFloat(sizingStyle.borderLeftWidth) + parseFloat(sizingStyle.paddingRight) + parseFloat(sizingStyle.paddingLeft) + 'px';\n  }\n  var paddingSize = parseFloat(sizingStyle.paddingBottom) + parseFloat(sizingStyle.paddingTop);\n  var borderSize = parseFloat(sizingStyle.borderBottomWidth) + parseFloat(sizingStyle.borderTopWidth);\n  return {\n    sizingStyle: sizingStyle,\n    paddingSize: paddingSize,\n    borderSize: borderSize\n  };\n};\nvar getSizingData$1 = getSizingData;\n\nfunction useListener(target, type, listener) {\n  var latestListener = (0,use_latest__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(listener);\n  react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(function () {\n    var handler = function handler(ev) {\n      return latestListener.current(ev);\n    };\n    // might happen if document.fonts is not defined, for instance\n    if (!target) {\n      return;\n    }\n    target.addEventListener(type, handler);\n    return function () {\n      return target.removeEventListener(type, handler);\n    };\n  }, []);\n}\nvar useFormResetListener = function useFormResetListener(libRef, listener) {\n  useListener(document.body, 'reset', function (ev) {\n    if (libRef.current.form === ev.target) {\n      listener(ev);\n    }\n  });\n};\nvar useWindowResizeListener = function useWindowResizeListener(listener) {\n  useListener(window, 'resize', listener);\n};\nvar useFontsLoadedListener = function useFontsLoadedListener(listener) {\n  useListener(document.fonts, 'loadingdone', listener);\n};\n\nvar _excluded = [\"cacheMeasurements\", \"maxRows\", \"minRows\", \"onChange\", \"onHeightChange\"];\nvar TextareaAutosize = function TextareaAutosize(_ref, userRef) {\n  var cacheMeasurements = _ref.cacheMeasurements,\n    maxRows = _ref.maxRows,\n    minRows = _ref.minRows,\n    _ref$onChange = _ref.onChange,\n    onChange = _ref$onChange === void 0 ? noop : _ref$onChange,\n    _ref$onHeightChange = _ref.onHeightChange,\n    onHeightChange = _ref$onHeightChange === void 0 ? noop : _ref$onHeightChange,\n    props = (0,_babel_runtime_helpers_esm_objectWithoutPropertiesLoose__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_ref, _excluded);\n  if (props.style) {\n    if ('maxHeight' in props.style) {\n      throw new Error('Using `style.maxHeight` for <TextareaAutosize/> is not supported. Please use `maxRows`.');\n    }\n    if ('minHeight' in props.style) {\n      throw new Error('Using `style.minHeight` for <TextareaAutosize/> is not supported. Please use `minRows`.');\n    }\n  }\n  var isControlled = props.value !== undefined;\n  var libRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(null);\n  var ref = (0,use_composed_ref__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(libRef, userRef);\n  var heightRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef(0);\n  var measurementsCacheRef = react__WEBPACK_IMPORTED_MODULE_2__.useRef();\n  var resizeTextarea = function resizeTextarea() {\n    var node = libRef.current;\n    var nodeSizingData = cacheMeasurements && measurementsCacheRef.current ? measurementsCacheRef.current : getSizingData$1(node);\n    if (!nodeSizingData) {\n      return;\n    }\n    measurementsCacheRef.current = nodeSizingData;\n    var _calculateNodeHeight = calculateNodeHeight(nodeSizingData, node.value || node.placeholder || 'x', minRows, maxRows),\n      height = _calculateNodeHeight[0],\n      rowHeight = _calculateNodeHeight[1];\n    if (heightRef.current !== height) {\n      heightRef.current = height;\n      node.style.setProperty('height', height + \"px\", 'important');\n      onHeightChange(height, {\n        rowHeight: rowHeight\n      });\n    }\n  };\n  var handleChange = function handleChange(event) {\n    if (!isControlled) {\n      resizeTextarea();\n    }\n    onChange(event);\n  };\n  if (isBrowser) {\n    react__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect(resizeTextarea);\n    useFormResetListener(libRef, function () {\n      if (!isControlled) {\n        var currentValue = libRef.current.value;\n        requestAnimationFrame(function () {\n          var node = libRef.current;\n          if (node && currentValue !== node.value) {\n            resizeTextarea();\n          }\n        });\n      }\n    });\n    useWindowResizeListener(resizeTextarea);\n    useFontsLoadedListener(resizeTextarea);\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n      onChange: handleChange,\n      ref: ref\n    }));\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_2__.createElement(\"textarea\", (0,_babel_runtime_helpers_esm_extends__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({}, props, {\n    onChange: onChange,\n    ref: ref\n  }));\n};\nvar index = /* #__PURE__ */react__WEBPACK_IMPORTED_MODULE_2__.forwardRef(TextareaAutosize);\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-textarea-autosize/dist/react-textarea-autosize.development.esm.js\n");

/***/ })

};
;