{"kind": "FETCH", "data": {"headers": {"allow": "GET, HEAD, OPTIONS", "content-length": "328", "content-type": "application/json", "cross-origin-opener-policy": "same-origin", "referrer-policy": "same-origin", "server": "daphne", "vary": "Accept, origin", "x-content-type-options": "nosniff", "x-frame-options": "DENY"}, "body": "eyJjb3VudCI6MSwibmV4dCI6bnVsbCwicHJldmlvdXMiOm51bGwsInJlc3VsdHMiOlt7ImlkIjoyLCJlbWVyZ2VuY3lfdHlwZSI6Ik8iLCJjcmVhdGVkX2F0IjoiMjAyNS0wNS0yNVQxOTowNDoyNS4yMDY5NDlaIiwibG9jYXRpb24iOiLYp9mE2YLYr9izIiwiaW1hZ2UiOiIvbWVkaWEvZW1lcmdlbmN5L2ltYWdlcy9hLXBob3RvZ3JhcGgtZGVwaWN0aW5nLWEtY2hhb3RpYy1zY2VuZS13X2tJbWRNcE5fUzRxandzYVZmWm54MlFfaUI1WmhmX1FJN2MyZ2kuanBlZyIsInVzZXJfZmlyc3RfbmFtZSI6Ik1vbnRhc2VyQmFsbGgiLCJ1c2VyX2xhc3RfbmFtZSI6IkhhcmZvdXNoIn1dfQ==", "status": 200, "url": "http://localhost:8000/emergency/?emergency_type=O"}, "revalidate": 60, "tags": []}