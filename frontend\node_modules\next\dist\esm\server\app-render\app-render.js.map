{"version": 3, "sources": ["../../../src/server/app-render/app-render.tsx"], "sourcesContent": ["import type {\n  <PERSON>R<PERSON>ult,\n  DynamicParamTypesShort,\n  FlightRouterState,\n  RenderOpts,\n  Segment,\n  CacheNodeSeedData,\n  PreloadCallbacks,\n  RSCPayload,\n  FlightData,\n  InitialRSCPayload,\n  FlightDataPath,\n} from './types'\nimport {\n  workAsyncStorage,\n  type WorkStore,\n} from '../app-render/work-async-storage.external'\nimport type { RequestStore } from '../app-render/work-unit-async-storage.external'\nimport type { NextParsedUrlQuery } from '../request-meta'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport type { AppPageModule } from '../route-modules/app-page/module'\nimport type {\n  ClientReferenceManifest,\n  ManifestNode,\n} from '../../build/webpack/plugins/flight-manifest-plugin'\nimport type { DeepReadonly } from '../../shared/lib/deep-readonly'\nimport type { BaseNextRequest, BaseNextResponse } from '../base-http'\nimport type { IncomingHttpHeaders } from 'http'\n\nimport React, { type ErrorInfo, type JSX } from 'react'\n\nimport RenderResult, {\n  type AppPageRenderResultMetadata,\n  type RenderResultOptions,\n} from '../render-result'\nimport {\n  chainStreams,\n  renderToInitialFizzStream,\n  createDocumentClosingStream,\n  continueFizzStream,\n  continueDynamicPrerender,\n  continueStaticPrerender,\n  continueDynamicHTMLResume,\n  streamToBuffer,\n  streamToString,\n} from '../stream-utils/node-web-streams-helper'\nimport { stripInternalQueries } from '../internal-utils'\nimport {\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_STALE_TIME_HEADER,\n  NEXT_URL,\n  RSC_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n} from '../../client/components/app-router-headers'\nimport {\n  createTrackedMetadataContext,\n  createMetadataContext,\n} from '../../lib/metadata/metadata-context'\nimport { createRequestStoreForRender } from '../async-storage/request-store'\nimport { createWorkStore } from '../async-storage/work-store'\nimport {\n  getAccessFallbackErrorTypeByStatus,\n  getAccessFallbackHTTPStatus,\n  isHTTPAccessFallbackError,\n} from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { getImplicitTags } from '../lib/implicit-tags'\nimport { AppRenderSpan, NextNodeServerSpan } from '../lib/trace/constants'\nimport { getTracer } from '../lib/trace/tracer'\nimport { FlightRenderResult } from './flight-render-result'\nimport {\n  createFlightReactServerErrorHandler,\n  createHTMLReactServerErrorHandler,\n  createHTMLErrorHandler,\n  type DigestedError,\n  isUserLandError,\n  getDigestForWellKnownError,\n} from './create-error-handler'\nimport {\n  getShortDynamicParamType,\n  dynamicParamTypes,\n} from './get-short-dynamic-param-type'\nimport { getSegmentParam } from './get-segment-param'\nimport { getScriptNonceFromHeader } from './get-script-nonce-from-header'\nimport { parseAndValidateFlightRouterState } from './parse-and-validate-flight-router-state'\nimport { createFlightRouterStateFromLoaderTree } from './create-flight-router-state-from-loader-tree'\nimport { handleAction } from './action-handler'\nimport { isBailoutToCSRError } from '../../shared/lib/lazy-dynamic/bailout-to-csr'\nimport { warn, error } from '../../build/output/log'\nimport { appendMutableCookies } from '../web/spec-extension/adapters/request-cookies'\nimport { createServerInsertedHTML } from './server-inserted-html'\nimport { getRequiredScripts } from './required-scripts'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport { makeGetServerInsertedHTML } from './make-get-server-inserted-html'\nimport { walkTreeWithFlightRouterState } from './walk-tree-with-flight-router-state'\nimport { createComponentTree, getRootParams } from './create-component-tree'\nimport { getAssetQueryString } from './get-asset-query-string'\nimport { setReferenceManifestsSingleton } from './encryption-utils'\nimport {\n  DynamicState,\n  type PostponedState,\n  parsePostponedState,\n} from './postponed-state'\nimport {\n  getDynamicDataPostponedState,\n  getDynamicHTMLPostponedState,\n  getPostponedFromState,\n} from './postponed-state'\nimport { isDynamicServerError } from '../../client/components/hooks-server-context'\nimport {\n  useFlightStream,\n  createInlinedDataReadableStream,\n} from './use-flight-response'\nimport {\n  StaticGenBailoutError,\n  isStaticGenBailoutError,\n} from '../../client/components/static-generation-bailout'\nimport { getStackWithoutErrorMessage } from '../../lib/format-server-error'\nimport {\n  accessedDynamicData,\n  createPostponedAbortSignal,\n  formatDynamicAPIAccesses,\n  isPrerenderInterruptedError,\n  createDynamicTrackingState,\n  createDynamicValidationState,\n  getFirstDynamicReason,\n  trackAllowedDynamicAccess,\n  throwIfDisallowedDynamic,\n  consumeDynamicAccess,\n  type DynamicAccess,\n} from './dynamic-rendering'\nimport {\n  getClientComponentLoaderMetrics,\n  wrapClientComponentLoader,\n} from '../client-component-renderer-logger'\nimport { createServerModuleMap } from './action-utils'\nimport { isNodeNextRequest } from '../base-http/helpers'\nimport { parseParameter } from '../../shared/lib/router/utils/route-regex'\nimport { parseRelativeUrl } from '../../shared/lib/router/utils/parse-relative-url'\nimport AppRouter from '../../client/components/app-router'\nimport type { ServerComponentsHmrCache } from '../response-cache'\nimport type { RequestErrorContext } from '../instrumentation/types'\nimport { getServerActionRequestMetadata } from '../lib/server-action-request-meta'\nimport { createInitialRouterState } from '../../client/components/router-reducer/create-initial-router-state'\nimport { createMutableActionQueue } from '../../shared/lib/router/action-queue'\nimport { getRevalidateReason } from '../instrumentation/utils'\nimport { PAGE_SEGMENT_KEY } from '../../shared/lib/segment'\nimport type { FallbackRouteParams } from '../request/fallback-params'\nimport { DynamicServerError } from '../../client/components/hooks-server-context'\nimport { ServerPrerenderStreamResult } from './app-render-prerender-utils'\nimport {\n  type ReactServerPrerenderResult,\n  ReactServerResult,\n  createReactServerPrerenderResult,\n  createReactServerPrerenderResultFromRender,\n  prerenderAndAbortInSequentialTasks,\n  prerenderServerWithPhases,\n  prerenderClientWithPhases,\n} from './app-render-prerender-utils'\nimport { printDebugThrownValueForProspectiveRender } from './prospective-render-utils'\nimport { scheduleInSequentialTasks } from './app-render-render-utils'\nimport { waitAtLeastOneReactRenderTask } from '../../lib/scheduler'\nimport {\n  workUnitAsyncStorage,\n  type PrerenderStore,\n} from './work-unit-async-storage.external'\nimport { CacheSignal } from './cache-signal'\nimport { getTracedMetadata } from '../lib/trace/utils'\nimport { InvariantError } from '../../shared/lib/invariant-error'\n\nimport './clean-async-snapshot.external'\nimport { INFINITE_CACHE } from '../../lib/constants'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport {\n  createPrerenderResumeDataCache,\n  createRenderResumeDataCache,\n} from '../resume-data-cache/resume-data-cache'\nimport type { MetadataErrorType } from '../../lib/metadata/resolve-metadata'\nimport isError from '../../lib/is-error'\nimport { isUseCacheTimeoutError } from '../use-cache/use-cache-errors'\nimport { createServerInsertedMetadata } from './metadata-insertion/create-server-inserted-metadata'\n\nexport type GetDynamicParamFromSegment = (\n  // [slug] / [[slug]] / [...slug]\n  segment: string\n) => {\n  param: string\n  value: string | string[] | null\n  treeSegment: Segment\n  type: DynamicParamTypesShort\n} | null\n\nexport type GenerateFlight = typeof generateDynamicFlightRenderResult\n\nexport type AppSharedContext = {\n  buildId: string\n}\n\nexport type AppRenderContext = {\n  sharedContext: AppSharedContext\n  workStore: WorkStore\n  url: ReturnType<typeof parseRelativeUrl>\n  componentMod: AppPageModule\n  renderOpts: RenderOpts\n  parsedRequestHeaders: ParsedRequestHeaders\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n  query: NextParsedUrlQuery\n  isPrefetch: boolean\n  isAction: boolean\n  requestTimestamp: number\n  appUsingSizeAdjustment: boolean\n  flightRouterState?: FlightRouterState\n  requestId: string\n  pagePath: string\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n  assetPrefix: string\n  isNotFoundPath: boolean\n  nonce: string | undefined\n  res: BaseNextResponse\n}\n\ninterface ParseRequestHeadersOptions {\n  readonly isDevWarmup: undefined | boolean\n  readonly isRoutePPREnabled: boolean\n}\n\nconst flightDataPathHeadKey = 'h'\n\ninterface ParsedRequestHeaders {\n  /**\n   * Router state provided from the client-side router. Used to handle rendering\n   * from the common layout down. This value will be undefined if the request is\n   * not a client-side navigation request, or if the request is a prefetch\n   * request.\n   */\n  readonly flightRouterState: FlightRouterState | undefined\n  readonly isPrefetchRequest: boolean\n  readonly isRouteTreePrefetchRequest: boolean\n  readonly isDevWarmupRequest: boolean\n  readonly isHmrRefresh: boolean\n  readonly isRSCRequest: boolean\n  readonly nonce: string | undefined\n}\n\nfunction parseRequestHeaders(\n  headers: IncomingHttpHeaders,\n  options: ParseRequestHeadersOptions\n): ParsedRequestHeaders {\n  const isDevWarmupRequest = options.isDevWarmup === true\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isPrefetchRequest =\n    isDevWarmupRequest ||\n    headers[NEXT_ROUTER_PREFETCH_HEADER.toLowerCase()] !== undefined\n\n  const isHmrRefresh =\n    headers[NEXT_HMR_REFRESH_HEADER.toLowerCase()] !== undefined\n\n  // dev warmup requests are treated as prefetch RSC requests\n  const isRSCRequest =\n    isDevWarmupRequest || headers[RSC_HEADER.toLowerCase()] !== undefined\n\n  const shouldProvideFlightRouterState =\n    isRSCRequest && (!isPrefetchRequest || !options.isRoutePPREnabled)\n\n  const flightRouterState = shouldProvideFlightRouterState\n    ? parseAndValidateFlightRouterState(\n        headers[NEXT_ROUTER_STATE_TREE_HEADER.toLowerCase()]\n      )\n    : undefined\n\n  // Checks if this is a prefetch of the Route Tree by the Segment Cache\n  const isRouteTreePrefetchRequest =\n    headers[NEXT_ROUTER_SEGMENT_PREFETCH_HEADER.toLowerCase()] === '/_tree'\n\n  const csp =\n    headers['content-security-policy'] ||\n    headers['content-security-policy-report-only']\n\n  const nonce =\n    typeof csp === 'string' ? getScriptNonceFromHeader(csp) : undefined\n\n  return {\n    flightRouterState,\n    isPrefetchRequest,\n    isRouteTreePrefetchRequest,\n    isHmrRefresh,\n    isRSCRequest,\n    isDevWarmupRequest,\n    nonce,\n  }\n}\n\nfunction createNotFoundLoaderTree(loaderTree: LoaderTree): LoaderTree {\n  // Align the segment with parallel-route-default in next-app-loader\n  const components = loaderTree[2]\n  return [\n    '',\n    {\n      children: [\n        PAGE_SEGMENT_KEY,\n        {},\n        {\n          page: components['not-found'],\n        },\n      ],\n    },\n    components,\n  ]\n}\n\nfunction createDivergedMetadataComponents(\n  Metadata: React.ComponentType,\n  serveStreamingMetadata: boolean\n): {\n  StaticMetadata: React.ComponentType<{}>\n  StreamingMetadata: React.ComponentType<{}> | null\n} {\n  function EmptyMetadata() {\n    return null\n  }\n  const StreamingMetadata: React.ComponentType | null = serveStreamingMetadata\n    ? Metadata\n    : null\n\n  const StaticMetadata: React.ComponentType<{}> = serveStreamingMetadata\n    ? EmptyMetadata\n    : Metadata\n\n  return {\n    StaticMetadata,\n    StreamingMetadata,\n  }\n}\n\n/**\n * Returns a function that parses the dynamic segment and return the associated value.\n */\nfunction makeGetDynamicParamFromSegment(\n  params: { [key: string]: any },\n  pagePath: string,\n  fallbackRouteParams: FallbackRouteParams | null\n): GetDynamicParamFromSegment {\n  return function getDynamicParamFromSegment(\n    // [slug] / [[slug]] / [...slug]\n    segment: string\n  ) {\n    const segmentParam = getSegmentParam(segment)\n    if (!segmentParam) {\n      return null\n    }\n\n    const key = segmentParam.param\n\n    let value = params[key]\n\n    if (fallbackRouteParams && fallbackRouteParams.has(segmentParam.param)) {\n      value = fallbackRouteParams.get(segmentParam.param)\n    } else if (Array.isArray(value)) {\n      value = value.map((i) => encodeURIComponent(i))\n    } else if (typeof value === 'string') {\n      value = encodeURIComponent(value)\n    }\n\n    if (!value) {\n      const isCatchall = segmentParam.type === 'catchall'\n      const isOptionalCatchall = segmentParam.type === 'optional-catchall'\n\n      if (isCatchall || isOptionalCatchall) {\n        const dynamicParamType = dynamicParamTypes[segmentParam.type]\n        // handle the case where an optional catchall does not have a value,\n        // e.g. `/dashboard/[[...slug]]` when requesting `/dashboard`\n        if (isOptionalCatchall) {\n          return {\n            param: key,\n            value: null,\n            type: dynamicParamType,\n            treeSegment: [key, '', dynamicParamType],\n          }\n        }\n\n        // handle the case where a catchall or optional catchall does not have a value,\n        // e.g. `/foo/bar/hello` and `@slot/[...catchall]` or `@slot/[[...catchall]]` is matched\n        value = pagePath\n          .split('/')\n          // remove the first empty string\n          .slice(1)\n          // replace any dynamic params with the actual values\n          .flatMap((pathSegment) => {\n            const param = parseParameter(pathSegment)\n            // if the segment matches a param, return the param value\n            // otherwise, it's a static segment, so just return that\n            return params[param.key] ?? param.key\n          })\n\n        return {\n          param: key,\n          value,\n          type: dynamicParamType,\n          // This value always has to be a string.\n          treeSegment: [key, value.join('/'), dynamicParamType],\n        }\n      }\n    }\n\n    const type = getShortDynamicParamType(segmentParam.type)\n\n    return {\n      param: key,\n      // The value that is passed to user code.\n      value: value,\n      // The value that is rendered in the router tree.\n      treeSegment: [key, Array.isArray(value) ? value.join('/') : value, type],\n      type: type,\n    }\n  }\n}\n\nfunction NonIndex({ ctx }: { ctx: AppRenderContext }) {\n  const is404Page = ctx.pagePath === '/404'\n  const isInvalidStatusCode =\n    typeof ctx.res.statusCode === 'number' && ctx.res.statusCode > 400\n\n  // Only render noindex for page request, skip for server actions\n  if (!ctx.isAction && (is404Page || isInvalidStatusCode)) {\n    return <meta name=\"robots\" content=\"noindex\" />\n  }\n  return null\n}\n\n/**\n * This is used by server actions & client-side navigations to generate RSC data from a client-side request.\n * This function is only called on \"dynamic\" requests (ie, there wasn't already a static response).\n * It uses request headers (namely `Next-Router-State-Tree`) to determine where to start rendering.\n */\nasync function generateDynamicRSCPayload(\n  ctx: AppRenderContext,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n  }\n): Promise<RSCPayload> {\n  // Flight data that is going to be passed to the browser.\n  // Currently a single item array but in the future multiple patches might be combined in a single request.\n\n  // We initialize `flightData` to an empty string because the client router knows how to tolerate\n  // it (treating it as an MPA navigation). The only time this function wouldn't generate flight data\n  // is for server actions, if the server action handler instructs this function to skip it. When the server\n  // action reducer sees a falsy value, it'll simply resolve the action with no data.\n  let flightData: FlightData = ''\n\n  const {\n    componentMod: {\n      tree: loaderTree,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    query,\n    requestId,\n    flightRouterState,\n    workStore,\n    url,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  if (!options?.skipFlight) {\n    const preloadCallbacks: PreloadCallbacks = []\n\n    const searchParams = createServerSearchParamsForMetadata(query, workStore)\n    const {\n      ViewportTree,\n      MetadataTree,\n      getViewportReady,\n      getMetadataReady,\n      StreamingMetadataOutlet,\n    } = createMetadataComponents({\n      tree: loaderTree,\n      searchParams,\n      metadataContext: createTrackedMetadataContext(\n        url.pathname,\n        ctx.renderOpts,\n        workStore\n      ),\n      getDynamicParamFromSegment,\n      appUsingSizeAdjustment,\n      createServerParamsForMetadata,\n      workStore,\n      MetadataBoundary,\n      ViewportBoundary,\n      serveStreamingMetadata,\n    })\n\n    const { StreamingMetadata, StaticMetadata } =\n      createDivergedMetadataComponents(() => {\n        return (\n          // Adding requestId as react key to make metadata remount for each render\n          <MetadataTree key={requestId} />\n        )\n      }, serveStreamingMetadata)\n\n    flightData = (\n      await walkTreeWithFlightRouterState({\n        ctx,\n        loaderTreeToFilter: loaderTree,\n        parentParams: {},\n        flightRouterState,\n        // For flight, render metadata inside leaf page\n        rscHead: (\n          <React.Fragment key={flightDataPathHeadKey}>\n            {/* noindex needs to be blocking */}\n            <NonIndex ctx={ctx} />\n            {/* Adding requestId as react key to make metadata remount for each render */}\n            <ViewportTree key={requestId} />\n            {StreamingMetadata ? <StreamingMetadata /> : null}\n            <StaticMetadata />\n          </React.Fragment>\n        ),\n        injectedCSS: new Set(),\n        injectedJS: new Set(),\n        injectedFontPreloadTags: new Set(),\n        rootLayoutIncluded: false,\n        getViewportReady,\n        getMetadataReady,\n        preloadCallbacks,\n        StreamingMetadataOutlet,\n      })\n    ).map((path) => path.slice(1)) // remove the '' (root) segment\n  }\n\n  // If we have an action result, then this is a server action response.\n  // We can rely on this because `ActionResult` will always be a promise, even if\n  // the result is falsey.\n  if (options?.actionResult) {\n    return {\n      a: options.actionResult,\n      f: flightData,\n      b: ctx.sharedContext.buildId,\n    }\n  }\n\n  // Otherwise, it's a regular RSC response.\n  return {\n    b: ctx.sharedContext.buildId,\n    f: flightData,\n    S: workStore.isStaticGeneration,\n  }\n}\n\nfunction createErrorContext(\n  ctx: AppRenderContext,\n  renderSource: RequestErrorContext['renderSource']\n): RequestErrorContext {\n  return {\n    routerKind: 'App Router',\n    routePath: ctx.pagePath,\n    routeType: ctx.isAction ? 'action' : 'render',\n    renderSource,\n    revalidateReason: getRevalidateReason(ctx.workStore),\n  }\n}\n/**\n * Produces a RenderResult containing the Flight data for the given request. See\n * `generateDynamicRSCPayload` for information on the contents of the render result.\n */\nasync function generateDynamicFlightRenderResult(\n  req: BaseNextRequest,\n  ctx: AppRenderContext,\n  requestStore: RequestStore,\n  options?: {\n    actionResult: ActionResult\n    skipFlight: boolean\n    componentTree?: CacheNodeSeedData\n    preloadCallbacks?: PreloadCallbacks\n    temporaryReferences?: WeakMap<any, string>\n  }\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    !!renderOpts.dev,\n    onFlightDataRenderError\n  )\n\n  const RSCPayload: RSCPayload & {\n    /** Only available during dynamicIO development builds. Used for logging errors. */\n    _validation?: Promise<React.ReactNode>\n  } = await workUnitAsyncStorage.run(\n    requestStore,\n    generateDynamicRSCPayload,\n    ctx,\n    options\n  )\n\n  if (\n    // We only want this behavior when running `next dev`\n    renderOpts.dev &&\n    // We only want this behavior when we have React's dev builds available\n    process.env.NODE_ENV === 'development' &&\n    // We only have a Prerender environment for projects opted into dynamicIO\n    renderOpts.experimental.dynamicIO\n  ) {\n    const [resolveValidation, validationOutlet] = createValidationOutlet()\n    RSCPayload._validation = validationOutlet\n\n    spawnDynamicValidationInDev(\n      resolveValidation,\n      ctx.componentMod.tree,\n      ctx,\n      false,\n      ctx.clientReferenceManifest,\n      ctx.workStore.route,\n      requestStore\n    )\n  }\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  const flightReadableStream = workUnitAsyncStorage.run(\n    requestStore,\n    ctx.componentMod.renderToReadableStream,\n    RSCPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      temporaryReferences: options?.temporaryReferences,\n    }\n  )\n\n  return new FlightRenderResult(flightReadableStream, {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n  })\n}\n\n/**\n * Performs a \"warmup\" render of the RSC payload for a given route. This function is called by the server\n * prior to an actual render request in Dev mode only. It's purpose is to fill caches so the actual render\n * can accurately log activity in the right render context (Prerender vs Render).\n *\n * At the moment this implementation is mostly a fork of generateDynamicFlightRenderResult\n */\nasync function warmupDevRender(\n  req: BaseNextRequest,\n  ctx: AppRenderContext\n): Promise<RenderResult> {\n  const renderOpts = ctx.renderOpts\n  if (!renderOpts.dev) {\n    throw new InvariantError(\n      'generateDynamicFlightRenderResult should never be called in `next start` mode.'\n    )\n  }\n\n  const rootParams = getRootParams(\n    ctx.componentMod.tree,\n    ctx.getDynamicParamFromSegment\n  )\n\n  function onFlightDataRenderError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components-payload')\n    )\n  }\n  const onError = createFlightReactServerErrorHandler(\n    true,\n    onFlightDataRenderError\n  )\n\n  // We're doing a dev warmup, so we should create a new resume data cache so\n  // we can fill it.\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n  const renderController = new AbortController()\n  const prerenderController = new AbortController()\n  const cacheSignal = new CacheSignal()\n\n  const prerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: renderController.signal,\n    controller: prerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const rscPayload = await workUnitAsyncStorage.run(\n    prerenderStore,\n    generateDynamicRSCPayload,\n    ctx\n  )\n\n  // For app dir, use the bundled version of Flight server renderer (renderToReadableStream)\n  // which contains the subset React.\n  workUnitAsyncStorage.run(\n    prerenderStore,\n    ctx.componentMod.renderToReadableStream,\n    rscPayload,\n    ctx.clientReferenceManifest.clientModules,\n    {\n      onError,\n      signal: renderController.signal,\n    }\n  )\n\n  // Wait for all caches to be finished filling\n  await cacheSignal.cacheReady()\n  // We unset the cache so any late over-run renders aren't able to write into this cache\n  prerenderStore.prerenderResumeDataCache = null\n  // Abort the render\n  renderController.abort()\n\n  // We don't really want to return a result here but the stack of functions\n  // that calls into renderToHTML... expects a result. We should refactor this to\n  // lift the warmup pathway outside of renderToHTML... but for now this suffices\n  return new FlightRenderResult('', {\n    fetchMetrics: ctx.workStore.fetchMetrics,\n    devRenderResumeDataCache: createRenderResumeDataCache(\n      prerenderResumeDataCache\n    ),\n  })\n}\n\n/**\n * Crawlers will inadvertently think the canonicalUrl in the RSC payload should be crawled\n * when our intention is to just seed the router state with the current URL.\n * This function splits up the pathname so that we can later join it on\n * when we're ready to consume the path.\n */\nfunction prepareInitialCanonicalUrl(url: RequestStore['url']) {\n  return (url.pathname + url.search).split('/')\n}\n\n// This is the data necessary to render <AppRouter /> when no SSR errors are encountered\nasync function getRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  is404: boolean\n): Promise<InitialRSCPayload & { P: React.ReactNode }> {\n  const injectedCSS = new Set<string>()\n  const injectedJS = new Set<string>()\n  const injectedFontPreloadTags = new Set<string>()\n  let missingSlots: Set<string> | undefined\n\n  // We only track missing parallel slots in development\n  if (process.env.NODE_ENV === 'development') {\n    missingSlots = new Set<string>()\n  }\n\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    workStore,\n  } = ctx\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n\n  const searchParams = createServerSearchParamsForMetadata(query, workStore)\n  const {\n    ViewportTree,\n    MetadataTree,\n    getViewportReady,\n    getMetadataReady,\n    StreamingMetadataOutlet,\n  } = createMetadataComponents({\n    tree,\n    errorType: is404 ? 'not-found' : undefined,\n    searchParams,\n    metadataContext: createTrackedMetadataContext(\n      url.pathname,\n      ctx.renderOpts,\n      workStore\n    ),\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    createServerParamsForMetadata,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata,\n  })\n\n  const preloadCallbacks: PreloadCallbacks = []\n\n  const { StreamingMetadata, StaticMetadata } =\n    createDivergedMetadataComponents(() => {\n      return (\n        // Not add requestId as react key to ensure segment prefetch could result consistently if nothing changed\n        <MetadataTree />\n      )\n    }, serveStreamingMetadata)\n\n  const seedData = await createComponentTree({\n    ctx,\n    loaderTree: tree,\n    parentParams: {},\n    injectedCSS,\n    injectedJS,\n    injectedFontPreloadTags,\n    rootLayoutIncluded: false,\n    getViewportReady,\n    getMetadataReady,\n    missingSlots,\n    preloadCallbacks,\n    authInterrupts: ctx.renderOpts.experimental.authInterrupts,\n    StreamingMetadata,\n    StreamingMetadataOutlet,\n  })\n\n  // When the `vary` response header is present with `Next-URL`, that means there's a chance\n  // it could respond differently if there's an interception route. We provide this information\n  // to `AppRouter` so that it can properly seed the prefetch cache with a prefix, if needed.\n  const varyHeader = ctx.res.getHeader('vary')\n  const couldBeIntercepted =\n    typeof varyHeader === 'string' && varyHeader.includes(NEXT_URL)\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex ctx={ctx} />\n      <ViewportTree key={ctx.requestId} />\n      <StaticMetadata />\n    </React.Fragment>\n  )\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  // Assume the head we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // See similar comment in create-component-tree.tsx for more context.\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    // See the comment above the `Preloads` component (below) for why this is part of the payload\n    P: <Preloads preloadCallbacks={preloadCallbacks} />,\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    i: !!couldBeIntercepted,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    m: missingSlots,\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  }\n}\n\n/**\n * Preload calls (such as `ReactDOM.preloadStyle` and `ReactDOM.preloadFont`) need to be called during rendering\n * in order to create the appropriate preload tags in the DOM, otherwise they're a no-op. Since we invoke\n * renderToReadableStream with a function that returns component props rather than a component itself, we use\n * this component to \"render  \" the preload calls.\n */\nfunction Preloads({ preloadCallbacks }: { preloadCallbacks: Function[] }) {\n  preloadCallbacks.forEach((preloadFn) => preloadFn())\n  return null\n}\n\n// This is the data necessary to render <AppRouter /> when an error state is triggered\nasync function getErrorRSCPayload(\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  ssrError: unknown,\n  errorType: MetadataErrorType | 'redirect' | undefined\n) {\n  const {\n    getDynamicParamFromSegment,\n    query,\n    appUsingSizeAdjustment,\n    componentMod: {\n      GlobalError,\n      createServerSearchParamsForMetadata,\n      createServerParamsForMetadata,\n      createMetadataComponents,\n      MetadataBoundary,\n      ViewportBoundary,\n    },\n    url,\n    requestId,\n    workStore,\n  } = ctx\n\n  const serveStreamingMetadata = !!ctx.renderOpts.serveStreamingMetadata\n  const searchParams = createServerSearchParamsForMetadata(query, workStore)\n  const { MetadataTree, ViewportTree } = createMetadataComponents({\n    tree,\n    searchParams,\n    // We create an untracked metadata context here because we can't postpone\n    // again during the error render.\n    metadataContext: createMetadataContext(url.pathname, ctx.renderOpts),\n    errorType,\n    getDynamicParamFromSegment,\n    appUsingSizeAdjustment,\n    createServerParamsForMetadata,\n    workStore,\n    MetadataBoundary,\n    ViewportBoundary,\n    serveStreamingMetadata: serveStreamingMetadata,\n  })\n\n  const { StreamingMetadata, StaticMetadata } =\n    createDivergedMetadataComponents(\n      () => (\n        <React.Fragment key={flightDataPathHeadKey}>\n          {/* Adding requestId as react key to make metadata remount for each render */}\n          <MetadataTree key={requestId} />\n        </React.Fragment>\n      ),\n      serveStreamingMetadata\n    )\n\n  const initialHead = (\n    <React.Fragment key={flightDataPathHeadKey}>\n      <NonIndex ctx={ctx} />\n      {/* Adding requestId as react key to make metadata remount for each render */}\n      <ViewportTree key={requestId} />\n      {process.env.NODE_ENV === 'development' && (\n        <meta name=\"next-error\" content=\"not-found\" />\n      )}\n      <StaticMetadata />\n    </React.Fragment>\n  )\n\n  const initialTree = createFlightRouterStateFromLoaderTree(\n    tree,\n    getDynamicParamFromSegment,\n    query\n  )\n\n  let err: Error | undefined = undefined\n  if (ssrError) {\n    err = isError(ssrError) ? ssrError : new Error(ssrError + '')\n  }\n\n  // For metadata notFound error there's no global not found boundary on top\n  // so we create a not found page with AppRouter\n  const seedData: CacheNodeSeedData = [\n    initialTree[0],\n    <html id=\"__next_error__\">\n      <head>{StreamingMetadata ? <StreamingMetadata /> : null}</head>\n      <body>\n        {process.env.NODE_ENV !== 'production' && err ? (\n          <template\n            data-next-error-message={err.message}\n            data-next-error-digest={'digest' in err ? err.digest : ''}\n            data-next-error-stack={err.stack}\n          />\n        ) : null}\n      </body>\n    </html>,\n    {},\n    null,\n    false,\n  ]\n\n  const globalErrorStyles = await getGlobalErrorStyles(tree, ctx)\n\n  const isPossiblyPartialHead =\n    workStore.isStaticGeneration &&\n    ctx.renderOpts.experimental.isRoutePPREnabled === true\n\n  return {\n    b: ctx.sharedContext.buildId,\n    p: ctx.assetPrefix,\n    c: prepareInitialCanonicalUrl(url),\n    m: undefined,\n    i: false,\n    f: [\n      [\n        initialTree,\n        seedData,\n        initialHead,\n        isPossiblyPartialHead,\n      ] as FlightDataPath,\n    ],\n    G: [GlobalError, globalErrorStyles],\n    s: typeof ctx.renderOpts.postponed === 'string',\n    S: workStore.isStaticGeneration,\n  } satisfies InitialRSCPayload\n}\n\n// This component must run in an SSR context. It will render the RSC root component\nfunction App<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n  ServerInsertedHTMLProvider,\n  ServerInsertedMetadataProvider,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  ServerInsertedHTMLProvider: React.ComponentType<{ children: JSX.Element }>\n  ServerInsertedMetadataProvider: React.ComponentType<{ children: JSX.Element }>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState)\n\n  const { HeadManagerContext } =\n    require('../../shared/lib/head-manager-context.shared-runtime') as typeof import('../../shared/lib/head-manager-context.shared-runtime')\n\n  return (\n    <HeadManagerContext.Provider\n      value={{\n        appDir: true,\n        nonce,\n      }}\n    >\n      <ServerInsertedMetadataProvider>\n        <ServerInsertedHTMLProvider>\n          <AppRouter\n            actionQueue={actionQueue}\n            globalErrorComponentAndStyles={response.G}\n            assetPrefix={response.p}\n          />\n        </ServerInsertedHTMLProvider>\n      </ServerInsertedMetadataProvider>\n    </HeadManagerContext.Provider>\n  )\n}\n\n// @TODO our error stream should be probably just use the same root component. But it was previously\n// different I don't want to figure out if that is meaningful at this time so just keeping the behavior\n// consistent for now.\nfunction AppWithoutContext<T>({\n  reactServerStream,\n  preinitScripts,\n  clientReferenceManifest,\n  nonce,\n}: {\n  reactServerStream: BinaryStreamOf<T>\n  preinitScripts: () => void\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>\n  nonce?: string\n}): JSX.Element {\n  preinitScripts()\n  const response = React.use(\n    useFlightStream<InitialRSCPayload>(\n      reactServerStream,\n      clientReferenceManifest,\n      nonce\n    )\n  )\n\n  const initialState = createInitialRouterState({\n    initialFlightData: response.f,\n    initialCanonicalUrlParts: response.c,\n    initialParallelRoutes: new Map(),\n    // location is not initialized in the SSR render\n    // it's set to window.location during hydration\n    location: null,\n    couldBeIntercepted: response.i,\n    postponed: response.s,\n    prerendered: response.S,\n  })\n\n  const actionQueue = createMutableActionQueue(initialState)\n\n  return (\n    <AppRouter\n      actionQueue={actionQueue}\n      globalErrorComponentAndStyles={response.G}\n      assetPrefix={response.p}\n    />\n  )\n}\n\n// We use a trick with TS Generics to branch streams with a type so we can\n// consume the parsed value of a Readable Stream if it was constructed with a\n// certain object shape. The generic type is not used directly in the type so it\n// requires a disabling of the eslint rule disallowing unused vars\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type BinaryStreamOf<T> = ReadableStream<Uint8Array>\n\nasync function renderToHTMLOrFlightImpl(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  url: ReturnType<typeof parseRelativeUrl>,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  renderOpts: RenderOpts,\n  workStore: WorkStore,\n  parsedRequestHeaders: ParsedRequestHeaders,\n  requestEndedState: { ended?: boolean },\n  postponedState: PostponedState | null,\n  implicitTags: Array<string>,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  sharedContext: AppSharedContext\n) {\n  const isNotFoundPath = pagePath === '/404'\n  if (isNotFoundPath) {\n    res.statusCode = 404\n  }\n\n  // A unique request timestamp used by development to ensure that it's\n  // consistent and won't change during this request. This is important to\n  // avoid that resources can be deduped by React Float if the same resource is\n  // rendered or preloaded multiple times: `<link href=\"a.css?v={Date.now()}\"/>`.\n  const requestTimestamp = Date.now()\n\n  const {\n    serverActionsManifest,\n    ComponentMod,\n    nextFontManifest,\n    serverActions,\n    assetPrefix = '',\n    enableTainting,\n  } = renderOpts\n\n  // We need to expose the bundled `require` API globally for\n  // react-server-dom-webpack. This is a hack until we find a better way.\n  if (ComponentMod.__next_app__) {\n    const instrumented = wrapClientComponentLoader(ComponentMod)\n    // @ts-ignore\n    globalThis.__next_require__ = instrumented.require\n    // When we are prerendering if there is a cacheSignal for tracking\n    // cache reads we wrap the loadChunk in this tracking. This allows us\n    // to treat chunk loading with similar semantics as cache reads to avoid\n    // async loading chunks from causing a prerender to abort too early.\n    const __next_chunk_load__: typeof instrumented.loadChunk = (...args) => {\n      const loadingChunk = instrumented.loadChunk(...args)\n      trackChunkLoading(loadingChunk)\n      return loadingChunk\n    }\n    // @ts-expect-error\n    globalThis.__next_chunk_load__ = __next_chunk_load__\n  }\n\n  if (process.env.NODE_ENV === 'development') {\n    // reset isr status at start of request\n    const { pathname } = new URL(req.url || '/', 'http://n')\n    renderOpts.setIsrStatus?.(pathname, null)\n  }\n\n  if (\n    // The type check here ensures that `req` is correctly typed, and the\n    // environment variable check provides dead code elimination.\n    process.env.NEXT_RUNTIME !== 'edge' &&\n    isNodeNextRequest(req)\n  ) {\n    req.originalRequest.on('end', () => {\n      requestEndedState.ended = true\n\n      if ('performance' in globalThis) {\n        const metrics = getClientComponentLoaderMetrics({ reset: true })\n        if (metrics) {\n          getTracer()\n            .startSpan(NextNodeServerSpan.clientComponentLoading, {\n              startTime: metrics.clientComponentLoadStart,\n              attributes: {\n                'next.clientComponentLoadCount':\n                  metrics.clientComponentLoadCount,\n                'next.span_type': NextNodeServerSpan.clientComponentLoading,\n              },\n            })\n            .end(\n              metrics.clientComponentLoadStart +\n                metrics.clientComponentLoadTimes\n            )\n        }\n      }\n    })\n  }\n\n  const metadata: AppPageRenderResultMetadata = {}\n\n  const appUsingSizeAdjustment = !!nextFontManifest?.appUsingSizeAdjust\n\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const serverModuleMap = createServerModuleMap({ serverActionsManifest })\n\n  setReferenceManifestsSingleton({\n    page: workStore.page,\n    clientReferenceManifest,\n    serverActionsManifest,\n    serverModuleMap,\n  })\n\n  ComponentMod.patchFetch()\n\n  // Pull out the hooks/references from the component.\n  const { tree: loaderTree, taintObjectReference } = ComponentMod\n\n  if (enableTainting) {\n    taintObjectReference(\n      'Do not pass process.env to client components since it will leak sensitive data',\n      process.env\n    )\n  }\n\n  workStore.fetchMetrics = []\n  metadata.fetchMetrics = workStore.fetchMetrics\n\n  // don't modify original query object\n  query = { ...query }\n  stripInternalQueries(query)\n\n  const {\n    flightRouterState,\n    isPrefetchRequest,\n    isRSCRequest,\n    isDevWarmupRequest,\n    isHmrRefresh,\n    nonce,\n  } = parsedRequestHeaders\n\n  /**\n   * The metadata items array created in next-app-loader with all relevant information\n   * that we need to resolve the final metadata.\n   */\n  let requestId: string\n\n  if (process.env.NEXT_RUNTIME === 'edge') {\n    requestId = crypto.randomUUID()\n  } else {\n    requestId = require('next/dist/compiled/nanoid').nanoid()\n  }\n\n  /**\n   * Dynamic parameters. E.g. when you visit `/dashboard/vercel` which is rendered by `/dashboard/[slug]` the value will be {\"slug\": \"vercel\"}.\n   */\n  const params = renderOpts.params ?? {}\n\n  const { isStaticGeneration, fallbackRouteParams } = workStore\n\n  const getDynamicParamFromSegment = makeGetDynamicParamFromSegment(\n    params,\n    pagePath,\n    fallbackRouteParams\n  )\n\n  const isActionRequest = getServerActionRequestMetadata(req).isServerAction\n\n  const ctx: AppRenderContext = {\n    componentMod: ComponentMod,\n    url,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    getDynamicParamFromSegment,\n    query,\n    isPrefetch: isPrefetchRequest,\n    isAction: isActionRequest,\n    requestTimestamp,\n    appUsingSizeAdjustment,\n    flightRouterState,\n    requestId,\n    pagePath,\n    clientReferenceManifest,\n    assetPrefix,\n    isNotFoundPath,\n    nonce,\n    res,\n    sharedContext,\n  }\n\n  getTracer().setRootSpanAttribute('next.route', pagePath)\n\n  if (isStaticGeneration) {\n    // We're either building or revalidating. In either case we need to\n    // prerender our page rather than render it.\n    const prerenderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `prerender route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      prerenderToStream\n    )\n\n    const response = await prerenderToStreamWithTracing(\n      req,\n      res,\n      ctx,\n      metadata,\n      workStore,\n      loaderTree,\n      implicitTags\n    )\n\n    // If we're debugging partial prerendering, print all the dynamic API accesses\n    // that occurred during the render.\n    // @TODO move into renderToStream function\n    if (\n      response.dynamicAccess &&\n      accessedDynamicData(response.dynamicAccess) &&\n      renderOpts.isDebugDynamicAccesses\n    ) {\n      warn('The following dynamic usage was detected:')\n      for (const access of formatDynamicAPIAccesses(response.dynamicAccess)) {\n        warn(access)\n      }\n    }\n\n    // If we encountered any unexpected errors during build we fail the\n    // prerendering phase and the build.\n    if (response.digestErrorsMap.size) {\n      const buildFailingError = response.digestErrorsMap.values().next().value\n      if (buildFailingError) throw buildFailingError\n    }\n    // Pick first userland SSR error, which is also not a RSC error.\n    if (response.ssrErrors.length) {\n      const buildFailingError = response.ssrErrors.find((err) =>\n        isUserLandError(err)\n      )\n      if (buildFailingError) throw buildFailingError\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.revalidatedTags\n    ) {\n      const pendingPromise = Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ]).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    if (response.collectedTags) {\n      metadata.fetchTags = response.collectedTags.join(',')\n    }\n\n    // Let the client router know how long to keep the cached entry around.\n    const staleHeader = String(response.collectedStale)\n    res.setHeader(NEXT_ROUTER_STALE_TIME_HEADER, staleHeader)\n    metadata.headers ??= {}\n    metadata.headers[NEXT_ROUTER_STALE_TIME_HEADER] = staleHeader\n\n    // If force static is specifically set to false, we should not revalidate\n    // the page.\n    if (workStore.forceStatic === false || response.collectedRevalidate === 0) {\n      metadata.cacheControl = { revalidate: 0, expire: undefined }\n    } else {\n      // Copy the cache control value onto the render result metadata.\n      metadata.cacheControl = {\n        revalidate:\n          response.collectedRevalidate >= INFINITE_CACHE\n            ? false\n            : response.collectedRevalidate,\n        expire:\n          response.collectedExpire >= INFINITE_CACHE\n            ? undefined\n            : response.collectedExpire,\n      }\n    }\n\n    // provide bailout info for debugging\n    if (metadata.cacheControl?.revalidate === 0) {\n      metadata.staticBailoutInfo = {\n        description: workStore.dynamicUsageDescription,\n        stack: workStore.dynamicUsageStack,\n      }\n    }\n\n    return new RenderResult(await streamToString(response.stream), options)\n  } else {\n    // We're rendering dynamically\n    const renderResumeDataCache =\n      renderOpts.devRenderResumeDataCache ??\n      postponedState?.renderResumeDataCache\n\n    const rootParams = getRootParams(loaderTree, ctx.getDynamicParamFromSegment)\n    const requestStore = createRequestStoreForRender(\n      req,\n      res,\n      url,\n      rootParams,\n      implicitTags,\n      renderOpts.onUpdateCookies,\n      renderOpts.previewProps,\n      isHmrRefresh,\n      serverComponentsHmrCache,\n      renderResumeDataCache\n    )\n\n    if (\n      process.env.NODE_ENV === 'development' &&\n      renderOpts.setIsrStatus &&\n      // The type check here ensures that `req` is correctly typed, and the\n      // environment variable check provides dead code elimination.\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      isNodeNextRequest(req) &&\n      !isDevWarmupRequest\n    ) {\n      const setIsrStatus = renderOpts.setIsrStatus\n      req.originalRequest.on('end', () => {\n        if (!requestStore.usedDynamic && !workStore.forceDynamic) {\n          // only node can be ISR so we only need to update the status here\n          const { pathname } = new URL(req.url || '/', 'http://n')\n          setIsrStatus(pathname, true)\n        }\n      })\n    }\n\n    if (isDevWarmupRequest) {\n      return warmupDevRender(req, ctx)\n    } else if (isRSCRequest) {\n      return generateDynamicFlightRenderResult(req, ctx, requestStore)\n    }\n\n    const renderToStreamWithTracing = getTracer().wrap(\n      AppRenderSpan.getBodyResult,\n      {\n        spanName: `render route (app) ${pagePath}`,\n        attributes: {\n          'next.route': pagePath,\n        },\n      },\n      renderToStream\n    )\n\n    let formState: null | any = null\n    if (isActionRequest) {\n      // For action requests, we handle them differently with a special render result.\n      const actionRequestResult = await handleAction({\n        req,\n        res,\n        ComponentMod,\n        serverModuleMap,\n        generateFlight: generateDynamicFlightRenderResult,\n        workStore,\n        requestStore,\n        serverActions,\n        ctx,\n      })\n\n      if (actionRequestResult) {\n        if (actionRequestResult.type === 'not-found') {\n          const notFoundLoaderTree = createNotFoundLoaderTree(loaderTree)\n          res.statusCode = 404\n          const stream = await renderToStreamWithTracing(\n            requestStore,\n            req,\n            res,\n            ctx,\n            workStore,\n            notFoundLoaderTree,\n            formState,\n            postponedState\n          )\n\n          return new RenderResult(stream, { metadata })\n        } else if (actionRequestResult.type === 'done') {\n          if (actionRequestResult.result) {\n            actionRequestResult.result.assignMetadata(metadata)\n            return actionRequestResult.result\n          } else if (actionRequestResult.formState) {\n            formState = actionRequestResult.formState\n          }\n        }\n      }\n    }\n\n    const options: RenderResultOptions = {\n      metadata,\n    }\n\n    const stream = await renderToStreamWithTracing(\n      requestStore,\n      req,\n      res,\n      ctx,\n      workStore,\n      loaderTree,\n      formState,\n      postponedState\n    )\n\n    // If we have pending revalidates, wait until they are all resolved.\n    if (\n      workStore.pendingRevalidates ||\n      workStore.pendingRevalidateWrites ||\n      workStore.revalidatedTags\n    ) {\n      const pendingPromise = Promise.all([\n        workStore.incrementalCache?.revalidateTag(\n          workStore.revalidatedTags || []\n        ),\n        ...Object.values(workStore.pendingRevalidates || {}),\n        ...(workStore.pendingRevalidateWrites || []),\n      ]).finally(() => {\n        if (process.env.NEXT_PRIVATE_DEBUG_CACHE) {\n          console.log('pending revalidates promise finished for:', url)\n        }\n      })\n\n      if (renderOpts.waitUntil) {\n        renderOpts.waitUntil(pendingPromise)\n      } else {\n        options.waitUntil = pendingPromise\n      }\n    }\n\n    // Create the new render result for the response.\n    return new RenderResult(stream, options)\n  }\n}\n\nexport type AppPageRender = (\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  pagePath: string,\n  query: NextParsedUrlQuery,\n  fallbackRouteParams: FallbackRouteParams | null,\n  renderOpts: RenderOpts,\n  serverComponentsHmrCache: ServerComponentsHmrCache | undefined,\n  isDevWarmup: boolean,\n  sharedContext: AppSharedContext\n) => Promise<RenderResult<AppPageRenderResultMetadata>>\n\nexport const renderToHTMLOrFlight: AppPageRender = (\n  req,\n  res,\n  pagePath,\n  query,\n  fallbackRouteParams,\n  renderOpts,\n  serverComponentsHmrCache,\n  isDevWarmup,\n  sharedContext\n) => {\n  if (!req.url) {\n    throw new Error('Invalid URL')\n  }\n\n  const url = parseRelativeUrl(req.url, undefined, false)\n\n  // We read these values from the request object as, in certain cases,\n  // base-server will strip them to opt into different rendering behavior.\n  const parsedRequestHeaders = parseRequestHeaders(req.headers, {\n    isDevWarmup,\n    isRoutePPREnabled: renderOpts.experimental.isRoutePPREnabled === true,\n  })\n\n  const { isPrefetchRequest } = parsedRequestHeaders\n\n  const requestEndedState = { ended: false }\n  let postponedState: PostponedState | null = null\n\n  // If provided, the postpone state should be parsed so it can be provided to\n  // React.\n  if (typeof renderOpts.postponed === 'string') {\n    if (fallbackRouteParams) {\n      throw new InvariantError(\n        'postponed state should not be provided when fallback params are provided'\n      )\n    }\n\n    postponedState = parsePostponedState(\n      renderOpts.postponed,\n      renderOpts.params\n    )\n  }\n\n  if (\n    postponedState?.renderResumeDataCache &&\n    renderOpts.devRenderResumeDataCache\n  ) {\n    throw new InvariantError(\n      'postponed state and dev warmup immutable resume data cache should not be provided together'\n    )\n  }\n\n  const implicitTags = getImplicitTags(\n    renderOpts.routeModule.definition.page,\n    url,\n    fallbackRouteParams\n  )\n\n  const workStore = createWorkStore({\n    page: renderOpts.routeModule.definition.page,\n    fallbackRouteParams,\n    renderOpts,\n    requestEndedState,\n    // @TODO move to workUnitStore of type Request\n    isPrefetchRequest,\n    buildId: sharedContext.buildId,\n  })\n\n  return workAsyncStorage.run(\n    workStore,\n    // The function to run\n    renderToHTMLOrFlightImpl,\n    // all of it's args\n    req,\n    res,\n    url,\n    pagePath,\n    query,\n    renderOpts,\n    workStore,\n    parsedRequestHeaders,\n    requestEndedState,\n    postponedState,\n    implicitTags,\n    serverComponentsHmrCache,\n    sharedContext\n  )\n}\n\nasync function renderToStream(\n  requestStore: RequestStore,\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  formState: any,\n  postponedState: PostponedState | null\n): Promise<ReadableStream<Uint8Array>> {\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(ctx.nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  const silenceLogger = false\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerResult: null | ReactServerResult = null\n\n  const setHeader = res.setHeader.bind(res)\n  const appendHeader = res.appendHeader.bind(res)\n\n  try {\n    if (\n      // We only want this behavior when running `next dev`\n      renderOpts.dev &&\n      // We only want this behavior when we have React's dev builds available\n      process.env.NODE_ENV === 'development' &&\n      // Edge routes never prerender so we don't have a Prerender environment for anything in edge runtime\n      process.env.NEXT_RUNTIME !== 'edge' &&\n      // We only have a Prerender environment for projects opted into dynamicIO\n      renderOpts.experimental.dynamicIO\n    ) {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload: InitialRSCPayload & {\n        /** Only available during dynamicIO development builds. Used for logging errors. */\n        _validation?: Promise<React.ReactNode>\n      } = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const [resolveValidation, validationOutlet] = createValidationOutlet()\n      RSCPayload._validation = validationOutlet\n\n      const reactServerStream = await workUnitAsyncStorage.run(\n        requestStore,\n        scheduleInSequentialTasks,\n        () => {\n          requestStore.prerenderPhase = true\n          return ComponentMod.renderToReadableStream(\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n              environmentName: () =>\n                requestStore.prerenderPhase === true ? 'Prerender' : 'Server',\n              filterStackFrame(url: string, _functionName: string): boolean {\n                // The default implementation filters out <anonymous> stack frames\n                // but we want to retain them because current Server Components and\n                // built-in Components in parent stacks don't have source location.\n                return !url.startsWith('node:') && !url.includes('node_modules')\n              },\n            }\n          )\n        },\n        () => {\n          requestStore.prerenderPhase = false\n        }\n      )\n\n      spawnDynamicValidationInDev(\n        resolveValidation,\n        tree,\n        ctx,\n        res.statusCode === 404,\n        clientReferenceManifest,\n        workStore.route,\n        requestStore\n      )\n\n      reactServerResult = new ReactServerResult(reactServerStream)\n    } else {\n      // This is a dynamic render. We don't do dynamic tracking because we're not prerendering\n      const RSCPayload = await workUnitAsyncStorage.run(\n        requestStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n\n      reactServerResult = new ReactServerResult(\n        workUnitAsyncStorage.run(\n          requestStore,\n          ComponentMod.renderToReadableStream,\n          RSCPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: serverComponentsErrorHandler,\n          }\n        )\n      )\n    }\n\n    // React doesn't start rendering synchronously but we want the RSC render to have a chance to start\n    // before we begin SSR rendering because we want to capture any available preload headers so we tick\n    // one task before continuing\n    await waitAtLeastOneReactRenderTask()\n\n    // If provided, the postpone state should be parsed as JSON so it can be\n    // provided to React.\n    if (typeof renderOpts.postponed === 'string') {\n      if (postponedState?.type === DynamicState.DATA) {\n        // We have a complete HTML Document in the prerender but we need to\n        // still include the new server component render because it was not included\n        // in the static prelude.\n        const inlinedReactServerDataStream = createInlinedDataReadableStream(\n          reactServerResult.tee(),\n          ctx.nonce,\n          formState\n        )\n\n        return chainStreams(\n          inlinedReactServerDataStream,\n          createDocumentClosingStream()\n        )\n      } else if (postponedState) {\n        // We assume we have dynamic HTML requiring a resume render to complete\n        const postponed = getPostponedFromState(postponedState)\n\n        const resume = require('react-dom/server.edge')\n          .resume as (typeof import('react-dom/server.edge'))['resume']\n\n        const htmlStream = await workUnitAsyncStorage.run(\n          requestStore,\n          resume,\n          <App\n            reactServerStream={reactServerResult.tee()}\n            preinitScripts={preinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          postponed,\n          {\n            onError: htmlRendererErrorHandler,\n            nonce: ctx.nonce,\n          }\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        return await continueDynamicHTMLResume(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consume(),\n            ctx.nonce,\n            formState\n          ),\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        })\n      }\n    }\n\n    // This is a regular dynamic render\n    const renderToReadableStream = require('react-dom/server.edge')\n      .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n    const htmlStream = await workUnitAsyncStorage.run(\n      requestStore,\n      renderToReadableStream,\n      <App\n        reactServerStream={reactServerResult.tee()}\n        preinitScripts={preinitScripts}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={ctx.nonce}\n      />,\n      {\n        onError: htmlRendererErrorHandler,\n        nonce: ctx.nonce,\n        onHeaders: (headers: Headers) => {\n          headers.forEach((value, key) => {\n            appendHeader(key, value)\n          })\n        },\n        maxHeadersLength: renderOpts.reactMaxHeadersLength,\n        bootstrapScripts: [bootstrapScript],\n        formState,\n      }\n    )\n\n    const getServerInsertedHTML = makeGetServerInsertedHTML({\n      polyfills,\n      renderServerInsertedHTML,\n      serverCapturedErrors: allCapturedErrors,\n      basePath: renderOpts.basePath,\n      tracingMetadata: tracingMetadata,\n    })\n    /**\n     * Rules of Static & Dynamic HTML:\n     *\n     *    1.) We must generate static HTML unless the caller explicitly opts\n     *        in to dynamic HTML support.\n     *\n     *    2.) If dynamic HTML support is requested, we must honor that request\n     *        or throw an error. It is the sole responsibility of the caller to\n     *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n     *\n     *   3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n     *       resolve all suspenses and generate a full HTML. e.g. when it's a\n     *       html limited bot requests, we produce the full HTML content.\n     *\n     * These rules help ensure that other existing features like request caching,\n     * coalescing, and ISR continue working as intended.\n     */\n    const generateStaticHTML =\n      renderOpts.supportsDynamicResponse !== true ||\n      !!renderOpts.shouldWaitOnAllReady\n\n    const validateRootLayout = renderOpts.dev\n    return await continueFizzStream(htmlStream, {\n      inlinedDataStream: createInlinedDataReadableStream(\n        reactServerResult.consume(),\n        ctx.nonce,\n        formState\n      ),\n      isStaticGeneration: generateStaticHTML,\n      getServerInsertedHTML,\n      getServerInsertedMetadata,\n      validateRootLayout,\n    })\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      // If there were mutable cookies set, we need to set them on the\n      // response.\n      const headers = new Headers()\n      if (appendMutableCookies(headers, requestStore.mutableCookies)) {\n        setHeader('set-cookie', Array.from(headers.values()))\n      }\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      requestStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? null : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      requestStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    if (reactServerResult === null) {\n      // We errored when we did not have an RSC stream to read from. This is not just a render\n      // error, we need to throw early\n      throw err\n    }\n\n    try {\n      const fizzStream = await workUnitAsyncStorage.run(\n        requestStore,\n        renderToInitialFizzStream,\n        {\n          ReactDOMServer: require('react-dom/server.edge'),\n          element: (\n            <AppWithoutContext\n              reactServerStream={errorServerStream}\n              preinitScripts={errorPreinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              nonce={ctx.nonce}\n            />\n          ),\n          streamOptions: {\n            nonce: ctx.nonce,\n            // Include hydration scripts in the HTML\n            bootstrapScripts: [errorBootstrapScript],\n            formState,\n          },\n        }\n      )\n\n      /**\n       * Rules of Static & Dynamic HTML:\n       *\n       *    1.) We must generate static HTML unless the caller explicitly opts\n       *        in to dynamic HTML support.\n       *\n       *    2.) If dynamic HTML support is requested, we must honor that request\n       *        or throw an error. It is the sole responsibility of the caller to\n       *        ensure they aren't e.g. requesting dynamic HTML for an AMP page.\n       *    3.) If `shouldWaitOnAllReady` is true, which indicates we need to\n       *        resolve all suspenses and generate a full HTML. e.g. when it's a\n       *        html limited bot requests, we produce the full HTML content.\n       *\n       * These rules help ensure that other existing features like request caching,\n       * coalescing, and ISR continue working as intended.\n       */\n      const generateStaticHTML =\n        renderOpts.supportsDynamicResponse !== true ||\n        !!renderOpts.shouldWaitOnAllReady\n      const validateRootLayout = renderOpts.dev\n      return await continueFizzStream(fizzStream, {\n        inlinedDataStream: createInlinedDataReadableStream(\n          // This is intentionally using the readable datastream from the\n          // main render rather than the flight data from the error page\n          // render\n          reactServerResult.consume(),\n          ctx.nonce,\n          formState\n        ),\n        isStaticGeneration: generateStaticHTML,\n        getServerInsertedHTML: makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: [],\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        }),\n        getServerInsertedMetadata,\n        validateRootLayout,\n      })\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nfunction createValidationOutlet() {\n  let resolveValidation: (value: React.ReactNode) => void\n  let outlet = new Promise<React.ReactNode>((resolve) => {\n    resolveValidation = resolve\n  })\n  return [resolveValidation!, outlet] as const\n}\n\nasync function spawnDynamicValidationInDev(\n  resolveValidation: (validatingElement: React.ReactNode) => void,\n  tree: LoaderTree,\n  ctx: AppRenderContext,\n  isNotFound: boolean,\n  clientReferenceManifest: NonNullable<RenderOpts['clientReferenceManifest']>,\n  route: string,\n  requestStore: RequestStore\n): Promise<void> {\n  const { componentMod: ComponentMod } = ctx\n  const rootParams = getRootParams(\n    ComponentMod.tree,\n    ctx.getDynamicParamFromSegment\n  )\n\n  // Prerender controller represents the lifetime of the prerender.\n  // It will be aborted when a Task is complete or a synchronously aborting\n  // API is called. Notably during cache-filling renders this does not actually\n  // terminate the render itself which will continue until all caches are filled\n  const initialServerPrerenderController = new AbortController()\n\n  // This controller represents the lifetime of the React render call. Notably\n  // during the cache-filling render it is different from the prerender controller\n  // because we don't want to end the react render until all caches are filled.\n  const initialServerRenderController = new AbortController()\n\n  const cacheSignal = new CacheSignal()\n  const prerenderResumeDataCache = createPrerenderResumeDataCache()\n  const initialServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: initialServerRenderController.signal,\n    controller: initialServerPrerenderController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const initialClientController = new AbortController()\n  const initialClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: initialClientController.signal,\n    controller: initialClientController,\n    cacheSignal,\n    dynamicTracking: null,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  // We're not going to use the result of this render because the only time it could be used\n  // is if it completes in a microtask and that's likely very rare for any non-trivial app\n  const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n    initialServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  let initialServerStream\n  try {\n    initialServerStream = workUnitAsyncStorage.run(\n      initialServerPrerenderStore,\n      ComponentMod.renderToReadableStream,\n      firstAttemptRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // The render aborted before this error was handled which indicates\n            // the error is caused by unfinished components within the render\n            return\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n        signal: initialServerRenderController.signal,\n      }\n    )\n  } catch (err: unknown) {\n    if (\n      initialServerPrerenderController.signal.aborted ||\n      initialServerRenderController.signal.aborted\n    ) {\n      // These are expected errors that might error the prerender. we ignore them.\n    } else if (\n      process.env.NEXT_DEBUG_BUILD ||\n      process.env.__NEXT_VERBOSE_LOGGING\n    ) {\n      // We don't normally log these errors because we are going to retry anyway but\n      // it can be useful for debugging Next.js itself to get visibility here when needed\n      printDebugThrownValueForProspectiveRender(err, route)\n    }\n  }\n\n  const nonce = '1'\n  const { ServerInsertedHTMLProvider } = createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider } = createServerInsertedMetadata(nonce)\n\n  if (initialServerStream) {\n    const [warmupStream, renderStream] = initialServerStream.tee()\n    initialServerStream = null\n    // Before we attempt the SSR initial render we need to ensure all client modules\n    // are already loaded.\n    await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    const pendingInitialClientResult = workUnitAsyncStorage.run(\n      initialClientPrerenderStore,\n      prerender,\n      <App\n        reactServerStream={renderStream}\n        preinitScripts={() => {}}\n        clientReferenceManifest={clientReferenceManifest}\n        ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n        ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n        nonce={nonce}\n      />,\n      {\n        signal: initialClientController.signal,\n        onError: (err) => {\n          const digest = getDigestForWellKnownError(err)\n\n          if (digest) {\n            return digest\n          }\n\n          if (initialClientController.signal.aborted) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, route)\n          }\n        },\n      }\n    )\n    pendingInitialClientResult.catch((err: unknown) => {\n      if (initialClientController.signal.aborted) {\n        // We aborted the render normally and can ignore this error\n      } else {\n        // We're going to retry to so we normally would suppress this error but\n        // when verbose logging is on we print it\n        if (process.env.__NEXT_VERBOSE_LOGGING) {\n          printDebugThrownValueForProspectiveRender(err, route)\n        }\n      }\n    })\n  }\n\n  await cacheSignal.cacheReady()\n  // It is important that we abort the SSR render first to avoid\n  // connection closed errors from having an incomplete RSC stream\n  initialClientController.abort()\n  initialServerRenderController.abort()\n  initialServerPrerenderController.abort()\n\n  // We've now filled caches and triggered any inadvertent sync bailouts\n  // due to lazy module initialization. We can restart our render to capture results\n\n  const finalServerController = new AbortController()\n  const serverDynamicTracking = createDynamicTrackingState(false)\n\n  const finalServerPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: finalServerController.signal,\n    controller: finalServerController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: serverDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const finalClientController = new AbortController()\n  const clientDynamicTracking = createDynamicTrackingState(false)\n  const dynamicValidation = createDynamicValidationState()\n\n  const finalClientPrerenderStore: PrerenderStore = {\n    type: 'prerender',\n    phase: 'render',\n    rootParams,\n    implicitTags: [],\n    renderSignal: finalClientController.signal,\n    controller: finalClientController,\n    // During the final prerender we don't need to track cache access so we omit the signal\n    cacheSignal: null,\n    dynamicTracking: clientDynamicTracking,\n    revalidate: INFINITE_CACHE,\n    expire: INFINITE_CACHE,\n    stale: INFINITE_CACHE,\n    tags: [],\n    prerenderResumeDataCache,\n  }\n\n  const finalServerPayload = await workUnitAsyncStorage.run(\n    finalServerPrerenderStore,\n    getRSCPayload,\n    tree,\n    ctx,\n    isNotFound\n  )\n\n  const serverPrerenderStreamResult = await prerenderServerWithPhases(\n    finalServerController.signal,\n    () =>\n      workUnitAsyncStorage.run(\n        finalServerPrerenderStore,\n        ComponentMod.renderToReadableStream,\n        finalServerPayload,\n        clientReferenceManifest.clientModules,\n        {\n          onError: (err) => {\n            if (isUseCacheTimeoutError(err)) {\n              return err.digest\n            }\n\n            if (\n              finalServerController.signal.aborted &&\n              isPrerenderInterruptedError(err)\n            ) {\n              return err.digest\n            }\n\n            return getDigestForWellKnownError(err)\n          },\n          signal: finalServerController.signal,\n        }\n      ),\n    () => {\n      finalServerController.abort()\n    }\n  )\n\n  let rootDidError = false\n  const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n  try {\n    const prerender = require('react-dom/static.edge')\n      .prerender as (typeof import('react-dom/static.edge'))['prerender']\n    await prerenderClientWithPhases(\n      () =>\n        workUnitAsyncStorage.run(\n          finalClientPrerenderStore,\n          prerender,\n          <App\n            reactServerStream={serverPhasedStream}\n            preinitScripts={() => {}}\n            clientReferenceManifest={clientReferenceManifest}\n            ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n            ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n            nonce={ctx.nonce}\n          />,\n          {\n            signal: finalClientController.signal,\n            onError: (err, errorInfo) => {\n              if (isUseCacheTimeoutError(err)) {\n                dynamicValidation.dynamicErrors.push(err)\n\n                return\n              }\n\n              if (\n                isPrerenderInterruptedError(err) ||\n                finalClientController.signal.aborted\n              ) {\n                if (!rootDidError) {\n                  // If the root errored before we observe this error then it wasn't caused by something dynamic.\n                  // If the root did not error or is erroring because of a sync dynamic API or a prerender interrupt error\n                  // then we are a dynamic route.\n                  requestStore.usedDynamic = true\n                }\n\n                const componentStack = errorInfo.componentStack\n                if (typeof componentStack === 'string') {\n                  trackAllowedDynamicAccess(\n                    route,\n                    componentStack,\n                    dynamicValidation,\n                    serverDynamicTracking,\n                    clientDynamicTracking\n                  )\n                }\n                return\n              }\n\n              return getDigestForWellKnownError(err)\n            },\n          }\n        ),\n      () => {\n        finalClientController.abort()\n        serverPhasedStream.assertExhausted()\n      }\n    )\n  } catch (err) {\n    rootDidError = true\n    if (\n      isPrerenderInterruptedError(err) ||\n      finalClientController.signal.aborted\n    ) {\n      // we don't have a root because the abort errored in the root. We can just ignore this error\n    } else {\n      // If an error is thrown in the root before prerendering is aborted, we\n      // don't want to rethrow it here, otherwise this would lead to a hanging\n      // response and unhandled rejection. We also don't want to log it, because\n      // it's most likely already logged as part of the normal render. So we\n      // just fall through here, to make sure `resolveValidation` is called.\n    }\n  }\n\n  function LogDynamicValidation() {\n    try {\n      throwIfDisallowedDynamic(\n        route,\n        dynamicValidation,\n        serverDynamicTracking,\n        clientDynamicTracking\n      )\n    } catch {}\n    return null\n  }\n\n  resolveValidation(<LogDynamicValidation />)\n}\n\ntype PrerenderToStreamResult = {\n  stream: ReadableStream<Uint8Array>\n  digestErrorsMap: Map<string, DigestedError>\n  ssrErrors: Array<unknown>\n  dynamicAccess?: null | Array<DynamicAccess>\n  collectedRevalidate: number\n  collectedExpire: number\n  collectedStale: number\n  collectedTags: null | string[]\n}\n\n/**\n * Determines whether we should generate static flight data.\n */\nfunction shouldGenerateStaticFlightData(workStore: WorkStore): boolean {\n  const { isStaticGeneration } = workStore\n  if (!isStaticGeneration) return false\n\n  return true\n}\n\nasync function prerenderToStream(\n  req: BaseNextRequest,\n  res: BaseNextResponse,\n  ctx: AppRenderContext,\n  metadata: AppPageRenderResultMetadata,\n  workStore: WorkStore,\n  tree: LoaderTree,\n  implicitTags: Array<string>\n): Promise<PrerenderToStreamResult> {\n  // When prerendering formState is always null. We still include it\n  // because some shared APIs expect a formState value and this is slightly\n  // more explicit than making it an optional function argument\n  const formState = null\n  const rootParams = getRootParams(tree, ctx.getDynamicParamFromSegment)\n\n  const renderOpts = ctx.renderOpts\n  const ComponentMod = renderOpts.ComponentMod\n  // TODO: fix this typescript\n  const clientReferenceManifest = renderOpts.clientReferenceManifest!\n  const fallbackRouteParams = workStore.fallbackRouteParams\n\n  const { ServerInsertedHTMLProvider, renderServerInsertedHTML } =\n    createServerInsertedHTML()\n  const { ServerInsertedMetadataProvider, getServerInsertedMetadata } =\n    createServerInsertedMetadata(ctx.nonce)\n\n  const tracingMetadata = getTracedMetadata(\n    getTracer().getTracePropagationData(),\n    renderOpts.experimental.clientTraceMetadata\n  )\n\n  const polyfills: JSX.IntrinsicElements['script'][] =\n    renderOpts.buildManifest.polyfillFiles\n      .filter(\n        (polyfill) =>\n          polyfill.endsWith('.js') && !polyfill.endsWith('.module.js')\n      )\n      .map((polyfill) => ({\n        src: `${ctx.assetPrefix}/_next/${polyfill}${getAssetQueryString(\n          ctx,\n          false\n        )}`,\n        integrity: renderOpts.subresourceIntegrityManifest?.[polyfill],\n        crossOrigin: renderOpts.crossOrigin,\n        noModule: true,\n        nonce: ctx.nonce,\n      }))\n\n  const [preinitScripts, bootstrapScript] = getRequiredScripts(\n    renderOpts.buildManifest,\n    // Why is assetPrefix optional on renderOpts?\n    // @TODO make it default empty string on renderOpts and get rid of it from ctx\n    ctx.assetPrefix,\n    renderOpts.crossOrigin,\n    renderOpts.subresourceIntegrityManifest,\n    getAssetQueryString(ctx, true),\n    ctx.nonce,\n    renderOpts.page\n  )\n\n  const reactServerErrorsByDigest: Map<string, DigestedError> = new Map()\n  // We don't report errors during prerendering through our instrumentation hooks\n  const silenceLogger = !!renderOpts.experimental.isRoutePPREnabled\n  function onHTMLRenderRSCError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'react-server-components')\n    )\n  }\n  const serverComponentsErrorHandler = createHTMLReactServerErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    silenceLogger,\n    onHTMLRenderRSCError\n  )\n\n  function onHTMLRenderSSRError(err: DigestedError) {\n    return renderOpts.onInstrumentationRequestError?.(\n      err,\n      req,\n      createErrorContext(ctx, 'server-rendering')\n    )\n  }\n  const allCapturedErrors: Array<unknown> = []\n  const htmlRendererErrorHandler = createHTMLErrorHandler(\n    !!renderOpts.dev,\n    !!renderOpts.nextExport,\n    reactServerErrorsByDigest,\n    allCapturedErrors,\n    silenceLogger,\n    onHTMLRenderSSRError\n  )\n\n  let reactServerPrerenderResult:\n    | null\n    | ReactServerPrerenderResult\n    | ServerPrerenderStreamResult = null\n  const setMetadataHeader = (name: string) => {\n    metadata.headers ??= {}\n    metadata.headers[name] = res.getHeader(name)\n  }\n  const setHeader = (name: string, value: string | string[]) => {\n    res.setHeader(name, value)\n    setMetadataHeader(name)\n    return res\n  }\n  const appendHeader = (name: string, value: string | string[]) => {\n    if (Array.isArray(value)) {\n      value.forEach((item) => {\n        res.appendHeader(name, item)\n      })\n    } else {\n      res.appendHeader(name, value)\n    }\n    setMetadataHeader(name)\n  }\n\n  let prerenderStore: PrerenderStore | null = null\n\n  try {\n    if (renderOpts.experimental.dynamicIO) {\n      if (renderOpts.experimental.isRoutePPREnabled) {\n        /**\n         * dynamicIO with PPR\n         *\n         * The general approach is to render the RSC stream first allowing any cache reads to resolve.\n         * Once we have settled all cache reads we restart the render and abort after a single Task.\n         *\n         * Unlike with the non PPR case we can't synchronously abort the render when a dynamic API is used\n         * during the initial render because we need to ensure all caches can be filled as part of the initial Task\n         * and a synchronous abort might prevent us from filling all caches.\n         *\n         * Once the render is complete we allow the SSR render to finish and use a combination of the postponed state\n         * and the reactServerIsDynamic value to determine how to treat the resulting render\n         */\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        // The cacheSignal helps us track whether caches are still filling or we are ready\n        // to cut the render off.\n        const cacheSignal = new CacheSignal()\n\n        // The resume data cache here should use a fresh instance as it's\n        // performing a fresh prerender. If we get to implementing the\n        // prerendering of an already prerendered page, we should use the passed\n        // resume data cache instead.\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const initialServerPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const pendingInitialServerResult = workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          ComponentMod.prerender,\n          initialServerPayload,\n          clientReferenceManifest.clientModules,\n          {\n            onError: (err) => {\n              const digest = getDigestForWellKnownError(err)\n\n              if (digest) {\n                return digest\n              }\n\n              if (initialServerPrerenderController.signal.aborted) {\n                // The render aborted before this error was handled which indicates\n                // the error is caused by unfinished components within the render\n                return\n              } else if (\n                process.env.NEXT_DEBUG_BUILD ||\n                process.env.__NEXT_VERBOSE_LOGGING\n              ) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            },\n            // we don't care to track postpones during the prospective render because we need\n            // to always do a final render anyway\n            onPostpone: undefined,\n            // We don't want to stop rendering until the cacheSignal is complete so we pass\n            // a different signal to this render call than is used by dynamic APIs to signify\n            // transitioning out of the prerender environment\n            signal: initialServerRenderController.signal,\n          }\n        )\n\n        await cacheSignal.cacheReady()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        let initialServerResult\n        try {\n          initialServerResult = await createReactServerPrerenderResult(\n            pendingInitialServerResult\n          )\n        } catch (err) {\n          if (\n            initialServerRenderController.signal.aborted ||\n            initialServerPrerenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerResult) {\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(\n            initialServerResult.asStream(),\n            clientReferenceManifest\n          )\n\n          const initialClientController = new AbortController()\n          const initialClientPrerenderStore: PrerenderStore = {\n            type: 'prerender',\n            phase: 'render',\n            rootParams,\n            implicitTags: implicitTags,\n            renderSignal: initialClientController.signal,\n            controller: initialClientController,\n            cacheSignal: null,\n            dynamicTracking: null,\n            revalidate: INFINITE_CACHE,\n            expire: INFINITE_CACHE,\n            stale: INFINITE_CACHE,\n            tags: [...implicitTags],\n            prerenderResumeDataCache,\n          }\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          await prerenderAndAbortInSequentialTasks(\n            () =>\n              workUnitAsyncStorage.run(\n                initialClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={initialServerResult.asUnclosingStream()}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={ctx.nonce}\n                />,\n                {\n                  signal: initialClientController.signal,\n                  onError: (err) => {\n                    const digest = getDigestForWellKnownError(err)\n\n                    if (digest) {\n                      return digest\n                    }\n\n                    if (initialClientController.signal.aborted) {\n                      // These are expected errors that might error the prerender. we ignore them.\n                    } else if (\n                      process.env.NEXT_DEBUG_BUILD ||\n                      process.env.__NEXT_VERBOSE_LOGGING\n                    ) {\n                      // We don't normally log these errors because we are going to retry anyway but\n                      // it can be useful for debugging Next.js itself to get visibility here when needed\n                      printDebugThrownValueForProspectiveRender(\n                        err,\n                        workStore.route\n                      )\n                    }\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              initialClientController.abort()\n            }\n          ).catch((err) => {\n            if (\n              initialServerRenderController.signal.aborted ||\n              isPrerenderInterruptedError(err)\n            ) {\n              // These are expected errors that might error the prerender. we ignore them.\n            } else if (\n              process.env.NEXT_DEBUG_BUILD ||\n              process.env.__NEXT_VERBOSE_LOGGING\n            ) {\n              // We don't normally log these errors because we are going to retry anyway but\n              // it can be useful for debugging Next.js itself to get visibility here when needed\n              printDebugThrownValueForProspectiveRender(err, workStore.route)\n            }\n          })\n        }\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalRenderPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const finalAttemptRSCPayload = await workUnitAsyncStorage.run(\n          finalRenderPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n        let prerenderIsPending = true\n        const reactServerResult = (reactServerPrerenderResult =\n          await createReactServerPrerenderResult(\n            prerenderAndAbortInSequentialTasks(\n              async () => {\n                const prerenderResult = await workUnitAsyncStorage.run(\n                  // The store to scope\n                  finalRenderPrerenderStore,\n                  // The function to run\n                  ComponentMod.prerender,\n                  // ... the arguments for the function to run\n                  finalAttemptRSCPayload,\n                  clientReferenceManifest.clientModules,\n                  {\n                    onError: (err: unknown) => {\n                      return serverComponentsErrorHandler(err)\n                    },\n                    signal: finalServerController.signal,\n                  }\n                )\n                prerenderIsPending = false\n                return prerenderResult\n              },\n              () => {\n                if (finalServerController.signal.aborted) {\n                  // If the server controller is already aborted we must have called something\n                  // that required aborting the prerender synchronously such as with new Date()\n                  serverIsDynamic = true\n                  return\n                }\n\n                if (prerenderIsPending) {\n                  // If prerenderIsPending then we have blocked for longer than a Task and we assume\n                  // there is something unfinished.\n                  serverIsDynamic = true\n                }\n                finalServerController.abort()\n              }\n            )\n          ))\n\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const finalClientController = new AbortController()\n        const finalClientPrerenderStore: PrerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // For HTML Generation we don't need to track cache reads (RSC only)\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        }\n\n        let clientIsDynamic = false\n        let dynamicValidation = createDynamicValidationState()\n\n        const prerender = require('react-dom/static.edge')\n          .prerender as (typeof import('react-dom/static.edge'))['prerender']\n        let { prelude, postponed } = await prerenderAndAbortInSequentialTasks(\n          () =>\n            workUnitAsyncStorage.run(\n              finalClientPrerenderStore,\n              prerender,\n              <App\n                reactServerStream={reactServerResult.asUnclosingStream()}\n                preinitScripts={preinitScripts}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={ctx.nonce}\n              />,\n              {\n                signal: finalClientController.signal,\n                onError: (err: unknown, errorInfo: ErrorInfo) => {\n                  if (\n                    isPrerenderInterruptedError(err) ||\n                    finalClientController.signal.aborted\n                  ) {\n                    clientIsDynamic = true\n\n                    const componentStack: string | undefined = (\n                      errorInfo as any\n                    ).componentStack\n                    if (typeof componentStack === 'string') {\n                      trackAllowedDynamicAccess(\n                        workStore.route,\n                        componentStack,\n                        dynamicValidation,\n                        serverDynamicTracking,\n                        clientDynamicTracking\n                      )\n                    }\n                    return\n                  }\n\n                  return htmlRendererErrorHandler(err, errorInfo)\n                },\n                onHeaders: (headers: Headers) => {\n                  headers.forEach((value, key) => {\n                    appendHeader(key, value)\n                  })\n                },\n                maxHeadersLength: renderOpts.reactMaxHeadersLength,\n                bootstrapScripts: [bootstrapScript],\n              }\n            ),\n          () => {\n            finalClientController.abort()\n          }\n        )\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalRenderPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          if (postponed != null) {\n            // Dynamic HTML case\n            metadata.postponed = await getDynamicHTMLPostponedState(\n              postponed,\n              fallbackRouteParams,\n              prerenderResumeDataCache\n            )\n          } else {\n            // Dynamic Data case\n            metadata.postponed = await getDynamicDataPostponedState(\n              prerenderResumeDataCache\n            )\n          }\n          reactServerResult.consume()\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueDynamicPrerender(prelude, {\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: finalRenderPrerenderStore.stale,\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        } else {\n          // Static case\n          if (workStore.forceDynamic) {\n            throw new StaticGenBailoutError(\n              'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n            )\n          }\n\n          let htmlStream = prelude\n          if (postponed != null) {\n            // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n            // so we can set all the postponed boundaries to client render mode before we store the HTML response\n            const resume = require('react-dom/server.edge')\n              .resume as (typeof import('react-dom/server.edge'))['resume']\n\n            // We don't actually want to render anything so we just pass a stream\n            // that never resolves. The resume call is going to abort immediately anyway\n            const foreverStream = new ReadableStream<Uint8Array>()\n\n            const resumeStream = await resume(\n              <App\n                reactServerStream={foreverStream}\n                preinitScripts={() => {}}\n                clientReferenceManifest={clientReferenceManifest}\n                ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n                nonce={ctx.nonce}\n              />,\n              JSON.parse(JSON.stringify(postponed)),\n              {\n                signal: createPostponedAbortSignal('static prerender resume'),\n                onError: htmlRendererErrorHandler,\n                nonce: ctx.nonce,\n              }\n            )\n\n            // First we write everything from the prerender, then we write everything from the aborted resume render\n            htmlStream = chainStreams(prelude, resumeStream)\n          }\n\n          return {\n            digestErrorsMap: reactServerErrorsByDigest,\n            ssrErrors: allCapturedErrors,\n            stream: await continueStaticPrerender(htmlStream, {\n              inlinedDataStream: createInlinedDataReadableStream(\n                reactServerResult.consumeAsStream(),\n                ctx.nonce,\n                formState\n              ),\n              getServerInsertedHTML,\n              getServerInsertedMetadata,\n            }),\n            dynamicAccess: consumeDynamicAccess(\n              serverDynamicTracking,\n              clientDynamicTracking\n            ),\n            // TODO: Should this include the SSR pass?\n            collectedRevalidate: finalRenderPrerenderStore.revalidate,\n            collectedExpire: finalRenderPrerenderStore.expire,\n            collectedStale: finalRenderPrerenderStore.stale,\n            collectedTags: finalRenderPrerenderStore.tags,\n          }\n        }\n      } else {\n        /**\n         * dynamicIO without PPR\n         *\n         * The general approach is to render the RSC tree first allowing for any inflight\n         * caches to resolve. Once we have settled inflight caches we can check and see if any\n         * synchronous dynamic APIs were used. If so we don't need to bother doing anything more\n         * because the page will be dynamic on re-render anyway\n         *\n         * If no sync dynamic APIs were used we then re-render and abort after a single Task.\n         * If the render errors we know that the page has some dynamic IO. This assumes and relies\n         * upon caches reading from a in process memory cache and resolving in a microtask. While this\n         * is true from our own default cache implementation and if you don't exceed our LRU size it\n         * might not be true for custom cache implementations.\n         *\n         * Future implementations can do some different strategies during build like using IPC to\n         * synchronously fill caches during this special rendering mode. For now this heuristic should work\n         */\n\n        const cache = workStore.incrementalCache\n        if (!cache) {\n          throw new Error(\n            'Expected incremental cache to exist. This is a bug in Next.js'\n          )\n        }\n\n        // Prerender controller represents the lifetime of the prerender.\n        // It will be aborted when a Task is complete or a synchronously aborting\n        // API is called. Notably during cache-filling renders this does not actually\n        // terminate the render itself which will continue until all caches are filled\n        const initialServerPrerenderController = new AbortController()\n\n        // This controller represents the lifetime of the React render call. Notably\n        // during the cache-filling render it is different from the prerender controller\n        // because we don't want to end the react render until all caches are filled.\n        const initialServerRenderController = new AbortController()\n\n        const cacheSignal = new CacheSignal()\n        const prerenderResumeDataCache = createPrerenderResumeDataCache()\n\n        const initialServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: initialServerRenderController.signal,\n          controller: initialServerPrerenderController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const initialClientController = new AbortController()\n        const initialClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: initialClientController.signal,\n          controller: initialClientController,\n          cacheSignal,\n          dynamicTracking: null,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        // We're not going to use the result of this render because the only time it could be used\n        // is if it completes in a microtask and that's likely very rare for any non-trivial app\n        const firstAttemptRSCPayload = await workUnitAsyncStorage.run(\n          initialServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        let initialServerStream\n        try {\n          initialServerStream = workUnitAsyncStorage.run(\n            initialServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            firstAttemptRSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (\n                  initialServerPrerenderController.signal.aborted ||\n                  initialServerRenderController.signal.aborted\n                ) {\n                  // The render aborted before this error was handled which indicates\n                  // the error is caused by unfinished components within the render\n                  return\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              signal: initialServerRenderController.signal,\n            }\n          )\n        } catch (err: unknown) {\n          if (\n            initialServerPrerenderController.signal.aborted ||\n            initialServerRenderController.signal.aborted\n          ) {\n            // These are expected errors that might error the prerender. we ignore them.\n          } else if (\n            process.env.NEXT_DEBUG_BUILD ||\n            process.env.__NEXT_VERBOSE_LOGGING\n          ) {\n            // We don't normally log these errors because we are going to retry anyway but\n            // it can be useful for debugging Next.js itself to get visibility here when needed\n            printDebugThrownValueForProspectiveRender(err, workStore.route)\n          }\n        }\n\n        if (initialServerStream) {\n          const [warmupStream, renderStream] = initialServerStream.tee()\n          initialServerStream = null\n          // Before we attempt the SSR initial render we need to ensure all client modules\n          // are already loaded.\n          await warmFlightResponse(warmupStream, clientReferenceManifest)\n\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const pendingInitialClientResult = workUnitAsyncStorage.run(\n            initialClientPrerenderStore,\n            prerender,\n            <App\n              reactServerStream={renderStream}\n              preinitScripts={preinitScripts}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={ctx.nonce}\n            />,\n            {\n              signal: initialClientController.signal,\n              onError: (err) => {\n                const digest = getDigestForWellKnownError(err)\n\n                if (digest) {\n                  return digest\n                }\n\n                if (initialClientController.signal.aborted) {\n                  // These are expected errors that might error the prerender. we ignore them.\n                } else if (\n                  process.env.NEXT_DEBUG_BUILD ||\n                  process.env.__NEXT_VERBOSE_LOGGING\n                ) {\n                  // We don't normally log these errors because we are going to retry anyway but\n                  // it can be useful for debugging Next.js itself to get visibility here when needed\n                  printDebugThrownValueForProspectiveRender(\n                    err,\n                    workStore.route\n                  )\n                }\n              },\n              bootstrapScripts: [bootstrapScript],\n            }\n          )\n          pendingInitialClientResult.catch((err: unknown) => {\n            if (initialClientController.signal.aborted) {\n              // We aborted the render normally and can ignore this error\n            } else {\n              // We're going to retry to so we normally would suppress this error but\n              // when verbose logging is on we print it\n              if (process.env.__NEXT_VERBOSE_LOGGING) {\n                printDebugThrownValueForProspectiveRender(err, workStore.route)\n              }\n            }\n          })\n        }\n\n        await cacheSignal.cacheReady()\n        // It is important that we abort the SSR render first to avoid\n        // connection closed errors from having an incomplete RSC stream\n        initialClientController.abort()\n        initialServerRenderController.abort()\n        initialServerPrerenderController.abort()\n\n        // We've now filled caches and triggered any inadvertant sync bailouts\n        // due to lazy module initialization. We can restart our render to capture results\n\n        let serverIsDynamic = false\n        const finalServerController = new AbortController()\n        const serverDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n\n        const finalServerPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalServerController.signal,\n          controller: finalServerController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: serverDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        let clientIsDynamic = false\n        const finalClientController = new AbortController()\n        const clientDynamicTracking = createDynamicTrackingState(\n          renderOpts.isDebugDynamicAccesses\n        )\n        const dynamicValidation = createDynamicValidationState()\n\n        const finalClientPrerenderStore: PrerenderStore = (prerenderStore = {\n          type: 'prerender',\n          phase: 'render',\n          rootParams,\n          implicitTags: implicitTags,\n          renderSignal: finalClientController.signal,\n          controller: finalClientController,\n          // During the final prerender we don't need to track cache access so we omit the signal\n          cacheSignal: null,\n          dynamicTracking: clientDynamicTracking,\n          revalidate: INFINITE_CACHE,\n          expire: INFINITE_CACHE,\n          stale: INFINITE_CACHE,\n          tags: [...implicitTags],\n          prerenderResumeDataCache,\n        })\n\n        const finalServerPayload = await workUnitAsyncStorage.run(\n          finalServerPrerenderStore,\n          getRSCPayload,\n          tree,\n          ctx,\n          res.statusCode === 404\n        )\n\n        const serverPrerenderStreamResult = (reactServerPrerenderResult =\n          await prerenderServerWithPhases(\n            finalServerController.signal,\n            () =>\n              workUnitAsyncStorage.run(\n                finalServerPrerenderStore,\n                ComponentMod.renderToReadableStream,\n                finalServerPayload,\n                clientReferenceManifest.clientModules,\n                {\n                  onError: (err: unknown) => {\n                    if (finalServerController.signal.aborted) {\n                      serverIsDynamic = true\n                      if (isPrerenderInterruptedError(err)) {\n                        return err.digest\n                      }\n                      return getDigestForWellKnownError(err)\n                    }\n\n                    return serverComponentsErrorHandler(err)\n                  },\n                  signal: finalServerController.signal,\n                }\n              ),\n            () => {\n              finalServerController.abort()\n            }\n          ))\n\n        let htmlStream\n        const serverPhasedStream = serverPrerenderStreamResult.asPhasedStream()\n        try {\n          const prerender = require('react-dom/static.edge')\n            .prerender as (typeof import('react-dom/static.edge'))['prerender']\n          const result = await prerenderClientWithPhases(\n            () =>\n              workUnitAsyncStorage.run(\n                finalClientPrerenderStore,\n                prerender,\n                <App\n                  reactServerStream={serverPhasedStream}\n                  preinitScripts={preinitScripts}\n                  clientReferenceManifest={clientReferenceManifest}\n                  ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n                  ServerInsertedMetadataProvider={\n                    ServerInsertedMetadataProvider\n                  }\n                  nonce={ctx.nonce}\n                />,\n                {\n                  signal: finalClientController.signal,\n                  onError: (err: unknown, errorInfo: ErrorInfo) => {\n                    if (\n                      isPrerenderInterruptedError(err) ||\n                      finalClientController.signal.aborted\n                    ) {\n                      clientIsDynamic = true\n\n                      const componentStack: string | undefined = (\n                        errorInfo as any\n                      ).componentStack\n                      if (typeof componentStack === 'string') {\n                        trackAllowedDynamicAccess(\n                          workStore.route,\n                          componentStack,\n                          dynamicValidation,\n                          serverDynamicTracking,\n                          clientDynamicTracking\n                        )\n                      }\n                      return\n                    }\n\n                    return htmlRendererErrorHandler(err, errorInfo)\n                  },\n                  bootstrapScripts: [bootstrapScript],\n                }\n              ),\n            () => {\n              finalClientController.abort()\n              serverPhasedStream.assertExhausted()\n            }\n          )\n          htmlStream = result.prelude\n        } catch (err) {\n          if (\n            isPrerenderInterruptedError(err) ||\n            finalClientController.signal.aborted\n          ) {\n            // we don't have a root because the abort errored in the root. We can just ignore this error\n          } else {\n            // This error is something else and should bubble up\n            throw err\n          }\n        }\n\n        throwIfDisallowedDynamic(\n          workStore.route,\n          dynamicValidation,\n          serverDynamicTracking,\n          clientDynamicTracking\n        )\n\n        if (serverIsDynamic || clientIsDynamic) {\n          const dynamicReason = serverIsDynamic\n            ? getFirstDynamicReason(serverDynamicTracking)\n            : getFirstDynamicReason(clientDynamicTracking)\n          if (dynamicReason) {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically because it used \\`${dynamicReason}\\`. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          } else {\n            throw new DynamicServerError(\n              `Route \"${workStore.route}\" couldn't be rendered statically it accessed data without explicitly caching it. See more info here: https://nextjs.org/docs/messages/next-prerender-data`\n            )\n          }\n        }\n\n        const flightData = await streamToBuffer(\n          serverPrerenderStreamResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          finalClientPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n\n        const getServerInsertedHTML = makeGetServerInsertedHTML({\n          polyfills,\n          renderServerInsertedHTML,\n          serverCapturedErrors: allCapturedErrors,\n          basePath: renderOpts.basePath,\n          tracingMetadata: tracingMetadata,\n        })\n        const validateRootLayout = renderOpts.dev\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueFizzStream(htmlStream!, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              serverPrerenderStreamResult.asStream(),\n              ctx.nonce,\n              formState\n            ),\n            isStaticGeneration: true,\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n            validateRootLayout,\n          }),\n          dynamicAccess: consumeDynamicAccess(\n            serverDynamicTracking,\n            clientDynamicTracking\n          ),\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: finalServerPrerenderStore.revalidate,\n          collectedExpire: finalServerPrerenderStore.expire,\n          collectedStale: finalServerPrerenderStore.stale,\n          collectedTags: finalServerPrerenderStore.tags,\n        }\n      }\n    } else if (renderOpts.experimental.isRoutePPREnabled) {\n      // We're statically generating with PPR and need to do dynamic tracking\n      let dynamicTracking = createDynamicTrackingState(\n        renderOpts.isDebugDynamicAccesses\n      )\n\n      const prerenderResumeDataCache = createPrerenderResumeDataCache()\n      const reactServerPrerenderStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags: implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n        prerenderResumeDataCache,\n      })\n      const RSCPayload = await workUnitAsyncStorage.run(\n        reactServerPrerenderStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            reactServerPrerenderStore,\n            ComponentMod.renderToReadableStream,\n            // ... the arguments for the function to run\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const ssrPrerenderStore: PrerenderStore = {\n        type: 'prerender-ppr',\n        phase: 'render',\n        rootParams,\n        implicitTags: implicitTags,\n        dynamicTracking,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n        prerenderResumeDataCache,\n      }\n      const prerender = require('react-dom/static.edge')\n        .prerender as (typeof import('react-dom/static.edge'))['prerender']\n      const { prelude, postponed } = await workUnitAsyncStorage.run(\n        ssrPrerenderStore,\n        prerender,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={ctx.nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          onHeaders: (headers: Headers) => {\n            headers.forEach((value, key) => {\n              appendHeader(key, value)\n            })\n          },\n          maxHeadersLength: renderOpts.reactMaxHeadersLength,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n\n      // After awaiting here we've waited for the entire RSC render to complete. Crucially this means\n      // that when we detect whether we've used dynamic APIs below we know we'll have picked up even\n      // parts of the React Server render that might not be used in the SSR render.\n      const flightData = await streamToBuffer(reactServerResult.asStream())\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          ssrPrerenderStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      /**\n       * When prerendering there are three outcomes to consider\n       *\n       *   Dynamic HTML:      The prerender has dynamic holes (caused by using Next.js Dynamic Rendering APIs)\n       *                      We will need to resume this result when requests are handled and we don't include\n       *                      any server inserted HTML or inlined flight data in the static HTML\n       *\n       *   Dynamic Data:      The prerender has no dynamic holes but dynamic APIs were used. We will not\n       *                      resume this render when requests are handled but we will generate new inlined\n       *                      flight data since it is dynamic and differences may end up reconciling on the client\n       *\n       *   Static:            The prerender has no dynamic holes and no dynamic APIs were used. We statically encode\n       *                      all server inserted HTML and flight data\n       */\n      // First we check if we have any dynamic holes in our HTML prerender\n      if (accessedDynamicData(dynamicTracking.dynamicAccesses)) {\n        if (postponed != null) {\n          // Dynamic HTML case.\n          metadata.postponed = await getDynamicHTMLPostponedState(\n            postponed,\n            fallbackRouteParams,\n            prerenderResumeDataCache\n          )\n        } else {\n          // Dynamic Data case.\n          metadata.postponed = await getDynamicDataPostponedState(\n            prerenderResumeDataCache\n          )\n        }\n        // Regardless of whether this is the Dynamic HTML or Dynamic Data case we need to ensure we include\n        // server inserted html in the static response because the html that is part of the prerender may depend on it\n        // It is possible in the set of stream transforms for Dynamic HTML vs Dynamic Data may differ but currently both states\n        // require the same set so we unify the code path here\n        reactServerResult.consume()\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else if (fallbackRouteParams && fallbackRouteParams.size > 0) {\n        // Rendering the fallback case.\n        metadata.postponed = await getDynamicDataPostponedState(\n          prerenderResumeDataCache\n        )\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueDynamicPrerender(prelude, {\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      } else {\n        // Static case\n        // We still have not used any dynamic APIs. At this point we can produce an entirely static prerender response\n        if (workStore.forceDynamic) {\n          throw new StaticGenBailoutError(\n            'Invariant: a Page with `dynamic = \"force-dynamic\"` did not trigger the dynamic pathway. This is a bug in Next.js'\n          )\n        }\n\n        let htmlStream = prelude\n        if (postponed != null) {\n          // We postponed but nothing dynamic was used. We resume the render now and immediately abort it\n          // so we can set all the postponed boundaries to client render mode before we store the HTML response\n          const resume = require('react-dom/server.edge')\n            .resume as (typeof import('react-dom/server.edge'))['resume']\n\n          // We don't actually want to render anything so we just pass a stream\n          // that never resolves. The resume call is going to abort immediately anyway\n          const foreverStream = new ReadableStream<Uint8Array>()\n\n          const resumeStream = await resume(\n            <App\n              reactServerStream={foreverStream}\n              preinitScripts={() => {}}\n              clientReferenceManifest={clientReferenceManifest}\n              ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n              ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n              nonce={ctx.nonce}\n            />,\n            JSON.parse(JSON.stringify(postponed)),\n            {\n              signal: createPostponedAbortSignal('static prerender resume'),\n              onError: htmlRendererErrorHandler,\n              nonce: ctx.nonce,\n            }\n          )\n\n          // First we write everything from the prerender, then we write everything from the aborted resume render\n          htmlStream = chainStreams(prelude, resumeStream)\n        }\n\n        return {\n          digestErrorsMap: reactServerErrorsByDigest,\n          ssrErrors: allCapturedErrors,\n          stream: await continueStaticPrerender(htmlStream, {\n            inlinedDataStream: createInlinedDataReadableStream(\n              reactServerResult.consumeAsStream(),\n              ctx.nonce,\n              formState\n            ),\n            getServerInsertedHTML,\n            getServerInsertedMetadata,\n          }),\n          dynamicAccess: dynamicTracking.dynamicAccesses,\n          // TODO: Should this include the SSR pass?\n          collectedRevalidate: reactServerPrerenderStore.revalidate,\n          collectedExpire: reactServerPrerenderStore.expire,\n          collectedStale: reactServerPrerenderStore.stale,\n          collectedTags: reactServerPrerenderStore.tags,\n        }\n      }\n    } else {\n      const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n        type: 'prerender-legacy',\n        phase: 'render',\n        rootParams,\n        implicitTags: implicitTags,\n        revalidate: INFINITE_CACHE,\n        expire: INFINITE_CACHE,\n        stale: INFINITE_CACHE,\n        tags: [...implicitTags],\n      })\n      // This is a regular static generation. We don't do dynamic tracking because we rely on\n      // the old-school dynamic error handling to bail out of static generation\n      const RSCPayload = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        getRSCPayload,\n        tree,\n        ctx,\n        res.statusCode === 404\n      )\n      const reactServerResult = (reactServerPrerenderResult =\n        await createReactServerPrerenderResultFromRender(\n          workUnitAsyncStorage.run(\n            prerenderLegacyStore,\n            ComponentMod.renderToReadableStream,\n            RSCPayload,\n            clientReferenceManifest.clientModules,\n            {\n              onError: serverComponentsErrorHandler,\n            }\n          )\n        ))\n\n      const renderToReadableStream = require('react-dom/server.edge')\n        .renderToReadableStream as (typeof import('react-dom/server.edge'))['renderToReadableStream']\n\n      const htmlStream = await workUnitAsyncStorage.run(\n        prerenderLegacyStore,\n        renderToReadableStream,\n        <App\n          reactServerStream={reactServerResult.asUnclosingStream()}\n          preinitScripts={preinitScripts}\n          clientReferenceManifest={clientReferenceManifest}\n          ServerInsertedHTMLProvider={ServerInsertedHTMLProvider}\n          ServerInsertedMetadataProvider={ServerInsertedMetadataProvider}\n          nonce={ctx.nonce}\n        />,\n        {\n          onError: htmlRendererErrorHandler,\n          nonce: ctx.nonce,\n          bootstrapScripts: [bootstrapScript],\n        }\n      )\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(reactServerResult.asStream())\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const getServerInsertedHTML = makeGetServerInsertedHTML({\n        polyfills,\n        renderServerInsertedHTML,\n        serverCapturedErrors: allCapturedErrors,\n        basePath: renderOpts.basePath,\n        tracingMetadata: tracingMetadata,\n      })\n      return {\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(htmlStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            reactServerResult.consumeAsStream(),\n            ctx.nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML,\n          getServerInsertedMetadata,\n        }),\n        // TODO: Should this include the SSR pass?\n        collectedRevalidate: prerenderLegacyStore.revalidate,\n        collectedExpire: prerenderLegacyStore.expire,\n        collectedStale: prerenderLegacyStore.stale,\n        collectedTags: prerenderLegacyStore.tags,\n      }\n    }\n  } catch (err) {\n    if (\n      isStaticGenBailoutError(err) ||\n      (typeof err === 'object' &&\n        err !== null &&\n        'message' in err &&\n        typeof err.message === 'string' &&\n        err.message.includes(\n          'https://nextjs.org/docs/advanced-features/static-html-export'\n        ))\n    ) {\n      // Ensure that \"next dev\" prints the red error overlay\n      throw err\n    }\n\n    // If this is a static generation error, we need to throw it so that it\n    // can be handled by the caller if we're in static generation mode.\n    if (isDynamicServerError(err)) {\n      throw err\n    }\n\n    // If a bailout made it to this point, it means it wasn't wrapped inside\n    // a suspense boundary.\n    const shouldBailoutToCSR = isBailoutToCSRError(err)\n    if (shouldBailoutToCSR) {\n      const stack = getStackWithoutErrorMessage(err)\n      error(\n        `${err.reason} should be wrapped in a suspense boundary at page \"${ctx.pagePath}\". Read more: https://nextjs.org/docs/messages/missing-suspense-with-csr-bailout\\n${stack}`\n      )\n\n      throw err\n    }\n\n    // If we errored when we did not have an RSC stream to read from. This is\n    // not just a render error, we need to throw early.\n    if (reactServerPrerenderResult === null) {\n      throw err\n    }\n\n    let errorType: MetadataErrorType | 'redirect' | undefined\n\n    if (isHTTPAccessFallbackError(err)) {\n      res.statusCode = getAccessFallbackHTTPStatus(err)\n      errorType = getAccessFallbackErrorTypeByStatus(res.statusCode)\n    } else if (isRedirectError(err)) {\n      errorType = 'redirect'\n      res.statusCode = getRedirectStatusCodeFromError(err)\n\n      const redirectUrl = addPathPrefix(\n        getURLFromRedirectError(err),\n        renderOpts.basePath\n      )\n\n      setHeader('location', redirectUrl)\n    } else if (!shouldBailoutToCSR) {\n      res.statusCode = 500\n    }\n\n    const [errorPreinitScripts, errorBootstrapScript] = getRequiredScripts(\n      renderOpts.buildManifest,\n      ctx.assetPrefix,\n      renderOpts.crossOrigin,\n      renderOpts.subresourceIntegrityManifest,\n      getAssetQueryString(ctx, false),\n      ctx.nonce,\n      '/_not-found/page'\n    )\n\n    const prerenderLegacyStore: PrerenderStore = (prerenderStore = {\n      type: 'prerender-legacy',\n      phase: 'render',\n      rootParams,\n      implicitTags: implicitTags,\n      revalidate:\n        typeof prerenderStore?.revalidate !== 'undefined'\n          ? prerenderStore.revalidate\n          : INFINITE_CACHE,\n      expire:\n        typeof prerenderStore?.expire !== 'undefined'\n          ? prerenderStore.expire\n          : INFINITE_CACHE,\n      stale:\n        typeof prerenderStore?.stale !== 'undefined'\n          ? prerenderStore.stale\n          : INFINITE_CACHE,\n      tags: [...(prerenderStore?.tags || implicitTags)],\n    })\n    const errorRSCPayload = await workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      getErrorRSCPayload,\n      tree,\n      ctx,\n      reactServerErrorsByDigest.has((err as any).digest) ? undefined : err,\n      errorType\n    )\n\n    const errorServerStream = workUnitAsyncStorage.run(\n      prerenderLegacyStore,\n      ComponentMod.renderToReadableStream,\n      errorRSCPayload,\n      clientReferenceManifest.clientModules,\n      {\n        onError: serverComponentsErrorHandler,\n      }\n    )\n\n    try {\n      const fizzStream = await renderToInitialFizzStream({\n        ReactDOMServer: require('react-dom/server.edge'),\n        element: (\n          <AppWithoutContext\n            reactServerStream={errorServerStream}\n            preinitScripts={errorPreinitScripts}\n            clientReferenceManifest={clientReferenceManifest}\n            nonce={ctx.nonce}\n          />\n        ),\n        streamOptions: {\n          nonce: ctx.nonce,\n          // Include hydration scripts in the HTML\n          bootstrapScripts: [errorBootstrapScript],\n          formState,\n        },\n      })\n\n      if (shouldGenerateStaticFlightData(workStore)) {\n        const flightData = await streamToBuffer(\n          reactServerPrerenderResult.asStream()\n        )\n        metadata.flightData = flightData\n        metadata.segmentData = await collectSegmentData(\n          flightData,\n          prerenderLegacyStore,\n          ComponentMod,\n          renderOpts,\n          fallbackRouteParams\n        )\n      }\n\n      const validateRootLayout = renderOpts.dev\n\n      // This is intentionally using the readable datastream from the main\n      // render rather than the flight data from the error page render\n      const flightStream =\n        reactServerPrerenderResult instanceof ServerPrerenderStreamResult\n          ? reactServerPrerenderResult.asStream()\n          : reactServerPrerenderResult.consumeAsStream()\n\n      return {\n        // Returning the error that was thrown so it can be used to handle\n        // the response in the caller.\n        digestErrorsMap: reactServerErrorsByDigest,\n        ssrErrors: allCapturedErrors,\n        stream: await continueFizzStream(fizzStream, {\n          inlinedDataStream: createInlinedDataReadableStream(\n            flightStream,\n            ctx.nonce,\n            formState\n          ),\n          isStaticGeneration: true,\n          getServerInsertedHTML: makeGetServerInsertedHTML({\n            polyfills,\n            renderServerInsertedHTML,\n            serverCapturedErrors: [],\n            basePath: renderOpts.basePath,\n            tracingMetadata: tracingMetadata,\n          }),\n          getServerInsertedMetadata,\n          validateRootLayout,\n        }),\n        dynamicAccess: null,\n        collectedRevalidate:\n          prerenderStore !== null ? prerenderStore.revalidate : INFINITE_CACHE,\n        collectedExpire:\n          prerenderStore !== null ? prerenderStore.expire : INFINITE_CACHE,\n        collectedStale:\n          prerenderStore !== null ? prerenderStore.stale : INFINITE_CACHE,\n        collectedTags: prerenderStore !== null ? prerenderStore.tags : null,\n      }\n    } catch (finalErr: any) {\n      if (\n        process.env.NODE_ENV === 'development' &&\n        isHTTPAccessFallbackError(finalErr)\n      ) {\n        const { bailOnRootNotFound } =\n          require('../../client/components/dev-root-http-access-fallback-boundary') as typeof import('../../client/components/dev-root-http-access-fallback-boundary')\n        bailOnRootNotFound()\n      }\n      throw finalErr\n    }\n  }\n}\n\nconst loadingChunks: Set<Promise<unknown>> = new Set()\nconst chunkListeners: Array<(x?: unknown) => void> = []\n\nfunction trackChunkLoading(load: Promise<unknown>) {\n  loadingChunks.add(load)\n  load.finally(() => {\n    if (loadingChunks.has(load)) {\n      loadingChunks.delete(load)\n      if (loadingChunks.size === 0) {\n        // We are not currently loading any chunks. We can notify all listeners\n        for (let i = 0; i < chunkListeners.length; i++) {\n          chunkListeners[i]()\n        }\n        chunkListeners.length = 0\n      }\n    }\n  })\n}\n\nexport async function warmFlightResponse(\n  flightStream: ReadableStream<Uint8Array>,\n  clientReferenceManifest: DeepReadonly<ClientReferenceManifest>\n) {\n  let createFromReadableStream\n  if (process.env.TURBOPACK) {\n    createFromReadableStream =\n      // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-turbopack/client.edge').createFromReadableStream\n  } else {\n    createFromReadableStream =\n      // eslint-disable-next-line import/no-extraneous-dependencies\n      require('react-server-dom-webpack/client.edge').createFromReadableStream\n  }\n\n  try {\n    createFromReadableStream(flightStream, {\n      serverConsumerManifest: {\n        moduleLoading: clientReferenceManifest.moduleLoading,\n        moduleMap: clientReferenceManifest.ssrModuleMapping,\n        serverModuleMap: null,\n      },\n    })\n  } catch {\n    // We don't want to handle errors here but we don't want it to\n    // interrupt the outer flow. We simply ignore it here and expect\n    // it will bubble up during a render\n  }\n\n  // We'll wait at least one task and then if no chunks have started to load\n  // we'll we can infer that there are none to load from this flight response\n  trackChunkLoading(waitAtLeastOneReactRenderTask())\n  return new Promise((r) => {\n    chunkListeners.push(r)\n  })\n}\n\nconst getGlobalErrorStyles = async (\n  tree: LoaderTree,\n  ctx: AppRenderContext\n): Promise<React.ReactNode | undefined> => {\n  const {\n    modules: { 'global-error': globalErrorModule },\n  } = parseLoaderTree(tree)\n\n  let globalErrorStyles\n  if (globalErrorModule) {\n    const [, styles] = await createComponentStylesAndScripts({\n      ctx,\n      filePath: globalErrorModule[1],\n      getComponent: globalErrorModule[0],\n      injectedCSS: new Set(),\n      injectedJS: new Set(),\n    })\n    globalErrorStyles = styles\n  }\n\n  return globalErrorStyles\n}\n\nasync function collectSegmentData(\n  fullPageDataBuffer: Buffer,\n  prerenderStore: PrerenderStore,\n  ComponentMod: AppPageModule,\n  renderOpts: RenderOpts,\n  fallbackRouteParams: FallbackRouteParams | null\n): Promise<Map<string, Buffer> | undefined> {\n  // Per-segment prefetch data\n  //\n  // All of the segments for a page are generated simultaneously, including\n  // during revalidations. This is to ensure consistency, because it's\n  // possible for a mismatch between a layout and page segment can cause the\n  // client to error during rendering. We want to preserve the ability of the\n  // client to recover from such a mismatch by re-requesting all the segments\n  // to get a consistent view of the page.\n  //\n  // For performance, we reuse the Flight output that was created when\n  // generating the initial page HTML. The Flight stream for the whole page is\n  // decomposed into a separate stream per segment.\n\n  const clientReferenceManifest = renderOpts.clientReferenceManifest\n  if (!clientReferenceManifest || !renderOpts.experimental.clientSegmentCache) {\n    return\n  }\n\n  // Manifest passed to the Flight client for reading the full-page Flight\n  // stream. Based off similar code in use-cache-wrapper.ts.\n  const isEdgeRuntime = process.env.NEXT_RUNTIME === 'edge'\n  const serverConsumerManifest = {\n    // moduleLoading must be null because we don't want to trigger preloads of ClientReferences\n    // to be added to the consumer. Instead, we'll wait for any ClientReference to be emitted\n    // which themselves will handle the preloading.\n    moduleLoading: null,\n    moduleMap: isEdgeRuntime\n      ? clientReferenceManifest.edgeRscModuleMapping\n      : clientReferenceManifest.rscModuleMapping,\n    serverModuleMap: null,\n  }\n\n  // When dynamicIO is enabled, missing data is encoded to an infinitely hanging\n  // promise, the absence of which we use to determine if a segment is fully\n  // static or partially static. However, when dynamicIO is not enabled, this\n  // trick doesn't work.\n  //\n  // So if PPR is enabled, and dynamicIO is not, we have to be conservative and\n  // assume all segments are partial.\n  //\n  // TODO: When PPR is on, we can at least optimize the case where the entire\n  // page is static. Either by passing that as an argument to this function, or\n  // by setting a header on the response like the we do for full page RSC\n  // prefetches today. The latter approach might be simpler since it requires\n  // less plumbing, and the client has to check the header regardless to see if\n  // PPR is enabled.\n  const shouldAssumePartialData =\n    renderOpts.experimental.isRoutePPREnabled === true && // PPR is enabled\n    !renderOpts.experimental.dynamicIO // dynamicIO is disabled\n\n  const staleTime = prerenderStore.stale\n  return await ComponentMod.collectSegmentData(\n    shouldAssumePartialData,\n    fullPageDataBuffer,\n    staleTime,\n    clientReferenceManifest.clientModules as ManifestNode,\n    serverConsumerManifest,\n    fallbackRouteParams\n  )\n}\n"], "names": ["workAsyncStorage", "React", "RenderResult", "chainStreams", "renderToInitialFizzStream", "createDocumentClosingStream", "continueFizzStream", "continueDynamicPrerender", "continueStaticP<PERSON><PERSON>", "continueDynamicHTMLResume", "streamToBuffer", "streamToString", "stripInternalQueries", "NEXT_HMR_REFRESH_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_URL", "RSC_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "createTrackedMetadataContext", "createMetadataContext", "createRequestStoreForRender", "createWorkStore", "getAccessFallbackErrorTypeByStatus", "getAccessFallbackHTTPStatus", "isHTTPAccessFallbackError", "getURLFromRedirectError", "getRedirectStatusCodeFromError", "isRedirectError", "getImplicitTags", "AppRenderSpan", "NextNodeServerSpan", "getTracer", "FlightRenderResult", "createFlightReactServerErrorHandler", "createHTMLReactServerErrorHandler", "createHTMLErrorHandler", "isUserLandError", "getDigestForWellKnownError", "getShortDynamicParamType", "dynamicParamTypes", "getSegmentParam", "getScriptNonceFromHeader", "parseAndValidateFlightRouterState", "createFlightRouterStateFromLoaderTree", "handleAction", "isBailoutToCSRError", "warn", "error", "appendMutableCookies", "createServerInsertedHTML", "getRequiredScripts", "addPathPrefix", "makeGetServerInsertedHTML", "walkTreeWithFlightRouterState", "createComponentTree", "getRootParams", "getAssetQueryString", "setReferenceManifestsSingleton", "DynamicState", "parsePostponedState", "getDynamicDataPostponedState", "getDynamicHTMLPostponedState", "getPostponedFromState", "isDynamicServerError", "useFlightStream", "createInlinedDataReadableStream", "StaticGenBailoutError", "isStaticGenBailoutError", "getStackWithoutErrorMessage", "accessedDynamicData", "createPostponedAbortSignal", "formatDynamicAPIAccesses", "isPrerenderInterruptedError", "createDynamicTrackingState", "createDynamicValidationState", "getFirstDynamicReason", "trackAllowedDynamicAccess", "throwIfDisallowedDynamic", "consumeDynamicAccess", "getClientComponentLoaderMetrics", "wrapClientComponentLoader", "createServerModuleMap", "isNodeNextRequest", "parseParameter", "parseRelativeUrl", "AppRouter", "getServerActionRequestMetadata", "createInitialRouterState", "createMutableActionQueue", "getRevalidateReason", "PAGE_SEGMENT_KEY", "DynamicServerError", "ServerPrerenderStreamResult", "ReactServerResult", "createReactServerPrerenderResult", "createReactServerPrerenderResultFromRender", "prerenderAndAbortInSequentialTasks", "prerenderServerWithPhases", "prerenderClientWithPhases", "printDebugThrownValueForProspectiveRender", "scheduleInSequentialTasks", "waitAtLeastOneReactRenderTask", "workUnitAsyncStorage", "CacheSignal", "getTracedMetadata", "InvariantError", "INFINITE_CACHE", "createComponentStylesAndScripts", "parseLoaderTree", "createPrerenderResumeDataCache", "createRenderResumeDataCache", "isError", "isUseCacheTimeoutError", "createServerInsertedMetadata", "flightDataPathHeadKey", "parseRequestHeaders", "headers", "options", "isDevWarmupRequest", "isDevWarmup", "isPrefetchRequest", "toLowerCase", "undefined", "isHmrRefresh", "isRSCRequest", "shouldProvideFlightRouterState", "isRoutePPREnabled", "flightRouterState", "isRouteTreePrefetchRequest", "csp", "nonce", "createNotFoundLoaderTree", "loaderTree", "components", "children", "page", "createDivergedMetadataComponents", "<PERSON><PERSON><PERSON>", "serveStreamingMetadata", "EmptyMetadata", "StreamingMetadata", "StaticMetadata", "makeGetDynamicParamFromSegment", "params", "pagePath", "fallbackRouteParams", "getDynamicParamFromSegment", "segment", "segmentParam", "key", "param", "value", "has", "get", "Array", "isArray", "map", "i", "encodeURIComponent", "isCatchall", "type", "isOptionalCatchall", "dynamicParamType", "treeSegment", "split", "slice", "flatMap", "pathSegment", "join", "NonIndex", "ctx", "is404Page", "isInvalidStatusCode", "res", "statusCode", "isAction", "meta", "name", "content", "generateDynamicRSCPayload", "flightData", "componentMod", "tree", "createServerSearchParamsForMetadata", "createServerParamsForMetadata", "createMetadataComponents", "MetadataBoundary", "ViewportBoundary", "appUsingSizeAdjustment", "query", "requestId", "workStore", "url", "renderOpts", "skipFlight", "preloadCallbacks", "searchParams", "ViewportTree", "MetadataTree", "getViewportReady", "getMetadataReady", "StreamingMetadataOutlet", "metadataContext", "pathname", "loaderTreeToFilter", "parentParams", "rscHead", "Fragment", "injectedCSS", "Set", "injectedJS", "injectedFontPreloadTags", "rootLayoutIncluded", "path", "actionResult", "a", "f", "b", "sharedContext", "buildId", "S", "isStaticGeneration", "createErrorContext", "renderSource", "routerKind", "routePath", "routeType", "revalidateReason", "generateDynamicFlightRenderResult", "req", "requestStore", "onFlightDataRenderError", "err", "onInstrumentationRequestError", "onError", "dev", "RSCPayload", "run", "process", "env", "NODE_ENV", "experimental", "dynamicIO", "resolveValidation", "validationOutlet", "createValidationOutlet", "_validation", "spawnDynamicValidationInDev", "clientReferenceManifest", "route", "flightReadableStream", "renderToReadableStream", "clientModules", "temporaryReferences", "fetchMetrics", "warmupDevRender", "rootParams", "prerenderResumeDataCache", "renderController", "AbortController", "prerenderController", "cacheSignal", "prerenderStore", "phase", "implicitTags", "renderSignal", "signal", "controller", "dynamicTracking", "revalidate", "expire", "stale", "tags", "rscPayload", "cacheReady", "abort", "devRenderResumeDataCache", "prepareInitialCanonicalUrl", "search", "getRSCPayload", "is404", "missingSlots", "GlobalError", "initialTree", "errorType", "seedData", "authInterrupts", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "couldBeIntercepted", "includes", "initialHead", "globalErrorStyles", "getGlobalErrorStyles", "isPossiblyPartialHead", "P", "Preloads", "p", "assetPrefix", "c", "m", "G", "s", "postponed", "for<PERSON>ach", "preloadFn", "getErrorRSCPayload", "ssrError", "Error", "html", "id", "head", "body", "template", "data-next-error-message", "message", "data-next-error-digest", "digest", "data-next-error-stack", "stack", "App", "reactServerStream", "preinitScripts", "ServerInsertedHTMLProvider", "ServerInsertedMetadataProvider", "response", "use", "initialState", "initialFlightData", "initialCanonicalUrlParts", "initialParallelRoutes", "Map", "location", "prerendered", "actionQueue", "HeadManagerContext", "require", "Provider", "appDir", "globalErrorComponentAndStyles", "AppWithoutContext", "renderToHTMLOrFlightImpl", "parsedRequestHeaders", "requestEndedState", "postponedState", "serverComponentsHmrCache", "isNotFoundPath", "requestTimestamp", "Date", "now", "serverActionsManifest", "ComponentMod", "nextFontManifest", "serverActions", "enableTainting", "__next_app__", "instrumented", "globalThis", "__next_require__", "__next_chunk_load__", "args", "loadingChunk", "loadChunk", "trackChunkLoading", "URL", "setIsrStatus", "NEXT_RUNTIME", "originalRequest", "on", "ended", "metrics", "reset", "startSpan", "clientComponentLoading", "startTime", "clientComponentLoadStart", "attributes", "clientComponentLoadCount", "end", "clientComponentLoadTimes", "metadata", "appUsingSizeAdjust", "serverModuleMap", "patchFetch", "taintObjectReference", "crypto", "randomUUID", "nanoid", "isActionRequest", "isServerAction", "isPrefetch", "setRootSpanAttribute", "prerenderToStreamWithTracing", "wrap", "getBodyResult", "spanName", "prerenderToStream", "dynamicAccess", "isDebugDynamicAccesses", "access", "digestErrorsMap", "size", "buildFailingError", "values", "next", "ssrErrors", "length", "find", "pendingRevalidates", "pendingRevalidateWrites", "revalidatedTags", "pendingPromise", "Promise", "all", "incrementalCache", "revalidateTag", "Object", "finally", "NEXT_PRIVATE_DEBUG_CACHE", "console", "log", "waitUntil", "collectedTags", "fetchTags", "staleHeader", "String", "collectedStale", "<PERSON><PERSON><PERSON><PERSON>", "forceStatic", "collectedRevalidate", "cacheControl", "collectedExpire", "staticBailoutInfo", "description", "dynamicUsageDescription", "dynamicUsageStack", "stream", "renderResumeDataCache", "onUpdateCookies", "previewProps", "usedDynamic", "forceDynamic", "renderToStreamWithTracing", "renderToStream", "formState", "actionRequestResult", "generateFlight", "notFoundLoaderTree", "result", "assignMetadata", "renderToHTMLOrFlight", "routeModule", "definition", "renderServerInsertedHTML", "getServerInsertedMetadata", "tracingMetadata", "getTracePropagationData", "clientTraceMetadata", "polyfills", "buildManifest", "polyfillFiles", "filter", "polyfill", "endsWith", "src", "integrity", "subresourceIntegrityManifest", "crossOrigin", "noModule", "bootstrapScript", "reactServerErrorsByDigest", "silenceLogger", "onHTMLRenderRSCError", "serverComponentsErrorHandler", "nextExport", "onHTMLRenderSSRError", "allCapturedErrors", "htmlRendererErrorHandler", "reactServerResult", "bind", "append<PERSON><PERSON>er", "prerenderPhase", "environmentName", "filterStackFrame", "_functionName", "startsWith", "DATA", "inlinedReactServerDataStream", "tee", "resume", "htmlStream", "getServerInsertedHTML", "serverCapturedErrors", "basePath", "inlinedDataStream", "consume", "onHeaders", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reactMaxHeadersLength", "bootstrapScripts", "generateStaticHTML", "supportsDynamicResponse", "shouldWaitOnAllReady", "validateRootLayout", "shouldBailoutToCSR", "reason", "redirectUrl", "Headers", "mutableCookies", "from", "errorPreinitScripts", "errorBootstrapScript", "errorRSCPayload", "errorServerStream", "fizzStream", "ReactDOMServer", "element", "streamOptions", "finalErr", "bailOnRootNotFound", "outlet", "resolve", "isNotFound", "initialServerPrerenderController", "initialServerRenderController", "initialServerPrerenderStore", "initialClientController", "initialClientPrerenderStore", "firstAttemptRSCPayload", "initialServerStream", "aborted", "NEXT_DEBUG_BUILD", "__NEXT_VERBOSE_LOGGING", "warmupStream", "renderStream", "warmFlightResponse", "prerender", "pendingInitialClientResult", "catch", "finalServerController", "serverDynamicTracking", "finalServerPrerenderStore", "finalClientController", "clientDynamicTracking", "dynamicValidation", "finalClientPrerenderStore", "finalServerPayload", "serverPrerenderStreamResult", "rootDidError", "serverPhasedStream", "asPhasedStream", "errorInfo", "dynamicErrors", "push", "componentStack", "assertExhausted", "LogDynamicValidation", "shouldGenerateStaticFlightData", "reactServerPrerenderResult", "setMetadataHeader", "item", "initialServerPayload", "pendingInitialServerResult", "onPostpone", "initialServerResult", "asStream", "asUnclosingStream", "serverIsDynamic", "finalRenderPrerenderStore", "finalAttemptRSCPayload", "prerenderIsPending", "prerenderResult", "clientIsDynamic", "prelude", "segmentData", "collectSegmentData", "foreverStream", "ReadableStream", "resumeStream", "JSON", "parse", "stringify", "consumeAsStream", "cache", "dynamicReason", "reactServerPrerenderStore", "ssrPrerenderStore", "dynamicAccesses", "prerenderLegacyStore", "flightStream", "loadingChunks", "chunkListeners", "load", "add", "delete", "createFromReadableStream", "TURBOPACK", "serverConsumerManifest", "moduleLoading", "moduleMap", "ssrModuleMapping", "r", "modules", "globalErrorModule", "styles", "filePath", "getComponent", "fullPageDataBuffer", "clientSegmentCache", "isEdgeRuntime", "edgeRscModuleMapping", "rscModuleMapping", "shouldAssumePartialData", "staleTime"], "mappings": ";AAaA,SACEA,gBAAgB,QAEX,4CAA2C;AAalD,OAAOC,WAAyC,QAAO;AAEvD,OAAOC,kBAGA,mBAAkB;AACzB,SACEC,YAAY,EACZC,yBAAyB,EACzBC,2BAA2B,EAC3BC,kBAAkB,EAClBC,wBAAwB,EACxBC,uBAAuB,EACvBC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,QACT,0CAAyC;AAChD,SAASC,oBAAoB,QAAQ,oBAAmB;AACxD,SACEC,uBAAuB,EACvBC,2BAA2B,EAC3BC,6BAA6B,EAC7BC,6BAA6B,EAC7BC,QAAQ,EACRC,UAAU,EACVC,mCAAmC,QAC9B,6CAA4C;AACnD,SACEC,4BAA4B,EAC5BC,qBAAqB,QAChB,sCAAqC;AAC5C,SAASC,2BAA2B,QAAQ,iCAAgC;AAC5E,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SACEC,kCAAkC,EAClCC,2BAA2B,EAC3BC,yBAAyB,QACpB,oEAAmE;AAC1E,SACEC,uBAAuB,EACvBC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,eAAe,QAAQ,uBAAsB;AACtD,SAASC,aAAa,EAAEC,kBAAkB,QAAQ,yBAAwB;AAC1E,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SACEC,mCAAmC,EACnCC,iCAAiC,EACjCC,sBAAsB,EAEtBC,eAAe,EACfC,0BAA0B,QACrB,yBAAwB;AAC/B,SACEC,wBAAwB,EACxBC,iBAAiB,QACZ,iCAAgC;AACvC,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,iCAAiC,QAAQ,2CAA0C;AAC5F,SAASC,qCAAqC,QAAQ,gDAA+C;AACrG,SAASC,YAAY,QAAQ,mBAAkB;AAC/C,SAASC,mBAAmB,QAAQ,+CAA8C;AAClF,SAASC,IAAI,EAAEC,KAAK,QAAQ,yBAAwB;AACpD,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,wBAAwB,QAAQ,yBAAwB;AACjE,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,aAAa,QAAQ,gDAA+C;AAC7E,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,6BAA6B,QAAQ,uCAAsC;AACpF,SAASC,mBAAmB,EAAEC,aAAa,QAAQ,0BAAyB;AAC5E,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,8BAA8B,QAAQ,qBAAoB;AACnE,SACEC,YAAY,EAEZC,mBAAmB,QACd,oBAAmB;AAC1B,SACEC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,qBAAqB,QAChB,oBAAmB;AAC1B,SAASC,oBAAoB,QAAQ,+CAA8C;AACnF,SACEC,eAAe,EACfC,+BAA+B,QAC1B,wBAAuB;AAC9B,SACEC,qBAAqB,EACrBC,uBAAuB,QAClB,oDAAmD;AAC1D,SAASC,2BAA2B,QAAQ,gCAA+B;AAC3E,SACEC,mBAAmB,EACnBC,0BAA0B,EAC1BC,wBAAwB,EACxBC,2BAA2B,EAC3BC,0BAA0B,EAC1BC,4BAA4B,EAC5BC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,QAEf,sBAAqB;AAC5B,SACEC,+BAA+B,EAC/BC,yBAAyB,QACpB,sCAAqC;AAC5C,SAASC,qBAAqB,QAAQ,iBAAgB;AACtD,SAASC,iBAAiB,QAAQ,uBAAsB;AACxD,SAASC,cAAc,QAAQ,4CAA2C;AAC1E,SAASC,gBAAgB,QAAQ,mDAAkD;AACnF,OAAOC,eAAe,qCAAoC;AAG1D,SAASC,8BAA8B,QAAQ,oCAAmC;AAClF,SAASC,wBAAwB,QAAQ,qEAAoE;AAC7G,SAASC,wBAAwB,QAAQ,uCAAsC;AAC/E,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,gBAAgB,QAAQ,2BAA0B;AAE3D,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,2BAA2B,QAAQ,+BAA8B;AAC1E,SAEEC,iBAAiB,EACjBC,gCAAgC,EAChCC,0CAA0C,EAC1CC,kCAAkC,EAClCC,yBAAyB,EACzBC,yBAAyB,QACpB,+BAA8B;AACrC,SAASC,yCAAyC,QAAQ,6BAA4B;AACtF,SAASC,yBAAyB,QAAQ,4BAA2B;AACrE,SAASC,6BAA6B,QAAQ,sBAAqB;AACnE,SACEC,oBAAoB,QAEf,qCAAoC;AAC3C,SAASC,WAAW,QAAQ,iBAAgB;AAC5C,SAASC,iBAAiB,QAAQ,qBAAoB;AACtD,SAASC,cAAc,QAAQ,mCAAkC;AAEjE,OAAO,kCAAiC;AACxC,SAASC,cAAc,QAAQ,sBAAqB;AACpD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,eAAe,QAAQ,sBAAqB;AACrD,SACEC,8BAA8B,EAC9BC,2BAA2B,QACtB,yCAAwC;AAE/C,OAAOC,aAAa,qBAAoB;AACxC,SAASC,sBAAsB,QAAQ,gCAA+B;AACtE,SAASC,4BAA4B,QAAQ,uDAAsD;AA8CnG,MAAMC,wBAAwB;AAkB9B,SAASC,oBACPC,OAA4B,EAC5BC,OAAmC;IAEnC,MAAMC,qBAAqBD,QAAQE,WAAW,KAAK;IAEnD,2DAA2D;IAC3D,MAAMC,oBACJF,sBACAF,OAAO,CAACxG,4BAA4B6G,WAAW,GAAG,KAAKC;IAEzD,MAAMC,eACJP,OAAO,CAACzG,wBAAwB8G,WAAW,GAAG,KAAKC;IAErD,2DAA2D;IAC3D,MAAME,eACJN,sBAAsBF,OAAO,CAACpG,WAAWyG,WAAW,GAAG,KAAKC;IAE9D,MAAMG,iCACJD,gBAAiB,CAAA,CAACJ,qBAAqB,CAACH,QAAQS,iBAAiB,AAAD;IAElE,MAAMC,oBAAoBF,iCACtBnF,kCACE0E,OAAO,CAACvG,8BAA8B4G,WAAW,GAAG,IAEtDC;IAEJ,sEAAsE;IACtE,MAAMM,6BACJZ,OAAO,CAACnG,oCAAoCwG,WAAW,GAAG,KAAK;IAEjE,MAAMQ,MACJb,OAAO,CAAC,0BAA0B,IAClCA,OAAO,CAAC,sCAAsC;IAEhD,MAAMc,QACJ,OAAOD,QAAQ,WAAWxF,yBAAyBwF,OAAOP;IAE5D,OAAO;QACLK;QACAP;QACAQ;QACAL;QACAC;QACAN;QACAY;IACF;AACF;AAEA,SAASC,yBAAyBC,UAAsB;IACtD,mEAAmE;IACnE,MAAMC,aAAaD,UAAU,CAAC,EAAE;IAChC,OAAO;QACL;QACA;YACEE,UAAU;gBACR5C;gBACA,CAAC;gBACD;oBACE6C,MAAMF,UAAU,CAAC,YAAY;gBAC/B;aACD;QACH;QACAA;KACD;AACH;AAEA,SAASG,iCACPC,QAA6B,EAC7BC,sBAA+B;IAK/B,SAASC;QACP,OAAO;IACT;IACA,MAAMC,oBAAgDF,yBAClDD,WACA;IAEJ,MAAMI,iBAA0CH,yBAC5CC,gBACAF;IAEJ,OAAO;QACLI;QACAD;IACF;AACF;AAEA;;CAEC,GACD,SAASE,+BACPC,MAA8B,EAC9BC,QAAgB,EAChBC,mBAA+C;IAE/C,OAAO,SAASC,2BACd,gCAAgC;IAChCC,OAAe;QAEf,MAAMC,eAAe5G,gBAAgB2G;QACrC,IAAI,CAACC,cAAc;YACjB,OAAO;QACT;QAEA,MAAMC,MAAMD,aAAaE,KAAK;QAE9B,IAAIC,QAAQR,MAAM,CAACM,IAAI;QAEvB,IAAIJ,uBAAuBA,oBAAoBO,GAAG,CAACJ,aAAaE,KAAK,GAAG;YACtEC,QAAQN,oBAAoBQ,GAAG,CAACL,aAAaE,KAAK;QACpD,OAAO,IAAII,MAAMC,OAAO,CAACJ,QAAQ;YAC/BA,QAAQA,MAAMK,GAAG,CAAC,CAACC,IAAMC,mBAAmBD;QAC9C,OAAO,IAAI,OAAON,UAAU,UAAU;YACpCA,QAAQO,mBAAmBP;QAC7B;QAEA,IAAI,CAACA,OAAO;YACV,MAAMQ,aAAaX,aAAaY,IAAI,KAAK;YACzC,MAAMC,qBAAqBb,aAAaY,IAAI,KAAK;YAEjD,IAAID,cAAcE,oBAAoB;gBACpC,MAAMC,mBAAmB3H,iBAAiB,CAAC6G,aAAaY,IAAI,CAAC;gBAC7D,oEAAoE;gBACpE,6DAA6D;gBAC7D,IAAIC,oBAAoB;oBACtB,OAAO;wBACLX,OAAOD;wBACPE,OAAO;wBACPS,MAAME;wBACNC,aAAa;4BAACd;4BAAK;4BAAIa;yBAAiB;oBAC1C;gBACF;gBAEA,+EAA+E;gBAC/E,wFAAwF;gBACxFX,QAAQP,SACLoB,KAAK,CAAC,IACP,gCAAgC;iBAC/BC,KAAK,CAAC,EACP,oDAAoD;iBACnDC,OAAO,CAAC,CAACC;oBACR,MAAMjB,QAAQnE,eAAeoF;oBAC7B,yDAAyD;oBACzD,wDAAwD;oBACxD,OAAOxB,MAAM,CAACO,MAAMD,GAAG,CAAC,IAAIC,MAAMD,GAAG;gBACvC;gBAEF,OAAO;oBACLC,OAAOD;oBACPE;oBACAS,MAAME;oBACN,wCAAwC;oBACxCC,aAAa;wBAACd;wBAAKE,MAAMiB,IAAI,CAAC;wBAAMN;qBAAiB;gBACvD;YACF;QACF;QAEA,MAAMF,OAAO1H,yBAAyB8G,aAAaY,IAAI;QAEvD,OAAO;YACLV,OAAOD;YACP,yCAAyC;YACzCE,OAAOA;YACP,iDAAiD;YACjDY,aAAa;gBAACd;gBAAKK,MAAMC,OAAO,CAACJ,SAASA,MAAMiB,IAAI,CAAC,OAAOjB;gBAAOS;aAAK;YACxEA,MAAMA;QACR;IACF;AACF;AAEA,SAASS,SAAS,EAAEC,GAAG,EAA6B;IAClD,MAAMC,YAAYD,IAAI1B,QAAQ,KAAK;IACnC,MAAM4B,sBACJ,OAAOF,IAAIG,GAAG,CAACC,UAAU,KAAK,YAAYJ,IAAIG,GAAG,CAACC,UAAU,GAAG;IAEjE,gEAAgE;IAChE,IAAI,CAACJ,IAAIK,QAAQ,IAAKJ,CAAAA,aAAaC,mBAAkB,GAAI;QACvD,qBAAO,KAACI;YAAKC,MAAK;YAASC,SAAQ;;IACrC;IACA,OAAO;AACT;AAEA;;;;CAIC,GACD,eAAeC,0BACbT,GAAqB,EACrBrD,OAGC;IAED,yDAAyD;IACzD,0GAA0G;IAE1G,gGAAgG;IAChG,mGAAmG;IACnG,0GAA0G;IAC1G,mFAAmF;IACnF,IAAI+D,aAAyB;IAE7B,MAAM,EACJC,cAAc,EACZC,MAAMlD,UAAU,EAChBmD,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDzC,0BAA0B,EAC1B0C,sBAAsB,EACtBC,KAAK,EACLC,SAAS,EACT/D,iBAAiB,EACjBgE,SAAS,EACTC,GAAG,EACJ,GAAGtB;IAEJ,MAAMhC,yBAAyB,CAAC,CAACgC,IAAIuB,UAAU,CAACvD,sBAAsB;IAEtE,IAAI,EAACrB,2BAAAA,QAAS6E,UAAU,GAAE;QACxB,MAAMC,mBAAqC,EAAE;QAE7C,MAAMC,eAAeb,oCAAoCM,OAAOE;QAChE,MAAM,EACJM,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGhB,yBAAyB;YAC3BH,MAAMlD;YACNgE;YACAM,iBAAiBxL,6BACf8K,IAAIW,QAAQ,EACZjC,IAAIuB,UAAU,EACdF;YAEF7C;YACA0C;YACAJ;YACAO;YACAL;YACAC;YACAjD;QACF;QAEA,MAAM,EAAEE,iBAAiB,EAAEC,cAAc,EAAE,GACzCL,iCAAiC;YAC/B,OACE,yEAAyE;0BACzE,KAAC8D,kBAAkBR;QAEvB,GAAGpD;QAEL0C,aAAa,AACX,CAAA,MAAM/H,8BAA8B;YAClCqH;YACAkC,oBAAoBxE;YACpByE,cAAc,CAAC;YACf9E;YACA,+CAA+C;YAC/C+E,uBACE,MAAC/M,MAAMgN,QAAQ;;kCAEb,KAACtC;wBAASC,KAAKA;;kCAEf,KAAC2B,kBAAkBP;oBAClBlD,kCAAoB,KAACA,yBAAuB;kCAC7C,KAACC;;eANkB3B;YASvB8F,aAAa,IAAIC;YACjBC,YAAY,IAAID;YAChBE,yBAAyB,IAAIF;YAC7BG,oBAAoB;YACpBb;YACAC;YACAL;YACAM;QACF,EAAC,EACD7C,GAAG,CAAC,CAACyD,OAASA,KAAKhD,KAAK,CAAC,IAAI,+BAA+B;;IAChE;IAEA,sEAAsE;IACtE,+EAA+E;IAC/E,wBAAwB;IACxB,IAAIhD,2BAAAA,QAASiG,YAAY,EAAE;QACzB,OAAO;YACLC,GAAGlG,QAAQiG,YAAY;YACvBE,GAAGpC;YACHqC,GAAG/C,IAAIgD,aAAa,CAACC,OAAO;QAC9B;IACF;IAEA,0CAA0C;IAC1C,OAAO;QACLF,GAAG/C,IAAIgD,aAAa,CAACC,OAAO;QAC5BH,GAAGpC;QACHwC,GAAG7B,UAAU8B,kBAAkB;IACjC;AACF;AAEA,SAASC,mBACPpD,GAAqB,EACrBqD,YAAiD;IAEjD,OAAO;QACLC,YAAY;QACZC,WAAWvD,IAAI1B,QAAQ;QACvBkF,WAAWxD,IAAIK,QAAQ,GAAG,WAAW;QACrCgD;QACAI,kBAAkB1I,oBAAoBiF,IAAIqB,SAAS;IACrD;AACF;AACA;;;CAGC,GACD,eAAeqC,kCACbC,GAAoB,EACpB3D,GAAqB,EACrB4D,YAA0B,EAC1BjH,OAMC;IAED,MAAM4E,aAAavB,IAAIuB,UAAU;IAEjC,SAASsC,wBAAwBC,GAAkB;QACjD,OAAOvC,WAAWwC,6BAA6B,oBAAxCxC,WAAWwC,6BAA6B,MAAxCxC,YACLuC,KACAH,KACAP,mBAAmBpD,KAAK;IAE5B;IACA,MAAMgE,UAAUzM,oCACd,CAAC,CAACgK,WAAW0C,GAAG,EAChBJ;IAGF,MAAMK,aAGF,MAAMtI,qBAAqBuI,GAAG,CAChCP,cACAnD,2BACAT,KACArD;IAGF,IACE,qDAAqD;IACrD4E,WAAW0C,GAAG,IACd,uEAAuE;IACvEG,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,yEAAyE;IACzE/C,WAAWgD,YAAY,CAACC,SAAS,EACjC;QACA,MAAM,CAACC,mBAAmBC,iBAAiB,GAAGC;QAC9CT,WAAWU,WAAW,GAAGF;QAEzBG,4BACEJ,mBACAzE,IAAIW,YAAY,CAACC,IAAI,EACrBZ,KACA,OACAA,IAAI8E,uBAAuB,EAC3B9E,IAAIqB,SAAS,CAAC0D,KAAK,EACnBnB;IAEJ;IAEA,0FAA0F;IAC1F,mCAAmC;IACnC,MAAMoB,uBAAuBpJ,qBAAqBuI,GAAG,CACnDP,cACA5D,IAAIW,YAAY,CAACsE,sBAAsB,EACvCf,YACAlE,IAAI8E,uBAAuB,CAACI,aAAa,EACzC;QACElB;QACAmB,mBAAmB,EAAExI,2BAAAA,QAASwI,mBAAmB;IACnD;IAGF,OAAO,IAAI7N,mBAAmB0N,sBAAsB;QAClDI,cAAcpF,IAAIqB,SAAS,CAAC+D,YAAY;IAC1C;AACF;AAEA;;;;;;CAMC,GACD,eAAeC,gBACb1B,GAAoB,EACpB3D,GAAqB;IAErB,MAAMuB,aAAavB,IAAIuB,UAAU;IACjC,IAAI,CAACA,WAAW0C,GAAG,EAAE;QACnB,MAAM,qBAEL,CAFK,IAAIlI,eACR,mFADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAMuJ,aAAazM,cACjBmH,IAAIW,YAAY,CAACC,IAAI,EACrBZ,IAAIxB,0BAA0B;IAGhC,SAASqF,wBAAwBC,GAAkB;QACjD,OAAOvC,WAAWwC,6BAA6B,oBAAxCxC,WAAWwC,6BAA6B,MAAxCxC,YACLuC,KACAH,KACAP,mBAAmBpD,KAAK;IAE5B;IACA,MAAMgE,UAAUzM,oCACd,MACAsM;IAGF,2EAA2E;IAC3E,kBAAkB;IAClB,MAAM0B,2BAA2BpJ;IAEjC,MAAMqJ,mBAAmB,IAAIC;IAC7B,MAAMC,sBAAsB,IAAID;IAChC,MAAME,cAAc,IAAI9J;IAExB,MAAM+J,iBAAiC;QACrCtG,MAAM;QACNuG,OAAO;QACPP;QACAQ,cAAc,EAAE;QAChBC,cAAcP,iBAAiBQ,MAAM;QACrCC,YAAYP;QACZC;QACAO,iBAAiB;QACjBC,YAAYnK;QACZoK,QAAQpK;QACRqK,OAAOrK;QACPsK,MAAM,EAAE;QACRf;IACF;IAEA,MAAMgB,aAAa,MAAM3K,qBAAqBuI,GAAG,CAC/CyB,gBACAnF,2BACAT;IAGF,0FAA0F;IAC1F,mCAAmC;IACnCpE,qBAAqBuI,GAAG,CACtByB,gBACA5F,IAAIW,YAAY,CAACsE,sBAAsB,EACvCsB,YACAvG,IAAI8E,uBAAuB,CAACI,aAAa,EACzC;QACElB;QACAgC,QAAQR,iBAAiBQ,MAAM;IACjC;IAGF,6CAA6C;IAC7C,MAAML,YAAYa,UAAU;IAC5B,uFAAuF;IACvFZ,eAAeL,wBAAwB,GAAG;IAC1C,mBAAmB;IACnBC,iBAAiBiB,KAAK;IAEtB,0EAA0E;IAC1E,+EAA+E;IAC/E,+EAA+E;IAC/E,OAAO,IAAInP,mBAAmB,IAAI;QAChC8N,cAAcpF,IAAIqB,SAAS,CAAC+D,YAAY;QACxCsB,0BAA0BtK,4BACxBmJ;IAEJ;AACF;AAEA;;;;;CAKC,GACD,SAASoB,2BAA2BrF,GAAwB;IAC1D,OAAO,AAACA,CAAAA,IAAIW,QAAQ,GAAGX,IAAIsF,MAAM,AAAD,EAAGlH,KAAK,CAAC;AAC3C;AAEA,wFAAwF;AACxF,eAAemH,cACbjG,IAAgB,EAChBZ,GAAqB,EACrB8G,KAAc;IAEd,MAAMxE,cAAc,IAAIC;IACxB,MAAMC,aAAa,IAAID;IACvB,MAAME,0BAA0B,IAAIF;IACpC,IAAIwE;IAEJ,sDAAsD;IACtD,IAAI3C,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1CyC,eAAe,IAAIxE;IACrB;IAEA,MAAM,EACJ/D,0BAA0B,EAC1B2C,KAAK,EACLD,sBAAsB,EACtBP,cAAc,EACZqG,WAAW,EACXnG,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDK,GAAG,EACHD,SAAS,EACV,GAAGrB;IAEJ,MAAMiH,cAAchP,sCAClB2I,MACApC,4BACA2C;IAEF,MAAMnD,yBAAyB,CAAC,CAACgC,IAAIuB,UAAU,CAACvD,sBAAsB;IAEtE,MAAM0D,eAAeb,oCAAoCM,OAAOE;IAChE,MAAM,EACJM,YAAY,EACZC,YAAY,EACZC,gBAAgB,EAChBC,gBAAgB,EAChBC,uBAAuB,EACxB,GAAGhB,yBAAyB;QAC3BH;QACAsG,WAAWJ,QAAQ,cAAc9J;QACjC0E;QACAM,iBAAiBxL,6BACf8K,IAAIW,QAAQ,EACZjC,IAAIuB,UAAU,EACdF;QAEF7C;QACA0C;QACAJ;QACAO;QACAL;QACAC;QACAjD;IACF;IAEA,MAAMyD,mBAAqC,EAAE;IAE7C,MAAM,EAAEvD,iBAAiB,EAAEC,cAAc,EAAE,GACzCL,iCAAiC;QAC/B,OACE,yGAAyG;sBACzG,KAAC8D;IAEL,GAAG5D;IAEL,MAAMmJ,WAAW,MAAMvO,oBAAoB;QACzCoH;QACAtC,YAAYkD;QACZuB,cAAc,CAAC;QACfG;QACAE;QACAC;QACAC,oBAAoB;QACpBb;QACAC;QACAiF;QACAtF;QACA2F,gBAAgBpH,IAAIuB,UAAU,CAACgD,YAAY,CAAC6C,cAAc;QAC1DlJ;QACA6D;IACF;IAEA,0FAA0F;IAC1F,6FAA6F;IAC7F,2FAA2F;IAC3F,MAAMsF,aAAarH,IAAIG,GAAG,CAACmH,SAAS,CAAC;IACrC,MAAMC,qBACJ,OAAOF,eAAe,YAAYA,WAAWG,QAAQ,CAACnR;IAExD,MAAMoR,4BACJ,MAACpS,MAAMgN,QAAQ;;0BACb,KAACtC;gBAASC,KAAKA;;0BACf,KAAC2B,kBAAkB3B,IAAIoB,SAAS;0BAChC,KAACjD;;OAHkB3B;IAOvB,MAAMkL,oBAAoB,MAAMC,qBAAqB/G,MAAMZ;IAE3D,uEAAuE;IACvE,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,qEAAqE;IACrE,MAAM4H,wBACJvG,UAAU8B,kBAAkB,IAC5BnD,IAAIuB,UAAU,CAACgD,YAAY,CAACnH,iBAAiB,KAAK;IAEpD,OAAO;QACL,6FAA6F;QAC7FyK,iBAAG,KAACC;YAASrG,kBAAkBA;;QAC/BsB,GAAG/C,IAAIgD,aAAa,CAACC,OAAO;QAC5B8E,GAAG/H,IAAIgI,WAAW;QAClBC,GAAGtB,2BAA2BrF;QAC9BnC,GAAG,CAAC,CAACoI;QACLzE,GAAG;YACD;gBACEmE;gBACAE;gBACAM;gBACAG;aACD;SACF;QACDM,GAAGnB;QACHoB,GAAG;YAACnB;YAAaU;SAAkB;QACnCU,GAAG,OAAOpI,IAAIuB,UAAU,CAAC8G,SAAS,KAAK;QACvCnF,GAAG7B,UAAU8B,kBAAkB;IACjC;AACF;AAEA;;;;;CAKC,GACD,SAAS2E,SAAS,EAAErG,gBAAgB,EAAoC;IACtEA,iBAAiB6G,OAAO,CAAC,CAACC,YAAcA;IACxC,OAAO;AACT;AAEA,sFAAsF;AACtF,eAAeC,mBACb5H,IAAgB,EAChBZ,GAAqB,EACrByI,QAAiB,EACjBvB,SAAqD;IAErD,MAAM,EACJ1I,0BAA0B,EAC1B2C,KAAK,EACLD,sBAAsB,EACtBP,cAAc,EACZqG,WAAW,EACXnG,mCAAmC,EACnCC,6BAA6B,EAC7BC,wBAAwB,EACxBC,gBAAgB,EAChBC,gBAAgB,EACjB,EACDK,GAAG,EACHF,SAAS,EACTC,SAAS,EACV,GAAGrB;IAEJ,MAAMhC,yBAAyB,CAAC,CAACgC,IAAIuB,UAAU,CAACvD,sBAAsB;IACtE,MAAM0D,eAAeb,oCAAoCM,OAAOE;IAChE,MAAM,EAAEO,YAAY,EAAED,YAAY,EAAE,GAAGZ,yBAAyB;QAC9DH;QACAc;QACA,yEAAyE;QACzE,iCAAiC;QACjCM,iBAAiBvL,sBAAsB6K,IAAIW,QAAQ,EAAEjC,IAAIuB,UAAU;QACnE2F;QACA1I;QACA0C;QACAJ;QACAO;QACAL;QACAC;QACAjD,wBAAwBA;IAC1B;IAEA,MAAM,EAAEE,iBAAiB,EAAEC,cAAc,EAAE,GACzCL,iCACE,kBACE,KAACzI,MAAMgN,QAAQ;sBAEb,cAAA,KAACT,kBAAkBR;WAFA5E,wBAKvBwB;IAGJ,MAAMyJ,4BACJ,MAACpS,MAAMgN,QAAQ;;0BACb,KAACtC;gBAASC,KAAKA;;0BAEf,KAAC2B,kBAAkBP;YAClBgD,QAAQC,GAAG,CAACC,QAAQ,KAAK,+BACxB,KAAChE;gBAAKC,MAAK;gBAAaC,SAAQ;;0BAElC,KAACrC;;OAPkB3B;IAWvB,MAAMyK,cAAchP,sCAClB2I,MACApC,4BACA2C;IAGF,IAAI2C,MAAyB9G;IAC7B,IAAIyL,UAAU;QACZ3E,MAAMzH,QAAQoM,YAAYA,WAAW,qBAAwB,CAAxB,IAAIC,MAAMD,WAAW,KAArB,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC9D;IAEA,0EAA0E;IAC1E,+CAA+C;IAC/C,MAAMtB,WAA8B;QAClCF,WAAW,CAAC,EAAE;sBACd,MAAC0B;YAAKC,IAAG;;8BACP,KAACC;8BAAM3K,kCAAoB,KAACA,yBAAuB;;8BACnD,KAAC4K;8BACE1E,QAAQC,GAAG,CAACC,QAAQ,KAAK,gBAAgBR,oBACxC,KAACiF;wBACCC,2BAAyBlF,IAAImF,OAAO;wBACpCC,0BAAwB,YAAYpF,MAAMA,IAAIqF,MAAM,GAAG;wBACvDC,yBAAuBtF,IAAIuF,KAAK;yBAEhC;;;;QAGR,CAAC;QACD;QACA;KACD;IAED,MAAM3B,oBAAoB,MAAMC,qBAAqB/G,MAAMZ;IAE3D,MAAM4H,wBACJvG,UAAU8B,kBAAkB,IAC5BnD,IAAIuB,UAAU,CAACgD,YAAY,CAACnH,iBAAiB,KAAK;IAEpD,OAAO;QACL2F,GAAG/C,IAAIgD,aAAa,CAACC,OAAO;QAC5B8E,GAAG/H,IAAIgI,WAAW;QAClBC,GAAGtB,2BAA2BrF;QAC9B4G,GAAGlL;QACHmC,GAAG;QACH2D,GAAG;YACD;gBACEmE;gBACAE;gBACAM;gBACAG;aACD;SACF;QACDO,GAAG;YAACnB;YAAaU;SAAkB;QACnCU,GAAG,OAAOpI,IAAIuB,UAAU,CAAC8G,SAAS,KAAK;QACvCnF,GAAG7B,UAAU8B,kBAAkB;IACjC;AACF;AAEA,mFAAmF;AACnF,SAASmG,IAAO,EACdC,iBAAiB,EACjBC,cAAc,EACd1E,uBAAuB,EACvBtH,KAAK,EACLiM,0BAA0B,EAC1BC,8BAA8B,EAQ/B;IACCF;IACA,MAAMG,WAAWtU,MAAMuU,GAAG,CACxBtQ,gBACEiQ,mBACAzE,yBACAtH;IAIJ,MAAMqM,eAAehP,yBAAyB;QAC5CiP,mBAAmBH,SAAS7G,CAAC;QAC7BiH,0BAA0BJ,SAAS1B,CAAC;QACpC+B,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV3C,oBAAoBoC,SAASxK,CAAC;QAC9BkJ,WAAWsB,SAASvB,CAAC;QACrB+B,aAAaR,SAASzG,CAAC;IACzB;IAEA,MAAMkH,cAActP,yBAAyB+O;IAE7C,MAAM,EAAEQ,kBAAkB,EAAE,GAC1BC,QAAQ;IAEV,qBACE,KAACD,mBAAmBE,QAAQ;QAC1B1L,OAAO;YACL2L,QAAQ;YACRhN;QACF;kBAEA,cAAA,KAACkM;sBACC,cAAA,KAACD;0BACC,cAAA,KAAC9O;oBACCyP,aAAaA;oBACbK,+BAA+Bd,SAASxB,CAAC;oBACzCH,aAAa2B,SAAS5B,CAAC;;;;;AAMnC;AAEA,oGAAoG;AACpG,uGAAuG;AACvG,sBAAsB;AACtB,SAAS2C,kBAAqB,EAC5BnB,iBAAiB,EACjBC,cAAc,EACd1E,uBAAuB,EACvBtH,KAAK,EAMN;IACCgM;IACA,MAAMG,WAAWtU,MAAMuU,GAAG,CACxBtQ,gBACEiQ,mBACAzE,yBACAtH;IAIJ,MAAMqM,eAAehP,yBAAyB;QAC5CiP,mBAAmBH,SAAS7G,CAAC;QAC7BiH,0BAA0BJ,SAAS1B,CAAC;QACpC+B,uBAAuB,IAAIC;QAC3B,gDAAgD;QAChD,+CAA+C;QAC/CC,UAAU;QACV3C,oBAAoBoC,SAASxK,CAAC;QAC9BkJ,WAAWsB,SAASvB,CAAC;QACrB+B,aAAaR,SAASzG,CAAC;IACzB;IAEA,MAAMkH,cAActP,yBAAyB+O;IAE7C,qBACE,KAAClP;QACCyP,aAAaA;QACbK,+BAA+Bd,SAASxB,CAAC;QACzCH,aAAa2B,SAAS5B,CAAC;;AAG7B;AASA,eAAe4C,yBACbhH,GAAoB,EACpBxD,GAAqB,EACrBmB,GAAwC,EACxChD,QAAgB,EAChB6C,KAAyB,EACzBI,UAAsB,EACtBF,SAAoB,EACpBuJ,oBAA0C,EAC1CC,iBAAsC,EACtCC,cAAqC,EACrChF,YAA2B,EAC3BiF,wBAA8D,EAC9D/H,aAA+B;IAE/B,MAAMgI,iBAAiB1M,aAAa;IACpC,IAAI0M,gBAAgB;QAClB7K,IAAIC,UAAU,GAAG;IACnB;IAEA,qEAAqE;IACrE,wEAAwE;IACxE,6EAA6E;IAC7E,+EAA+E;IAC/E,MAAM6K,mBAAmBC,KAAKC,GAAG;IAEjC,MAAM,EACJC,qBAAqB,EACrBC,YAAY,EACZC,gBAAgB,EAChBC,aAAa,EACbvD,cAAc,EAAE,EAChBwD,cAAc,EACf,GAAGjK;IAEJ,2DAA2D;IAC3D,uEAAuE;IACvE,IAAI8J,aAAaI,YAAY,EAAE;QAC7B,MAAMC,eAAepR,0BAA0B+Q;QAC/C,aAAa;QACbM,WAAWC,gBAAgB,GAAGF,aAAapB,OAAO;QAClD,kEAAkE;QAClE,qEAAqE;QACrE,wEAAwE;QACxE,oEAAoE;QACpE,MAAMuB,sBAAqD,CAAC,GAAGC;YAC7D,MAAMC,eAAeL,aAAaM,SAAS,IAAIF;YAC/CG,kBAAkBF;YAClB,OAAOA;QACT;QACA,mBAAmB;QACnBJ,WAAWE,mBAAmB,GAAGA;IACnC;IAEA,IAAIzH,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,uCAAuC;QACvC,MAAM,EAAErC,QAAQ,EAAE,GAAG,IAAIiK,IAAIvI,IAAIrC,GAAG,IAAI,KAAK;QAC7CC,WAAW4K,YAAY,oBAAvB5K,WAAW4K,YAAY,MAAvB5K,YAA0BU,UAAU;IACtC;IAEA,IACE,qEAAqE;IACrE,6DAA6D;IAC7DmC,QAAQC,GAAG,CAAC+H,YAAY,KAAK,UAC7B5R,kBAAkBmJ,MAClB;QACAA,IAAI0I,eAAe,CAACC,EAAE,CAAC,OAAO;YAC5BzB,kBAAkB0B,KAAK,GAAG;YAE1B,IAAI,iBAAiBZ,YAAY;gBAC/B,MAAMa,UAAUnS,gCAAgC;oBAAEoS,OAAO;gBAAK;gBAC9D,IAAID,SAAS;oBACXnV,YACGqV,SAAS,CAACtV,mBAAmBuV,sBAAsB,EAAE;wBACpDC,WAAWJ,QAAQK,wBAAwB;wBAC3CC,YAAY;4BACV,iCACEN,QAAQO,wBAAwB;4BAClC,kBAAkB3V,mBAAmBuV,sBAAsB;wBAC7D;oBACF,GACCK,GAAG,CACFR,QAAQK,wBAAwB,GAC9BL,QAAQS,wBAAwB;gBAExC;YACF;QACF;IACF;IAEA,MAAMC,WAAwC,CAAC;IAE/C,MAAMhM,yBAAyB,CAAC,EAACoK,oCAAAA,iBAAkB6B,kBAAkB;IAErE,4BAA4B;IAC5B,MAAMrI,0BAA0BvD,WAAWuD,uBAAuB;IAElE,MAAMsI,kBAAkB7S,sBAAsB;QAAE6Q;IAAsB;IAEtErS,+BAA+B;QAC7B8E,MAAMwD,UAAUxD,IAAI;QACpBiH;QACAsG;QACAgC;IACF;IAEA/B,aAAagC,UAAU;IAEvB,oDAAoD;IACpD,MAAM,EAAEzM,MAAMlD,UAAU,EAAE4P,oBAAoB,EAAE,GAAGjC;IAEnD,IAAIG,gBAAgB;QAClB8B,qBACE,kFACAlJ,QAAQC,GAAG;IAEf;IAEAhD,UAAU+D,YAAY,GAAG,EAAE;IAC3B8H,SAAS9H,YAAY,GAAG/D,UAAU+D,YAAY;IAE9C,qCAAqC;IACrCjE,QAAQ;QAAE,GAAGA,KAAK;IAAC;IACnBnL,qBAAqBmL;IAErB,MAAM,EACJ9D,iBAAiB,EACjBP,iBAAiB,EACjBI,YAAY,EACZN,kBAAkB,EAClBK,YAAY,EACZO,KAAK,EACN,GAAGoN;IAEJ;;;GAGC,GACD,IAAIxJ;IAEJ,IAAIgD,QAAQC,GAAG,CAAC+H,YAAY,KAAK,QAAQ;QACvChL,YAAYmM,OAAOC,UAAU;IAC/B,OAAO;QACLpM,YAAYkJ,QAAQ,6BAA6BmD,MAAM;IACzD;IAEA;;GAEC,GACD,MAAMpP,SAASkD,WAAWlD,MAAM,IAAI,CAAC;IAErC,MAAM,EAAE8E,kBAAkB,EAAE5E,mBAAmB,EAAE,GAAG8C;IAEpD,MAAM7C,6BAA6BJ,+BACjCC,QACAC,UACAC;IAGF,MAAMmP,kBAAkB9S,+BAA+B+I,KAAKgK,cAAc;IAE1E,MAAM3N,MAAwB;QAC5BW,cAAc0K;QACd/J;QACAC;QACAF;QACAuJ;QACApM;QACA2C;QACAyM,YAAY9Q;QACZuD,UAAUqN;QACVzC;QACA/J;QACA7D;QACA+D;QACA9C;QACAwG;QACAkD;QACAgD;QACAxN;QACA2C;QACA6C;IACF;IAEA3L,YAAYwW,oBAAoB,CAAC,cAAcvP;IAE/C,IAAI6E,oBAAoB;YA6GlB+J;QA5GJ,mEAAmE;QACnE,4CAA4C;QAC5C,MAAMY,+BAA+BzW,YAAY0W,IAAI,CACnD5W,cAAc6W,aAAa,EAC3B;YACEC,UAAU,CAAC,sBAAsB,EAAE3P,UAAU;YAC7CwO,YAAY;gBACV,cAAcxO;YAChB;QACF,GACA4P;QAGF,MAAMvE,WAAW,MAAMmE,6BACrBnK,KACAxD,KACAH,KACAkN,UACA7L,WACA3D,YACAoI;QAGF,8EAA8E;QAC9E,mCAAmC;QACnC,0CAA0C;QAC1C,IACE6D,SAASwE,aAAa,IACtBxU,oBAAoBgQ,SAASwE,aAAa,KAC1C5M,WAAW6M,sBAAsB,EACjC;YACAhW,KAAK;YACL,KAAK,MAAMiW,UAAUxU,yBAAyB8P,SAASwE,aAAa,EAAG;gBACrE/V,KAAKiW;YACP;QACF;QAEA,mEAAmE;QACnE,oCAAoC;QACpC,IAAI1E,SAAS2E,eAAe,CAACC,IAAI,EAAE;YACjC,MAAMC,oBAAoB7E,SAAS2E,eAAe,CAACG,MAAM,GAAGC,IAAI,GAAG7P,KAAK;YACxE,IAAI2P,mBAAmB,MAAMA;QAC/B;QACA,gEAAgE;QAChE,IAAI7E,SAASgF,SAAS,CAACC,MAAM,EAAE;YAC7B,MAAMJ,oBAAoB7E,SAASgF,SAAS,CAACE,IAAI,CAAC,CAAC/K,MACjDpM,gBAAgBoM;YAElB,IAAI0K,mBAAmB,MAAMA;QAC/B;QAEA,MAAM7R,UAA+B;YACnCuQ;QACF;QACA,oEAAoE;QACpE,IACE7L,UAAUyN,kBAAkB,IAC5BzN,UAAU0N,uBAAuB,IACjC1N,UAAU2N,eAAe,EACzB;gBAEE3N;YADF,MAAM4N,iBAAiBC,QAAQC,GAAG,CAAC;iBACjC9N,8BAAAA,UAAU+N,gBAAgB,qBAA1B/N,4BAA4BgO,aAAa,CACvChO,UAAU2N,eAAe,IAAI,EAAE;mBAE9BM,OAAOb,MAAM,CAACpN,UAAUyN,kBAAkB,IAAI,CAAC;mBAC9CzN,UAAU0N,uBAAuB,IAAI,EAAE;aAC5C,EAAEQ,OAAO,CAAC;gBACT,IAAInL,QAAQC,GAAG,CAACmL,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CpO;gBAC3D;YACF;YAEA,IAAIC,WAAWoO,SAAS,EAAE;gBACxBpO,WAAWoO,SAAS,CAACV;YACvB,OAAO;gBACLtS,QAAQgT,SAAS,GAAGV;YACtB;QACF;QAEA,IAAItF,SAASiG,aAAa,EAAE;YAC1B1C,SAAS2C,SAAS,GAAGlG,SAASiG,aAAa,CAAC9P,IAAI,CAAC;QACnD;QAEA,uEAAuE;QACvE,MAAMgQ,cAAcC,OAAOpG,SAASqG,cAAc;QAClD7P,IAAI8P,SAAS,CAAC7Z,+BAA+B0Z;QAC7C5C,SAASxQ,OAAO,KAAK,CAAC;QACtBwQ,SAASxQ,OAAO,CAACtG,8BAA8B,GAAG0Z;QAElD,yEAAyE;QACzE,YAAY;QACZ,IAAIzO,UAAU6O,WAAW,KAAK,SAASvG,SAASwG,mBAAmB,KAAK,GAAG;YACzEjD,SAASkD,YAAY,GAAG;gBAAEjK,YAAY;gBAAGC,QAAQpJ;YAAU;QAC7D,OAAO;YACL,gEAAgE;YAChEkQ,SAASkD,YAAY,GAAG;gBACtBjK,YACEwD,SAASwG,mBAAmB,IAAInU,iBAC5B,QACA2N,SAASwG,mBAAmB;gBAClC/J,QACEuD,SAAS0G,eAAe,IAAIrU,iBACxBgB,YACA2M,SAAS0G,eAAe;YAChC;QACF;QAEA,qCAAqC;QACrC,IAAInD,EAAAA,yBAAAA,SAASkD,YAAY,qBAArBlD,uBAAuB/G,UAAU,MAAK,GAAG;YAC3C+G,SAASoD,iBAAiB,GAAG;gBAC3BC,aAAalP,UAAUmP,uBAAuB;gBAC9CnH,OAAOhI,UAAUoP,iBAAiB;YACpC;QACF;QAEA,OAAO,IAAInb,aAAa,MAAMS,eAAe4T,SAAS+G,MAAM,GAAG/T;IACjE,OAAO;QACL,8BAA8B;QAC9B,MAAMgU,wBACJpP,WAAWmF,wBAAwB,KACnCoE,kCAAAA,eAAgB6F,qBAAqB;QAEvC,MAAMrL,aAAazM,cAAc6E,YAAYsC,IAAIxB,0BAA0B;QAC3E,MAAMoF,eAAelN,4BACnBiN,KACAxD,KACAmB,KACAgE,YACAQ,cACAvE,WAAWqP,eAAe,EAC1BrP,WAAWsP,YAAY,EACvB5T,cACA8N,0BACA4F;QAGF,IACEvM,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB/C,WAAW4K,YAAY,IACvB,qEAAqE;QACrE,6DAA6D;QAC7D/H,QAAQC,GAAG,CAAC+H,YAAY,KAAK,UAC7B5R,kBAAkBmJ,QAClB,CAAC/G,oBACD;YACA,MAAMuP,eAAe5K,WAAW4K,YAAY;YAC5CxI,IAAI0I,eAAe,CAACC,EAAE,CAAC,OAAO;gBAC5B,IAAI,CAAC1I,aAAakN,WAAW,IAAI,CAACzP,UAAU0P,YAAY,EAAE;oBACxD,iEAAiE;oBACjE,MAAM,EAAE9O,QAAQ,EAAE,GAAG,IAAIiK,IAAIvI,IAAIrC,GAAG,IAAI,KAAK;oBAC7C6K,aAAalK,UAAU;gBACzB;YACF;QACF;QAEA,IAAIrF,oBAAoB;YACtB,OAAOyI,gBAAgB1B,KAAK3D;QAC9B,OAAO,IAAI9C,cAAc;YACvB,OAAOwG,kCAAkCC,KAAK3D,KAAK4D;QACrD;QAEA,MAAMoN,4BAA4B3Z,YAAY0W,IAAI,CAChD5W,cAAc6W,aAAa,EAC3B;YACEC,UAAU,CAAC,mBAAmB,EAAE3P,UAAU;YAC1CwO,YAAY;gBACV,cAAcxO;YAChB;QACF,GACA2S;QAGF,IAAIC,YAAwB;QAC5B,IAAIxD,iBAAiB;YACnB,gFAAgF;YAChF,MAAMyD,sBAAsB,MAAMjZ,aAAa;gBAC7CyL;gBACAxD;gBACAkL;gBACA+B;gBACAgE,gBAAgB1N;gBAChBrC;gBACAuC;gBACA2H;gBACAvL;YACF;YAEA,IAAImR,qBAAqB;gBACvB,IAAIA,oBAAoB7R,IAAI,KAAK,aAAa;oBAC5C,MAAM+R,qBAAqB5T,yBAAyBC;oBACpDyC,IAAIC,UAAU,GAAG;oBACjB,MAAMsQ,SAAS,MAAMM,0BACnBpN,cACAD,KACAxD,KACAH,KACAqB,WACAgQ,oBACAH,WACApG;oBAGF,OAAO,IAAIxV,aAAaob,QAAQ;wBAAExD;oBAAS;gBAC7C,OAAO,IAAIiE,oBAAoB7R,IAAI,KAAK,QAAQ;oBAC9C,IAAI6R,oBAAoBG,MAAM,EAAE;wBAC9BH,oBAAoBG,MAAM,CAACC,cAAc,CAACrE;wBAC1C,OAAOiE,oBAAoBG,MAAM;oBACnC,OAAO,IAAIH,oBAAoBD,SAAS,EAAE;wBACxCA,YAAYC,oBAAoBD,SAAS;oBAC3C;gBACF;YACF;QACF;QAEA,MAAMvU,UAA+B;YACnCuQ;QACF;QAEA,MAAMwD,SAAS,MAAMM,0BACnBpN,cACAD,KACAxD,KACAH,KACAqB,WACA3D,YACAwT,WACApG;QAGF,oEAAoE;QACpE,IACEzJ,UAAUyN,kBAAkB,IAC5BzN,UAAU0N,uBAAuB,IACjC1N,UAAU2N,eAAe,EACzB;gBAEE3N;YADF,MAAM4N,iBAAiBC,QAAQC,GAAG,CAAC;iBACjC9N,+BAAAA,UAAU+N,gBAAgB,qBAA1B/N,6BAA4BgO,aAAa,CACvChO,UAAU2N,eAAe,IAAI,EAAE;mBAE9BM,OAAOb,MAAM,CAACpN,UAAUyN,kBAAkB,IAAI,CAAC;mBAC9CzN,UAAU0N,uBAAuB,IAAI,EAAE;aAC5C,EAAEQ,OAAO,CAAC;gBACT,IAAInL,QAAQC,GAAG,CAACmL,wBAAwB,EAAE;oBACxCC,QAAQC,GAAG,CAAC,6CAA6CpO;gBAC3D;YACF;YAEA,IAAIC,WAAWoO,SAAS,EAAE;gBACxBpO,WAAWoO,SAAS,CAACV;YACvB,OAAO;gBACLtS,QAAQgT,SAAS,GAAGV;YACtB;QACF;QAEA,iDAAiD;QACjD,OAAO,IAAI3Z,aAAaob,QAAQ/T;IAClC;AACF;AAcA,OAAO,MAAM6U,uBAAsC,CACjD7N,KACAxD,KACA7B,UACA6C,OACA5C,qBACAgD,YACAwJ,0BACAlO,aACAmG;IAEA,IAAI,CAACW,IAAIrC,GAAG,EAAE;QACZ,MAAM,qBAAwB,CAAxB,IAAIoH,MAAM,gBAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAuB;IAC/B;IAEA,MAAMpH,MAAM5G,iBAAiBiJ,IAAIrC,GAAG,EAAEtE,WAAW;IAEjD,qEAAqE;IACrE,wEAAwE;IACxE,MAAM4N,uBAAuBnO,oBAAoBkH,IAAIjH,OAAO,EAAE;QAC5DG;QACAO,mBAAmBmE,WAAWgD,YAAY,CAACnH,iBAAiB,KAAK;IACnE;IAEA,MAAM,EAAEN,iBAAiB,EAAE,GAAG8N;IAE9B,MAAMC,oBAAoB;QAAE0B,OAAO;IAAM;IACzC,IAAIzB,iBAAwC;IAE5C,4EAA4E;IAC5E,SAAS;IACT,IAAI,OAAOvJ,WAAW8G,SAAS,KAAK,UAAU;QAC5C,IAAI9J,qBAAqB;YACvB,MAAM,qBAEL,CAFK,IAAIxC,eACR,6EADI,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;QAEA+O,iBAAiB7R,oBACfsI,WAAW8G,SAAS,EACpB9G,WAAWlD,MAAM;IAErB;IAEA,IACEyM,CAAAA,kCAAAA,eAAgB6F,qBAAqB,KACrCpP,WAAWmF,wBAAwB,EACnC;QACA,MAAM,qBAEL,CAFK,IAAI3K,eACR,+FADI,qBAAA;mBAAA;wBAAA;0BAAA;QAEN;IACF;IAEA,MAAM+J,eAAe5O,gBACnBqK,WAAWkQ,WAAW,CAACC,UAAU,CAAC7T,IAAI,EACtCyD,KACA/C;IAGF,MAAM8C,YAAY1K,gBAAgB;QAChCkH,MAAM0D,WAAWkQ,WAAW,CAACC,UAAU,CAAC7T,IAAI;QAC5CU;QACAgD;QACAsJ;QACA,8CAA8C;QAC9C/N;QACAmG,SAASD,cAAcC,OAAO;IAChC;IAEA,OAAO7N,iBAAiB+O,GAAG,CACzB9C,WACA,sBAAsB;IACtBsJ,0BACA,mBAAmB;IACnBhH,KACAxD,KACAmB,KACAhD,UACA6C,OACAI,YACAF,WACAuJ,sBACAC,mBACAC,gBACAhF,cACAiF,0BACA/H;AAEJ,EAAC;AAED,eAAeiO,eACbrN,YAA0B,EAC1BD,GAAoB,EACpBxD,GAAqB,EACrBH,GAAqB,EACrBqB,SAAoB,EACpBT,IAAgB,EAChBsQ,SAAc,EACdpG,cAAqC;IAErC,MAAMvJ,aAAavB,IAAIuB,UAAU;IACjC,MAAM8J,eAAe9J,WAAW8J,YAAY;IAC5C,4BAA4B;IAC5B,MAAMvG,0BAA0BvD,WAAWuD,uBAAuB;IAElE,MAAM,EAAE2E,0BAA0B,EAAEkI,wBAAwB,EAAE,GAC5DpZ;IACF,MAAM,EAAEmR,8BAA8B,EAAEkI,yBAAyB,EAAE,GACjErV,6BAA6ByD,IAAIxC,KAAK;IAExC,MAAMqU,kBAAkB/V,kBACtBzE,YAAYya,uBAAuB,IACnCvQ,WAAWgD,YAAY,CAACwN,mBAAmB;IAG7C,MAAMC,YACJzQ,WAAW0Q,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnT,GAAG,CAAC,CAACkT;YAKO7Q;eALO;YAClB+Q,KAAK,GAAGtS,IAAIgI,WAAW,CAAC,OAAO,EAAEoK,WAAWtZ,oBAC1CkH,KACA,QACC;YACHuS,SAAS,GAAEhR,2CAAAA,WAAWiR,4BAA4B,qBAAvCjR,wCAAyC,CAAC6Q,SAAS;YAC9DK,aAAalR,WAAWkR,WAAW;YACnCC,UAAU;YACVlV,OAAOwC,IAAIxC,KAAK;QAClB;;IAEJ,MAAM,CAACgM,gBAAgBmJ,gBAAgB,GAAGna,mBACxC+I,WAAW0Q,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9EjS,IAAIgI,WAAW,EACfzG,WAAWkR,WAAW,EACtBlR,WAAWiR,4BAA4B,EACvC1Z,oBAAoBkH,KAAK,OACzBA,IAAIxC,KAAK,EACT+D,WAAW1D,IAAI;IAGjB,MAAM+U,4BAAwD,IAAI3I;IAClE,MAAM4I,gBAAgB;IACtB,SAASC,qBAAqBhP,GAAkB;QAC9C,OAAOvC,WAAWwC,6BAA6B,oBAAxCxC,WAAWwC,6BAA6B,MAAxCxC,YACLuC,KACAH,KACAP,mBAAmBpD,KAAK;IAE5B;IACA,MAAM+S,+BAA+Bvb,kCACnC,CAAC,CAAC+J,WAAW0C,GAAG,EAChB,CAAC,CAAC1C,WAAWyR,UAAU,EACvBJ,2BACAC,eACAC;IAGF,SAASG,qBAAqBnP,GAAkB;QAC9C,OAAOvC,WAAWwC,6BAA6B,oBAAxCxC,WAAWwC,6BAA6B,MAAxCxC,YACLuC,KACAH,KACAP,mBAAmBpD,KAAK;IAE5B;IAEA,MAAMkT,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2B1b,uBAC/B,CAAC,CAAC8J,WAAW0C,GAAG,EAChB,CAAC,CAAC1C,WAAWyR,UAAU,EACvBJ,2BACAM,mBACAL,eACAI;IAGF,IAAIG,oBAA8C;IAElD,MAAMnD,YAAY9P,IAAI8P,SAAS,CAACoD,IAAI,CAAClT;IACrC,MAAMmT,eAAenT,IAAImT,YAAY,CAACD,IAAI,CAAClT;IAE3C,IAAI;QACF,IACE,qDAAqD;QACrDoB,WAAW0C,GAAG,IACd,uEAAuE;QACvEG,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,oGAAoG;QACpGF,QAAQC,GAAG,CAAC+H,YAAY,KAAK,UAC7B,yEAAyE;QACzE7K,WAAWgD,YAAY,CAACC,SAAS,EACjC;YACA,wFAAwF;YACxF,MAAMN,aAGF,MAAMtI,qBAAqBuI,GAAG,CAChCP,cACAiD,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAM,CAACqE,mBAAmBC,iBAAiB,GAAGC;YAC9CT,WAAWU,WAAW,GAAGF;YAEzB,MAAM6E,oBAAoB,MAAM3N,qBAAqBuI,GAAG,CACtDP,cACAlI,2BACA;gBACEkI,aAAa2P,cAAc,GAAG;gBAC9B,OAAOlI,aAAapG,sBAAsB,CACxCf,YACAY,wBAAwBI,aAAa,EACrC;oBACElB,SAAS+O;oBACTS,iBAAiB,IACf5P,aAAa2P,cAAc,KAAK,OAAO,cAAc;oBACvDE,kBAAiBnS,GAAW,EAAEoS,aAAqB;wBACjD,kEAAkE;wBAClE,mEAAmE;wBACnE,mEAAmE;wBACnE,OAAO,CAACpS,IAAIqS,UAAU,CAAC,YAAY,CAACrS,IAAIkG,QAAQ,CAAC;oBACnD;gBACF;YAEJ,GACA;gBACE5D,aAAa2P,cAAc,GAAG;YAChC;YAGF1O,4BACEJ,mBACA7D,MACAZ,KACAG,IAAIC,UAAU,KAAK,KACnB0E,yBACAzD,UAAU0D,KAAK,EACfnB;YAGFwP,oBAAoB,IAAIjY,kBAAkBoO;QAC5C,OAAO;YACL,wFAAwF;YACxF,MAAMrF,aAAa,MAAMtI,qBAAqBuI,GAAG,CAC/CP,cACAiD,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAGrBgT,oBAAoB,IAAIjY,kBACtBS,qBAAqBuI,GAAG,CACtBP,cACAyH,aAAapG,sBAAsB,EACnCf,YACAY,wBAAwBI,aAAa,EACrC;gBACElB,SAAS+O;YACX;QAGN;QAEA,mGAAmG;QACnG,oGAAoG;QACpG,6BAA6B;QAC7B,MAAMpX;QAEN,wEAAwE;QACxE,qBAAqB;QACrB,IAAI,OAAO4F,WAAW8G,SAAS,KAAK,UAAU;YAC5C,IAAIyC,CAAAA,kCAAAA,eAAgBxL,IAAI,MAAKtG,aAAa4a,IAAI,EAAE;gBAC9C,mEAAmE;gBACnE,4EAA4E;gBAC5E,yBAAyB;gBACzB,MAAMC,+BAA+Bta,gCACnC6Z,kBAAkBU,GAAG,IACrB9T,IAAIxC,KAAK,EACT0T;gBAGF,OAAO3b,aACLse,8BACApe;YAEJ,OAAO,IAAIqV,gBAAgB;gBACzB,uEAAuE;gBACvE,MAAMzC,YAAYjP,sBAAsB0R;gBAExC,MAAMiJ,SAASzJ,QAAQ,yBACpByJ,MAAM;gBAET,MAAMC,aAAa,MAAMpY,qBAAqBuI,GAAG,CAC/CP,cACAmQ,sBACA,KAACzK;oBACCC,mBAAmB6J,kBAAkBU,GAAG;oBACxCtK,gBAAgBA;oBAChB1E,yBAAyBA;oBACzB2E,4BAA4BA;oBAC5BC,gCAAgCA;oBAChClM,OAAOwC,IAAIxC,KAAK;oBAElB6K,WACA;oBACErE,SAASmP;oBACT3V,OAAOwC,IAAIxC,KAAK;gBAClB;gBAGF,MAAMyW,wBAAwBvb,0BAA0B;oBACtDsZ;oBACAL;oBACAuC,sBAAsBhB;oBACtBiB,UAAU5S,WAAW4S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBACA,OAAO,MAAMhc,0BAA0Bme,YAAY;oBACjDI,mBAAmB7a,gCACjB6Z,kBAAkBiB,OAAO,IACzBrU,IAAIxC,KAAK,EACT0T;oBAEF+C;oBACArC;gBACF;YACF;QACF;QAEA,mCAAmC;QACnC,MAAM3M,yBAAyBqF,QAAQ,yBACpCrF,sBAAsB;QAEzB,MAAM+O,aAAa,MAAMpY,qBAAqBuI,GAAG,CAC/CP,cACAqB,sCACA,KAACqE;YACCC,mBAAmB6J,kBAAkBU,GAAG;YACxCtK,gBAAgBA;YAChB1E,yBAAyBA;YACzB2E,4BAA4BA;YAC5BC,gCAAgCA;YAChClM,OAAOwC,IAAIxC,KAAK;YAElB;YACEwG,SAASmP;YACT3V,OAAOwC,IAAIxC,KAAK;YAChB8W,WAAW,CAAC5X;gBACVA,QAAQ4L,OAAO,CAAC,CAACzJ,OAAOF;oBACtB2U,aAAa3U,KAAKE;gBACpB;YACF;YACA0V,kBAAkBhT,WAAWiT,qBAAqB;YAClDC,kBAAkB;gBAAC9B;aAAgB;YACnCzB;QACF;QAGF,MAAM+C,wBAAwBvb,0BAA0B;YACtDsZ;YACAL;YACAuC,sBAAsBhB;YACtBiB,UAAU5S,WAAW4S,QAAQ;YAC7BtC,iBAAiBA;QACnB;QACA;;;;;;;;;;;;;;;;KAgBC,GACD,MAAM6C,qBACJnT,WAAWoT,uBAAuB,KAAK,QACvC,CAAC,CAACpT,WAAWqT,oBAAoB;QAEnC,MAAMC,qBAAqBtT,WAAW0C,GAAG;QACzC,OAAO,MAAMvO,mBAAmBse,YAAY;YAC1CI,mBAAmB7a,gCACjB6Z,kBAAkBiB,OAAO,IACzBrU,IAAIxC,KAAK,EACT0T;YAEF/N,oBAAoBuR;YACpBT;YACArC;YACAiD;QACF;IACF,EAAE,OAAO/Q,KAAK;QACZ,IACErK,wBAAwBqK,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAImF,OAAO,KAAK,YACvBnF,IAAImF,OAAO,CAACzB,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAM1D;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMgR,qBAAqB3c,oBAAoB2L;QAC/C,IAAIgR,oBAAoB;YACtB,MAAMzL,QAAQ3P,4BAA4BoK;YAC1CzL,MACE,GAAGyL,IAAIiR,MAAM,CAAC,mDAAmD,EAAE/U,IAAI1B,QAAQ,CAAC,kFAAkF,EAAE+K,OAAO;YAG7K,MAAMvF;QACR;QAEA,IAAIoD;QAEJ,IAAIpQ,0BAA0BgN,MAAM;YAClC3D,IAAIC,UAAU,GAAGvJ,4BAA4BiN;YAC7CoD,YAAYtQ,mCAAmCuJ,IAAIC,UAAU;QAC/D,OAAO,IAAInJ,gBAAgB6M,MAAM;YAC/BoD,YAAY;YACZ/G,IAAIC,UAAU,GAAGpJ,+BAA+B8M;YAEhD,MAAMkR,cAAcvc,cAClB1B,wBAAwB+M,MACxBvC,WAAW4S,QAAQ;YAGrB,gEAAgE;YAChE,YAAY;YACZ,MAAMzX,UAAU,IAAIuY;YACpB,IAAI3c,qBAAqBoE,SAASkH,aAAasR,cAAc,GAAG;gBAC9DjF,UAAU,cAAcjR,MAAMmW,IAAI,CAACzY,QAAQ+R,MAAM;YACnD;YAEAwB,UAAU,YAAY+E;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9B3U,IAAIC,UAAU,GAAG;QACnB;QAEA,MAAM,CAACgV,qBAAqBC,qBAAqB,GAAG7c,mBAClD+I,WAAW0Q,aAAa,EACxBjS,IAAIgI,WAAW,EACfzG,WAAWkR,WAAW,EACtBlR,WAAWiR,4BAA4B,EACvC1Z,oBAAoBkH,KAAK,QACzBA,IAAIxC,KAAK,EACT;QAGF,MAAM8X,kBAAkB,MAAM1Z,qBAAqBuI,GAAG,CACpDP,cACA4E,oBACA5H,MACAZ,KACA4S,0BAA0B9T,GAAG,CAAC,AAACgF,IAAYqF,MAAM,IAAI,OAAOrF,KAC5DoD;QAGF,MAAMqO,oBAAoB3Z,qBAAqBuI,GAAG,CAChDP,cACAyH,aAAapG,sBAAsB,EACnCqQ,iBACAxQ,wBAAwBI,aAAa,EACrC;YACElB,SAAS+O;QACX;QAGF,IAAIK,sBAAsB,MAAM;YAC9B,wFAAwF;YACxF,gCAAgC;YAChC,MAAMtP;QACR;QAEA,IAAI;YACF,MAAM0R,aAAa,MAAM5Z,qBAAqBuI,GAAG,CAC/CP,cACApO,2BACA;gBACEigB,gBAAgBnL,QAAQ;gBACxBoL,uBACE,KAAChL;oBACCnB,mBAAmBgM;oBACnB/L,gBAAgB4L;oBAChBtQ,yBAAyBA;oBACzBtH,OAAOwC,IAAIxC,KAAK;;gBAGpBmY,eAAe;oBACbnY,OAAOwC,IAAIxC,KAAK;oBAChB,wCAAwC;oBACxCiX,kBAAkB;wBAACY;qBAAqB;oBACxCnE;gBACF;YACF;YAGF;;;;;;;;;;;;;;;OAeC,GACD,MAAMwD,qBACJnT,WAAWoT,uBAAuB,KAAK,QACvC,CAAC,CAACpT,WAAWqT,oBAAoB;YACnC,MAAMC,qBAAqBtT,WAAW0C,GAAG;YACzC,OAAO,MAAMvO,mBAAmB8f,YAAY;gBAC1CpB,mBAAmB7a,gCACjB,+DAA+D;gBAC/D,8DAA8D;gBAC9D,SAAS;gBACT6Z,kBAAkBiB,OAAO,IACzBrU,IAAIxC,KAAK,EACT0T;gBAEF/N,oBAAoBuR;gBACpBT,uBAAuBvb,0BAA0B;oBAC/CsZ;oBACAL;oBACAuC,sBAAsB,EAAE;oBACxBC,UAAU5S,WAAW4S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBACAD;gBACAiD;YACF;QACF,EAAE,OAAOe,UAAe;YACtB,IACExR,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBxN,0BAA0B8e,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BvL,QAAQ;gBACVuL;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,SAASjR;IACP,IAAIF;IACJ,IAAIqR,SAAS,IAAI5G,QAAyB,CAAC6G;QACzCtR,oBAAoBsR;IACtB;IACA,OAAO;QAACtR;QAAoBqR;KAAO;AACrC;AAEA,eAAejR,4BACbJ,iBAA+D,EAC/D7D,IAAgB,EAChBZ,GAAqB,EACrBgW,UAAmB,EACnBlR,uBAA2E,EAC3EC,KAAa,EACbnB,YAA0B;IAE1B,MAAM,EAAEjD,cAAc0K,YAAY,EAAE,GAAGrL;IACvC,MAAMsF,aAAazM,cACjBwS,aAAazK,IAAI,EACjBZ,IAAIxB,0BAA0B;IAGhC,iEAAiE;IACjE,yEAAyE;IACzE,6EAA6E;IAC7E,8EAA8E;IAC9E,MAAMyX,mCAAmC,IAAIxQ;IAE7C,4EAA4E;IAC5E,gFAAgF;IAChF,6EAA6E;IAC7E,MAAMyQ,gCAAgC,IAAIzQ;IAE1C,MAAME,cAAc,IAAI9J;IACxB,MAAM0J,2BAA2BpJ;IACjC,MAAMga,8BAA8C;QAClD7W,MAAM;QACNuG,OAAO;QACPP;QACAQ,cAAc,EAAE;QAChBC,cAAcmQ,8BAA8BlQ,MAAM;QAClDC,YAAYgQ;QACZtQ;QACAO,iBAAiB;QACjBC,YAAYnK;QACZoK,QAAQpK;QACRqK,OAAOrK;QACPsK,MAAM,EAAE;QACRf;IACF;IAEA,MAAM6Q,0BAA0B,IAAI3Q;IACpC,MAAM4Q,8BAA8C;QAClD/W,MAAM;QACNuG,OAAO;QACPP;QACAQ,cAAc,EAAE;QAChBC,cAAcqQ,wBAAwBpQ,MAAM;QAC5CC,YAAYmQ;QACZzQ;QACAO,iBAAiB;QACjBC,YAAYnK;QACZoK,QAAQpK;QACRqK,OAAOrK;QACPsK,MAAM,EAAE;QACRf;IACF;IAEA,0FAA0F;IAC1F,wFAAwF;IACxF,MAAM+Q,yBAAyB,MAAM1a,qBAAqBuI,GAAG,CAC3DgS,6BACAtP,eACAjG,MACAZ,KACAgW;IAGF,IAAIO;IACJ,IAAI;QACFA,sBAAsB3a,qBAAqBuI,GAAG,CAC5CgS,6BACA9K,aAAapG,sBAAsB,EACnCqR,wBACAxR,wBAAwBI,aAAa,EACrC;YACElB,SAAS,CAACF;gBACR,MAAMqF,SAASxR,2BAA2BmM;gBAE1C,IAAIqF,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IACE8M,iCAAiCjQ,MAAM,CAACwQ,OAAO,IAC/CN,8BAA8BlQ,MAAM,CAACwQ,OAAO,EAC5C;oBACA,mEAAmE;oBACnE,iEAAiE;oBACjE;gBACF,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;oBACAjb,0CAA0CqI,KAAKiB;gBACjD;YACF;YACAiB,QAAQkQ,8BAA8BlQ,MAAM;QAC9C;IAEJ,EAAE,OAAOlC,KAAc;QACrB,IACEmS,iCAAiCjQ,MAAM,CAACwQ,OAAO,IAC/CN,8BAA8BlQ,MAAM,CAACwQ,OAAO,EAC5C;QACA,4EAA4E;QAC9E,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;YACA,8EAA8E;YAC9E,mFAAmF;YACnFjb,0CAA0CqI,KAAKiB;QACjD;IACF;IAEA,MAAMvH,QAAQ;IACd,MAAM,EAAEiM,0BAA0B,EAAE,GAAGlR;IACvC,MAAM,EAAEmR,8BAA8B,EAAE,GAAGnN,6BAA6BiB;IAExE,IAAI+Y,qBAAqB;QACvB,MAAM,CAACI,cAAcC,aAAa,GAAGL,oBAAoBzC,GAAG;QAC5DyC,sBAAsB;QACtB,gFAAgF;QAChF,sBAAsB;QACtB,MAAMM,mBAAmBF,cAAc7R;QAEvC,MAAMgS,YAAYxM,QAAQ,yBACvBwM,SAAS;QACZ,MAAMC,6BAA6Bnb,qBAAqBuI,GAAG,CACzDkS,6BACAS,yBACA,KAACxN;YACCC,mBAAmBqN;YACnBpN,gBAAgB,KAAO;YACvB1E,yBAAyBA;YACzB2E,4BAA4BA;YAC5BC,gCAAgCA;YAChClM,OAAOA;YAET;YACEwI,QAAQoQ,wBAAwBpQ,MAAM;YACtChC,SAAS,CAACF;gBACR,MAAMqF,SAASxR,2BAA2BmM;gBAE1C,IAAIqF,QAAQ;oBACV,OAAOA;gBACT;gBAEA,IAAIiN,wBAAwBpQ,MAAM,CAACwQ,OAAO,EAAE;gBAC1C,4EAA4E;gBAC9E,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;oBACA,8EAA8E;oBAC9E,mFAAmF;oBACnFjb,0CAA0CqI,KAAKiB;gBACjD;YACF;QACF;QAEFgS,2BAA2BC,KAAK,CAAC,CAAClT;YAChC,IAAIsS,wBAAwBpQ,MAAM,CAACwQ,OAAO,EAAE;YAC1C,2DAA2D;YAC7D,OAAO;gBACL,uEAAuE;gBACvE,yCAAyC;gBACzC,IAAIpS,QAAQC,GAAG,CAACqS,sBAAsB,EAAE;oBACtCjb,0CAA0CqI,KAAKiB;gBACjD;YACF;QACF;IACF;IAEA,MAAMY,YAAYa,UAAU;IAC5B,8DAA8D;IAC9D,gEAAgE;IAChE4P,wBAAwB3P,KAAK;IAC7ByP,8BAA8BzP,KAAK;IACnCwP,iCAAiCxP,KAAK;IAEtC,sEAAsE;IACtE,kFAAkF;IAElF,MAAMwQ,wBAAwB,IAAIxR;IAClC,MAAMyR,wBAAwBnd,2BAA2B;IAEzD,MAAMod,4BAA4C;QAChD7X,MAAM;QACNuG,OAAO;QACPP;QACAQ,cAAc,EAAE;QAChBC,cAAckR,sBAAsBjR,MAAM;QAC1CC,YAAYgR;QACZ,uFAAuF;QACvFtR,aAAa;QACbO,iBAAiBgR;QACjB/Q,YAAYnK;QACZoK,QAAQpK;QACRqK,OAAOrK;QACPsK,MAAM,EAAE;QACRf;IACF;IAEA,MAAM6R,wBAAwB,IAAI3R;IAClC,MAAM4R,wBAAwBtd,2BAA2B;IACzD,MAAMud,oBAAoBtd;IAE1B,MAAMud,4BAA4C;QAChDjY,MAAM;QACNuG,OAAO;QACPP;QACAQ,cAAc,EAAE;QAChBC,cAAcqR,sBAAsBpR,MAAM;QAC1CC,YAAYmR;QACZ,uFAAuF;QACvFzR,aAAa;QACbO,iBAAiBmR;QACjBlR,YAAYnK;QACZoK,QAAQpK;QACRqK,OAAOrK;QACPsK,MAAM,EAAE;QACRf;IACF;IAEA,MAAMiS,qBAAqB,MAAM5b,qBAAqBuI,GAAG,CACvDgT,2BACAtQ,eACAjG,MACAZ,KACAgW;IAGF,MAAMyB,8BAA8B,MAAMlc,0BACxC0b,sBAAsBjR,MAAM,EAC5B,IACEpK,qBAAqBuI,GAAG,CACtBgT,2BACA9L,aAAapG,sBAAsB,EACnCuS,oBACA1S,wBAAwBI,aAAa,EACrC;YACElB,SAAS,CAACF;gBACR,IAAIxH,uBAAuBwH,MAAM;oBAC/B,OAAOA,IAAIqF,MAAM;gBACnB;gBAEA,IACE8N,sBAAsBjR,MAAM,CAACwQ,OAAO,IACpC1c,4BAA4BgK,MAC5B;oBACA,OAAOA,IAAIqF,MAAM;gBACnB;gBAEA,OAAOxR,2BAA2BmM;YACpC;YACAkC,QAAQiR,sBAAsBjR,MAAM;QACtC,IAEJ;QACEiR,sBAAsBxQ,KAAK;IAC7B;IAGF,IAAIiR,eAAe;IACnB,MAAMC,qBAAqBF,4BAA4BG,cAAc;IACrE,IAAI;QACF,MAAMd,YAAYxM,QAAQ,yBACvBwM,SAAS;QACZ,MAAMtb,0BACJ,IACEI,qBAAqBuI,GAAG,CACtBoT,2BACAT,yBACA,KAACxN;gBACCC,mBAAmBoO;gBACnBnO,gBAAgB,KAAO;gBACvB1E,yBAAyBA;gBACzB2E,4BAA4BA;gBAC5BC,gCAAgCA;gBAChClM,OAAOwC,IAAIxC,KAAK;gBAElB;gBACEwI,QAAQoR,sBAAsBpR,MAAM;gBACpChC,SAAS,CAACF,KAAK+T;oBACb,IAAIvb,uBAAuBwH,MAAM;wBAC/BwT,kBAAkBQ,aAAa,CAACC,IAAI,CAACjU;wBAErC;oBACF;oBAEA,IACEhK,4BAA4BgK,QAC5BsT,sBAAsBpR,MAAM,CAACwQ,OAAO,EACpC;wBACA,IAAI,CAACkB,cAAc;4BACjB,+FAA+F;4BAC/F,wGAAwG;4BACxG,+BAA+B;4BAC/B9T,aAAakN,WAAW,GAAG;wBAC7B;wBAEA,MAAMkH,iBAAiBH,UAAUG,cAAc;wBAC/C,IAAI,OAAOA,mBAAmB,UAAU;4BACtC9d,0BACE6K,OACAiT,gBACAV,mBACAJ,uBACAG;wBAEJ;wBACA;oBACF;oBAEA,OAAO1f,2BAA2BmM;gBACpC;YACF,IAEJ;YACEsT,sBAAsB3Q,KAAK;YAC3BkR,mBAAmBM,eAAe;QACpC;IAEJ,EAAE,OAAOnU,KAAK;QACZ4T,eAAe;QACf,IACE5d,4BAA4BgK,QAC5BsT,sBAAsBpR,MAAM,CAACwQ,OAAO,EACpC;QACA,4FAA4F;QAC9F,OAAO;QACL,uEAAuE;QACvE,wEAAwE;QACxE,0EAA0E;QAC1E,sEAAsE;QACtE,sEAAsE;QACxE;IACF;IAEA,SAAS0B;QACP,IAAI;YACF/d,yBACE4K,OACAuS,mBACAJ,uBACAG;QAEJ,EAAE,OAAM,CAAC;QACT,OAAO;IACT;IAEA5S,gCAAkB,KAACyT;AACrB;AAaA;;CAEC,GACD,SAASC,+BAA+B9W,SAAoB;IAC1D,MAAM,EAAE8B,kBAAkB,EAAE,GAAG9B;IAC/B,IAAI,CAAC8B,oBAAoB,OAAO;IAEhC,OAAO;AACT;AAEA,eAAe+K,kBACbvK,GAAoB,EACpBxD,GAAqB,EACrBH,GAAqB,EACrBkN,QAAqC,EACrC7L,SAAoB,EACpBT,IAAgB,EAChBkF,YAA2B;IAE3B,kEAAkE;IAClE,yEAAyE;IACzE,6DAA6D;IAC7D,MAAMoL,YAAY;IAClB,MAAM5L,aAAazM,cAAc+H,MAAMZ,IAAIxB,0BAA0B;IAErE,MAAM+C,aAAavB,IAAIuB,UAAU;IACjC,MAAM8J,eAAe9J,WAAW8J,YAAY;IAC5C,4BAA4B;IAC5B,MAAMvG,0BAA0BvD,WAAWuD,uBAAuB;IAClE,MAAMvG,sBAAsB8C,UAAU9C,mBAAmB;IAEzD,MAAM,EAAEkL,0BAA0B,EAAEkI,wBAAwB,EAAE,GAC5DpZ;IACF,MAAM,EAAEmR,8BAA8B,EAAEkI,yBAAyB,EAAE,GACjErV,6BAA6ByD,IAAIxC,KAAK;IAExC,MAAMqU,kBAAkB/V,kBACtBzE,YAAYya,uBAAuB,IACnCvQ,WAAWgD,YAAY,CAACwN,mBAAmB;IAG7C,MAAMC,YACJzQ,WAAW0Q,aAAa,CAACC,aAAa,CACnCC,MAAM,CACL,CAACC,WACCA,SAASC,QAAQ,CAAC,UAAU,CAACD,SAASC,QAAQ,CAAC,eAElDnT,GAAG,CAAC,CAACkT;YAKO7Q;eALO;YAClB+Q,KAAK,GAAGtS,IAAIgI,WAAW,CAAC,OAAO,EAAEoK,WAAWtZ,oBAC1CkH,KACA,QACC;YACHuS,SAAS,GAAEhR,2CAAAA,WAAWiR,4BAA4B,qBAAvCjR,wCAAyC,CAAC6Q,SAAS;YAC9DK,aAAalR,WAAWkR,WAAW;YACnCC,UAAU;YACVlV,OAAOwC,IAAIxC,KAAK;QAClB;;IAEJ,MAAM,CAACgM,gBAAgBmJ,gBAAgB,GAAGna,mBACxC+I,WAAW0Q,aAAa,EACxB,6CAA6C;IAC7C,8EAA8E;IAC9EjS,IAAIgI,WAAW,EACfzG,WAAWkR,WAAW,EACtBlR,WAAWiR,4BAA4B,EACvC1Z,oBAAoBkH,KAAK,OACzBA,IAAIxC,KAAK,EACT+D,WAAW1D,IAAI;IAGjB,MAAM+U,4BAAwD,IAAI3I;IAClE,+EAA+E;IAC/E,MAAM4I,gBAAgB,CAAC,CAACtR,WAAWgD,YAAY,CAACnH,iBAAiB;IACjE,SAAS0V,qBAAqBhP,GAAkB;QAC9C,OAAOvC,WAAWwC,6BAA6B,oBAAxCxC,WAAWwC,6BAA6B,MAAxCxC,YACLuC,KACAH,KACAP,mBAAmBpD,KAAK;IAE5B;IACA,MAAM+S,+BAA+Bvb,kCACnC,CAAC,CAAC+J,WAAW0C,GAAG,EAChB,CAAC,CAAC1C,WAAWyR,UAAU,EACvBJ,2BACAC,eACAC;IAGF,SAASG,qBAAqBnP,GAAkB;QAC9C,OAAOvC,WAAWwC,6BAA6B,oBAAxCxC,WAAWwC,6BAA6B,MAAxCxC,YACLuC,KACAH,KACAP,mBAAmBpD,KAAK;IAE5B;IACA,MAAMkT,oBAAoC,EAAE;IAC5C,MAAMC,2BAA2B1b,uBAC/B,CAAC,CAAC8J,WAAW0C,GAAG,EAChB,CAAC,CAAC1C,WAAWyR,UAAU,EACvBJ,2BACAM,mBACAL,eACAI;IAGF,IAAImF,6BAG8B;IAClC,MAAMC,oBAAoB,CAAC9X;QACzB2M,SAASxQ,OAAO,KAAK,CAAC;QACtBwQ,SAASxQ,OAAO,CAAC6D,KAAK,GAAGJ,IAAImH,SAAS,CAAC/G;IACzC;IACA,MAAM0P,YAAY,CAAC1P,MAAc1B;QAC/BsB,IAAI8P,SAAS,CAAC1P,MAAM1B;QACpBwZ,kBAAkB9X;QAClB,OAAOJ;IACT;IACA,MAAMmT,eAAe,CAAC/S,MAAc1B;QAClC,IAAIG,MAAMC,OAAO,CAACJ,QAAQ;YACxBA,MAAMyJ,OAAO,CAAC,CAACgQ;gBACbnY,IAAImT,YAAY,CAAC/S,MAAM+X;YACzB;QACF,OAAO;YACLnY,IAAImT,YAAY,CAAC/S,MAAM1B;QACzB;QACAwZ,kBAAkB9X;IACpB;IAEA,IAAIqF,iBAAwC;IAE5C,IAAI;QACF,IAAIrE,WAAWgD,YAAY,CAACC,SAAS,EAAE;YACrC,IAAIjD,WAAWgD,YAAY,CAACnH,iBAAiB,EAAE;gBAC7C;;;;;;;;;;;;SAYC,GAED,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAM6Y,mCAAmC,IAAIxQ;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAMyQ,gCAAgC,IAAIzQ;gBAE1C,kFAAkF;gBAClF,yBAAyB;gBACzB,MAAME,cAAc,IAAI9J;gBAExB,iEAAiE;gBACjE,8DAA8D;gBAC9D,wEAAwE;gBACxE,6BAA6B;gBAC7B,MAAM0J,2BAA2BpJ;gBAEjC,MAAMga,8BAA+CvQ,iBAAiB;oBACpEtG,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAcmQ,8BAA8BlQ,MAAM;oBAClDC,YAAYgQ;oBACZtQ;oBACAO,iBAAiB;oBACjBC,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAMgT,uBAAuB,MAAM3c,qBAAqBuI,GAAG,CACzDgS,6BACAtP,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAGrB,MAAMoY,6BAA6B5c,qBAAqBuI,GAAG,CACzDgS,6BACA9K,aAAayL,SAAS,EACtByB,sBACAzT,wBAAwBI,aAAa,EACrC;oBACElB,SAAS,CAACF;wBACR,MAAMqF,SAASxR,2BAA2BmM;wBAE1C,IAAIqF,QAAQ;4BACV,OAAOA;wBACT;wBAEA,IAAI8M,iCAAiCjQ,MAAM,CAACwQ,OAAO,EAAE;4BACnD,mEAAmE;4BACnE,iEAAiE;4BACjE;wBACF,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;4BACAjb,0CAA0CqI,KAAKzC,UAAU0D,KAAK;wBAChE;oBACF;oBACA,iFAAiF;oBACjF,qCAAqC;oBACrC0T,YAAYzb;oBACZ,+EAA+E;oBAC/E,iFAAiF;oBACjF,iDAAiD;oBACjDgJ,QAAQkQ,8BAA8BlQ,MAAM;gBAC9C;gBAGF,MAAML,YAAYa,UAAU;gBAC5B0P,8BAA8BzP,KAAK;gBACnCwP,iCAAiCxP,KAAK;gBAEtC,IAAIiS;gBACJ,IAAI;oBACFA,sBAAsB,MAAMtd,iCAC1Bod;gBAEJ,EAAE,OAAO1U,KAAK;oBACZ,IACEoS,8BAA8BlQ,MAAM,CAACwQ,OAAO,IAC5CP,iCAAiCjQ,MAAM,CAACwQ,OAAO,EAC/C;oBACA,4EAA4E;oBAC9E,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFjb,0CAA0CqI,KAAKzC,UAAU0D,KAAK;oBAChE;gBACF;gBAEA,IAAI2T,qBAAqB;oBACvB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAM7B,mBACJ6B,oBAAoBC,QAAQ,IAC5B7T;oBAGF,MAAMsR,0BAA0B,IAAI3Q;oBACpC,MAAM4Q,8BAA8C;wBAClD/W,MAAM;wBACNuG,OAAO;wBACPP;wBACAQ,cAAcA;wBACdC,cAAcqQ,wBAAwBpQ,MAAM;wBAC5CC,YAAYmQ;wBACZzQ,aAAa;wBACbO,iBAAiB;wBACjBC,YAAYnK;wBACZoK,QAAQpK;wBACRqK,OAAOrK;wBACPsK,MAAM;+BAAIR;yBAAa;wBACvBP;oBACF;oBAEA,MAAMuR,YAAYxM,QAAQ,yBACvBwM,SAAS;oBACZ,MAAMxb,mCACJ,IACEM,qBAAqBuI,GAAG,CACtBkS,6BACAS,yBACA,KAACxN;4BACCC,mBAAmBmP,oBAAoBE,iBAAiB;4BACxDpP,gBAAgBA;4BAChB1E,yBAAyBA;4BACzB2E,4BAA4BA;4BAC5BC,gCACEA;4BAEFlM,OAAOwC,IAAIxC,KAAK;4BAElB;4BACEwI,QAAQoQ,wBAAwBpQ,MAAM;4BACtChC,SAAS,CAACF;gCACR,MAAMqF,SAASxR,2BAA2BmM;gCAE1C,IAAIqF,QAAQ;oCACV,OAAOA;gCACT;gCAEA,IAAIiN,wBAAwBpQ,MAAM,CAACwQ,OAAO,EAAE;gCAC1C,4EAA4E;gCAC9E,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;oCACA,8EAA8E;oCAC9E,mFAAmF;oCACnFjb,0CACEqI,KACAzC,UAAU0D,KAAK;gCAEnB;4BACF;4BACA0P,kBAAkB;gCAAC9B;6BAAgB;wBACrC,IAEJ;wBACEyD,wBAAwB3P,KAAK;oBAC/B,GACAuQ,KAAK,CAAC,CAAClT;wBACP,IACEoS,8BAA8BlQ,MAAM,CAACwQ,OAAO,IAC5C1c,4BAA4BgK,MAC5B;wBACA,4EAA4E;wBAC9E,OAAO,IACLM,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;4BACA,8EAA8E;4BAC9E,mFAAmF;4BACnFjb,0CAA0CqI,KAAKzC,UAAU0D,KAAK;wBAChE;oBACF;gBACF;gBAEA,IAAI8T,kBAAkB;gBACtB,MAAM5B,wBAAwB,IAAIxR;gBAClC,MAAMyR,wBAAwBnd,2BAC5BwH,WAAW6M,sBAAsB;gBAGnC,MAAM0K,4BAA6ClT,iBAAiB;oBAClEtG,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAckR,sBAAsBjR,MAAM;oBAC1CC,YAAYgR;oBACZ,uFAAuF;oBACvFtR,aAAa;oBACbO,iBAAiBgR;oBACjB/Q,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,MAAMwT,yBAAyB,MAAMnd,qBAAqBuI,GAAG,CAC3D2U,2BACAjS,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAErB,IAAI4Y,qBAAqB;gBACzB,MAAM5F,oBAAqBgF,6BACzB,MAAMhd,iCACJE,mCACE;oBACE,MAAM2d,kBAAkB,MAAMrd,qBAAqBuI,GAAG,CACpD,qBAAqB;oBACrB2U,2BACA,sBAAsB;oBACtBzN,aAAayL,SAAS,EACtB,4CAA4C;oBAC5CiC,wBACAjU,wBAAwBI,aAAa,EACrC;wBACElB,SAAS,CAACF;4BACR,OAAOiP,6BAA6BjP;wBACtC;wBACAkC,QAAQiR,sBAAsBjR,MAAM;oBACtC;oBAEFgT,qBAAqB;oBACrB,OAAOC;gBACT,GACA;oBACE,IAAIhC,sBAAsBjR,MAAM,CAACwQ,OAAO,EAAE;wBACxC,4EAA4E;wBAC5E,6EAA6E;wBAC7EqC,kBAAkB;wBAClB;oBACF;oBAEA,IAAIG,oBAAoB;wBACtB,kFAAkF;wBAClF,iCAAiC;wBACjCH,kBAAkB;oBACpB;oBACA5B,sBAAsBxQ,KAAK;gBAC7B;gBAIN,MAAM4Q,wBAAwBtd,2BAC5BwH,WAAW6M,sBAAsB;gBAEnC,MAAMgJ,wBAAwB,IAAI3R;gBAClC,MAAM8R,4BAA4C;oBAChDjY,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAcqR,sBAAsBpR,MAAM;oBAC1CC,YAAYmR;oBACZ,oEAAoE;oBACpEzR,aAAa;oBACbO,iBAAiBmR;oBACjBlR,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,IAAI2T,kBAAkB;gBACtB,IAAI5B,oBAAoBtd;gBAExB,MAAM8c,YAAYxM,QAAQ,yBACvBwM,SAAS;gBACZ,IAAI,EAAEqC,OAAO,EAAE9Q,SAAS,EAAE,GAAG,MAAM/M,mCACjC,IACEM,qBAAqBuI,GAAG,CACtBoT,2BACAT,yBACA,KAACxN;wBACCC,mBAAmB6J,kBAAkBwF,iBAAiB;wBACtDpP,gBAAgBA;wBAChB1E,yBAAyBA;wBACzB2E,4BAA4BA;wBAC5BC,gCAAgCA;wBAChClM,OAAOwC,IAAIxC,KAAK;wBAElB;wBACEwI,QAAQoR,sBAAsBpR,MAAM;wBACpChC,SAAS,CAACF,KAAc+T;4BACtB,IACE/d,4BAA4BgK,QAC5BsT,sBAAsBpR,MAAM,CAACwQ,OAAO,EACpC;gCACA0C,kBAAkB;gCAElB,MAAMlB,iBAAqC,AACzCH,UACAG,cAAc;gCAChB,IAAI,OAAOA,mBAAmB,UAAU;oCACtC9d,0BACEmH,UAAU0D,KAAK,EACfiT,gBACAV,mBACAJ,uBACAG;gCAEJ;gCACA;4BACF;4BAEA,OAAOlE,yBAAyBrP,KAAK+T;wBACvC;wBACAvD,WAAW,CAAC5X;4BACVA,QAAQ4L,OAAO,CAAC,CAACzJ,OAAOF;gCACtB2U,aAAa3U,KAAKE;4BACpB;wBACF;wBACA0V,kBAAkBhT,WAAWiT,qBAAqB;wBAClDC,kBAAkB;4BAAC9B;yBAAgB;oBACrC,IAEJ;oBACEyE,sBAAsB3Q,KAAK;gBAC7B;gBAGFtM,yBACEkH,UAAU0D,KAAK,EACfuS,mBACAJ,uBACAG;gBAGF,MAAMpD,wBAAwBvb,0BAA0B;oBACtDsZ;oBACAL;oBACAuC,sBAAsBhB;oBACtBiB,UAAU5S,WAAW4S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBAEA,MAAMnR,aAAa,MAAM5K,eAAesd,kBAAkBuF,QAAQ;gBAClEzL,SAASxM,UAAU,GAAGA;gBACtBwM,SAASkM,WAAW,GAAG,MAAMC,mBAC3B3Y,YACAoY,2BACAzN,cACA9J,YACAhD;gBAGF,IAAIsa,mBAAmBK,iBAAiB;oBACtC,IAAI7Q,aAAa,MAAM;wBACrB,oBAAoB;wBACpB6E,SAAS7E,SAAS,GAAG,MAAMlP,6BACzBkP,WACA9J,qBACAgH;oBAEJ,OAAO;wBACL,oBAAoB;wBACpB2H,SAAS7E,SAAS,GAAG,MAAMnP,6BACzBqM;oBAEJ;oBACA6N,kBAAkBiB,OAAO;oBACzB,OAAO;wBACL/F,iBAAiBsE;wBACjBjE,WAAWuE;wBACXxC,QAAQ,MAAM/a,yBAAyBwjB,SAAS;4BAC9ClF;4BACArC;wBACF;wBACAzD,eAAe/T,qBACb8c,uBACAG;wBAEF,0CAA0C;wBAC1ClH,qBAAqB2I,0BAA0B3S,UAAU;wBACzDkK,iBAAiByI,0BAA0B1S,MAAM;wBACjD4J,gBAAgB8I,0BAA0BzS,KAAK;wBAC/CuJ,eAAekJ,0BAA0BxS,IAAI;oBAC/C;gBACF,OAAO;oBACL,cAAc;oBACd,IAAIjF,UAAU0P,YAAY,EAAE;wBAC1B,MAAM,qBAEL,CAFK,IAAIvX,sBACR,qHADI,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;oBAEA,IAAIwa,aAAamF;oBACjB,IAAI9Q,aAAa,MAAM;wBACrB,+FAA+F;wBAC/F,qGAAqG;wBACrG,MAAM0L,SAASzJ,QAAQ,yBACpByJ,MAAM;wBAET,qEAAqE;wBACrE,4EAA4E;wBAC5E,MAAMuF,gBAAgB,IAAIC;wBAE1B,MAAMC,eAAe,MAAMzF,qBACzB,KAACzK;4BACCC,mBAAmB+P;4BACnB9P,gBAAgB,KAAO;4BACvB1E,yBAAyBA;4BACzB2E,4BAA4BA;4BAC5BC,gCAAgCA;4BAChClM,OAAOwC,IAAIxC,KAAK;4BAElBic,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACtR,aAC1B;4BACErC,QAAQpM,2BAA2B;4BACnCoK,SAASmP;4BACT3V,OAAOwC,IAAIxC,KAAK;wBAClB;wBAGF,wGAAwG;wBACxGwW,aAAaze,aAAa4jB,SAASK;oBACrC;oBAEA,OAAO;wBACLlL,iBAAiBsE;wBACjBjE,WAAWuE;wBACXxC,QAAQ,MAAM9a,wBAAwBoe,YAAY;4BAChDI,mBAAmB7a,gCACjB6Z,kBAAkBwG,eAAe,IACjC5Z,IAAIxC,KAAK,EACT0T;4BAEF+C;4BACArC;wBACF;wBACAzD,eAAe/T,qBACb8c,uBACAG;wBAEF,0CAA0C;wBAC1ClH,qBAAqB2I,0BAA0B3S,UAAU;wBACzDkK,iBAAiByI,0BAA0B1S,MAAM;wBACjD4J,gBAAgB8I,0BAA0BzS,KAAK;wBAC/CuJ,eAAekJ,0BAA0BxS,IAAI;oBAC/C;gBACF;YACF,OAAO;gBACL;;;;;;;;;;;;;;;;SAgBC,GAED,MAAMuT,QAAQxY,UAAU+N,gBAAgB;gBACxC,IAAI,CAACyK,OAAO;oBACV,MAAM,qBAEL,CAFK,IAAInR,MACR,kEADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,iEAAiE;gBACjE,yEAAyE;gBACzE,6EAA6E;gBAC7E,8EAA8E;gBAC9E,MAAMuN,mCAAmC,IAAIxQ;gBAE7C,4EAA4E;gBAC5E,gFAAgF;gBAChF,6EAA6E;gBAC7E,MAAMyQ,gCAAgC,IAAIzQ;gBAE1C,MAAME,cAAc,IAAI9J;gBACxB,MAAM0J,2BAA2BpJ;gBAEjC,MAAMga,8BAA+CvQ,iBAAiB;oBACpEtG,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAcmQ,8BAA8BlQ,MAAM;oBAClDC,YAAYgQ;oBACZtQ;oBACAO,iBAAiB;oBACjBC,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,MAAM6Q,0BAA0B,IAAI3Q;gBACpC,MAAM4Q,8BAA+CzQ,iBAAiB;oBACpEtG,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAcqQ,wBAAwBpQ,MAAM;oBAC5CC,YAAYmQ;oBACZzQ;oBACAO,iBAAiB;oBACjBC,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,0FAA0F;gBAC1F,wFAAwF;gBACxF,MAAM+Q,yBAAyB,MAAM1a,qBAAqBuI,GAAG,CAC3DgS,6BACAtP,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAGrB,IAAImW;gBACJ,IAAI;oBACFA,sBAAsB3a,qBAAqBuI,GAAG,CAC5CgS,6BACA9K,aAAapG,sBAAsB,EACnCqR,wBACAxR,wBAAwBI,aAAa,EACrC;wBACElB,SAAS,CAACF;4BACR,MAAMqF,SAASxR,2BAA2BmM;4BAE1C,IAAIqF,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IACE8M,iCAAiCjQ,MAAM,CAACwQ,OAAO,IAC/CN,8BAA8BlQ,MAAM,CAACwQ,OAAO,EAC5C;gCACA,mEAAmE;gCACnE,iEAAiE;gCACjE;4BACF,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;gCACAjb,0CACEqI,KACAzC,UAAU0D,KAAK;4BAEnB;wBACF;wBACAiB,QAAQkQ,8BAA8BlQ,MAAM;oBAC9C;gBAEJ,EAAE,OAAOlC,KAAc;oBACrB,IACEmS,iCAAiCjQ,MAAM,CAACwQ,OAAO,IAC/CN,8BAA8BlQ,MAAM,CAACwQ,OAAO,EAC5C;oBACA,4EAA4E;oBAC9E,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;wBACA,8EAA8E;wBAC9E,mFAAmF;wBACnFjb,0CAA0CqI,KAAKzC,UAAU0D,KAAK;oBAChE;gBACF;gBAEA,IAAIwR,qBAAqB;oBACvB,MAAM,CAACI,cAAcC,aAAa,GAAGL,oBAAoBzC,GAAG;oBAC5DyC,sBAAsB;oBACtB,gFAAgF;oBAChF,sBAAsB;oBACtB,MAAMM,mBAAmBF,cAAc7R;oBAEvC,MAAMgS,YAAYxM,QAAQ,yBACvBwM,SAAS;oBACZ,MAAMC,6BAA6Bnb,qBAAqBuI,GAAG,CACzDkS,6BACAS,yBACA,KAACxN;wBACCC,mBAAmBqN;wBACnBpN,gBAAgBA;wBAChB1E,yBAAyBA;wBACzB2E,4BAA4BA;wBAC5BC,gCAAgCA;wBAChClM,OAAOwC,IAAIxC,KAAK;wBAElB;wBACEwI,QAAQoQ,wBAAwBpQ,MAAM;wBACtChC,SAAS,CAACF;4BACR,MAAMqF,SAASxR,2BAA2BmM;4BAE1C,IAAIqF,QAAQ;gCACV,OAAOA;4BACT;4BAEA,IAAIiN,wBAAwBpQ,MAAM,CAACwQ,OAAO,EAAE;4BAC1C,4EAA4E;4BAC9E,OAAO,IACLpS,QAAQC,GAAG,CAACoS,gBAAgB,IAC5BrS,QAAQC,GAAG,CAACqS,sBAAsB,EAClC;gCACA,8EAA8E;gCAC9E,mFAAmF;gCACnFjb,0CACEqI,KACAzC,UAAU0D,KAAK;4BAEnB;wBACF;wBACA0P,kBAAkB;4BAAC9B;yBAAgB;oBACrC;oBAEFoE,2BAA2BC,KAAK,CAAC,CAAClT;wBAChC,IAAIsS,wBAAwBpQ,MAAM,CAACwQ,OAAO,EAAE;wBAC1C,2DAA2D;wBAC7D,OAAO;4BACL,uEAAuE;4BACvE,yCAAyC;4BACzC,IAAIpS,QAAQC,GAAG,CAACqS,sBAAsB,EAAE;gCACtCjb,0CAA0CqI,KAAKzC,UAAU0D,KAAK;4BAChE;wBACF;oBACF;gBACF;gBAEA,MAAMY,YAAYa,UAAU;gBAC5B,8DAA8D;gBAC9D,gEAAgE;gBAChE4P,wBAAwB3P,KAAK;gBAC7ByP,8BAA8BzP,KAAK;gBACnCwP,iCAAiCxP,KAAK;gBAEtC,sEAAsE;gBACtE,kFAAkF;gBAElF,IAAIoS,kBAAkB;gBACtB,MAAM5B,wBAAwB,IAAIxR;gBAClC,MAAMyR,wBAAwBnd,2BAC5BwH,WAAW6M,sBAAsB;gBAGnC,MAAM+I,4BAA6CvR,iBAAiB;oBAClEtG,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAckR,sBAAsBjR,MAAM;oBAC1CC,YAAYgR;oBACZ,uFAAuF;oBACvFtR,aAAa;oBACbO,iBAAiBgR;oBACjB/Q,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,IAAI2T,kBAAkB;gBACtB,MAAM9B,wBAAwB,IAAI3R;gBAClC,MAAM4R,wBAAwBtd,2BAC5BwH,WAAW6M,sBAAsB;gBAEnC,MAAMkJ,oBAAoBtd;gBAE1B,MAAMud,4BAA6C3R,iBAAiB;oBAClEtG,MAAM;oBACNuG,OAAO;oBACPP;oBACAQ,cAAcA;oBACdC,cAAcqR,sBAAsBpR,MAAM;oBAC1CC,YAAYmR;oBACZ,uFAAuF;oBACvFzR,aAAa;oBACbO,iBAAiBmR;oBACjBlR,YAAYnK;oBACZoK,QAAQpK;oBACRqK,OAAOrK;oBACPsK,MAAM;2BAAIR;qBAAa;oBACvBP;gBACF;gBAEA,MAAMiS,qBAAqB,MAAM5b,qBAAqBuI,GAAG,CACvDgT,2BACAtQ,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;gBAGrB,MAAMqX,8BAA+BW,6BACnC,MAAM7c,0BACJ0b,sBAAsBjR,MAAM,EAC5B,IACEpK,qBAAqBuI,GAAG,CACtBgT,2BACA9L,aAAapG,sBAAsB,EACnCuS,oBACA1S,wBAAwBI,aAAa,EACrC;wBACElB,SAAS,CAACF;4BACR,IAAImT,sBAAsBjR,MAAM,CAACwQ,OAAO,EAAE;gCACxCqC,kBAAkB;gCAClB,IAAI/e,4BAA4BgK,MAAM;oCACpC,OAAOA,IAAIqF,MAAM;gCACnB;gCACA,OAAOxR,2BAA2BmM;4BACpC;4BAEA,OAAOiP,6BAA6BjP;wBACtC;wBACAkC,QAAQiR,sBAAsBjR,MAAM;oBACtC,IAEJ;oBACEiR,sBAAsBxQ,KAAK;gBAC7B;gBAGJ,IAAIuN;gBACJ,MAAM2D,qBAAqBF,4BAA4BG,cAAc;gBACrE,IAAI;oBACF,MAAMd,YAAYxM,QAAQ,yBACvBwM,SAAS;oBACZ,MAAMxF,SAAS,MAAM9V,0BACnB,IACEI,qBAAqBuI,GAAG,CACtBoT,2BACAT,yBACA,KAACxN;4BACCC,mBAAmBoO;4BACnBnO,gBAAgBA;4BAChB1E,yBAAyBA;4BACzB2E,4BAA4BA;4BAC5BC,gCACEA;4BAEFlM,OAAOwC,IAAIxC,KAAK;4BAElB;4BACEwI,QAAQoR,sBAAsBpR,MAAM;4BACpChC,SAAS,CAACF,KAAc+T;gCACtB,IACE/d,4BAA4BgK,QAC5BsT,sBAAsBpR,MAAM,CAACwQ,OAAO,EACpC;oCACA0C,kBAAkB;oCAElB,MAAMlB,iBAAqC,AACzCH,UACAG,cAAc;oCAChB,IAAI,OAAOA,mBAAmB,UAAU;wCACtC9d,0BACEmH,UAAU0D,KAAK,EACfiT,gBACAV,mBACAJ,uBACAG;oCAEJ;oCACA;gCACF;gCAEA,OAAOlE,yBAAyBrP,KAAK+T;4BACvC;4BACApD,kBAAkB;gCAAC9B;6BAAgB;wBACrC,IAEJ;wBACEyE,sBAAsB3Q,KAAK;wBAC3BkR,mBAAmBM,eAAe;oBACpC;oBAEFjE,aAAa1C,OAAO6H,OAAO;gBAC7B,EAAE,OAAOrV,KAAK;oBACZ,IACEhK,4BAA4BgK,QAC5BsT,sBAAsBpR,MAAM,CAACwQ,OAAO,EACpC;oBACA,4FAA4F;oBAC9F,OAAO;wBACL,oDAAoD;wBACpD,MAAM1S;oBACR;gBACF;gBAEA3J,yBACEkH,UAAU0D,KAAK,EACfuS,mBACAJ,uBACAG;gBAGF,IAAIwB,mBAAmBK,iBAAiB;oBACtC,MAAMY,gBAAgBjB,kBAClB5e,sBAAsBid,yBACtBjd,sBAAsBod;oBAC1B,IAAIyC,eAAe;wBACjB,MAAM,qBAEL,CAFK,IAAI7e,mBACR,CAAC,OAAO,EAAEoG,UAAU0D,KAAK,CAAC,oDAAoD,EAAE+U,cAAc,4EAA4E,CAAC,GADvK,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF,OAAO;wBACL,MAAM,qBAEL,CAFK,IAAI7e,mBACR,CAAC,OAAO,EAAEoG,UAAU0D,KAAK,CAAC,0JAA0J,CAAC,GADjL,qBAAA;mCAAA;wCAAA;0CAAA;wBAEN;oBACF;gBACF;gBAEA,MAAMrE,aAAa,MAAM5K,eACvB2hB,4BAA4BkB,QAAQ;gBAEtCzL,SAASxM,UAAU,GAAGA;gBACtBwM,SAASkM,WAAW,GAAG,MAAMC,mBAC3B3Y,YACA6W,2BACAlM,cACA9J,YACAhD;gBAGF,MAAM0V,wBAAwBvb,0BAA0B;oBACtDsZ;oBACAL;oBACAuC,sBAAsBhB;oBACtBiB,UAAU5S,WAAW4S,QAAQ;oBAC7BtC,iBAAiBA;gBACnB;gBACA,MAAMgD,qBAAqBtT,WAAW0C,GAAG;gBACzC,OAAO;oBACLqK,iBAAiBsE;oBACjBjE,WAAWuE;oBACXxC,QAAQ,MAAMhb,mBAAmBse,YAAa;wBAC5CI,mBAAmB7a,gCACjBke,4BAA4BkB,QAAQ,IACpC3Y,IAAIxC,KAAK,EACT0T;wBAEF/N,oBAAoB;wBACpB8Q;wBACArC;wBACAiD;oBACF;oBACA1G,eAAe/T,qBACb8c,uBACAG;oBAEF,0CAA0C;oBAC1ClH,qBAAqBgH,0BAA0BhR,UAAU;oBACzDkK,iBAAiB8G,0BAA0B/Q,MAAM;oBACjD4J,gBAAgBmH,0BAA0B9Q,KAAK;oBAC/CuJ,eAAeuH,0BAA0B7Q,IAAI;gBAC/C;YACF;QACF,OAAO,IAAI/E,WAAWgD,YAAY,CAACnH,iBAAiB,EAAE;YACpD,uEAAuE;YACvE,IAAI8I,kBAAkBnM,2BACpBwH,WAAW6M,sBAAsB;YAGnC,MAAM7I,2BAA2BpJ;YACjC,MAAM4d,4BAA6CnU,iBAAiB;gBAClEtG,MAAM;gBACNuG,OAAO;gBACPP;gBACAQ,cAAcA;gBACdI;gBACAC,YAAYnK;gBACZoK,QAAQpK;gBACRqK,OAAOrK;gBACPsK,MAAM;uBAAIR;iBAAa;gBACvBP;YACF;YACA,MAAMrB,aAAa,MAAMtI,qBAAqBuI,GAAG,CAC/C4V,2BACAlT,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAMgT,oBAAqBgF,6BACzB,MAAM/c,2CACJO,qBAAqBuI,GAAG,CACtB4V,2BACA1O,aAAapG,sBAAsB,EACnC,4CAA4C;YAC5Cf,YACAY,wBAAwBI,aAAa,EACrC;gBACElB,SAAS+O;YACX;YAIN,MAAMiH,oBAAoC;gBACxC1a,MAAM;gBACNuG,OAAO;gBACPP;gBACAQ,cAAcA;gBACdI;gBACAC,YAAYnK;gBACZoK,QAAQpK;gBACRqK,OAAOrK;gBACPsK,MAAM;uBAAIR;iBAAa;gBACvBP;YACF;YACA,MAAMuR,YAAYxM,QAAQ,yBACvBwM,SAAS;YACZ,MAAM,EAAEqC,OAAO,EAAE9Q,SAAS,EAAE,GAAG,MAAMzM,qBAAqBuI,GAAG,CAC3D6V,mBACAlD,yBACA,KAACxN;gBACCC,mBAAmB6J,kBAAkBwF,iBAAiB;gBACtDpP,gBAAgBA;gBAChB1E,yBAAyBA;gBACzB2E,4BAA4BA;gBAC5BC,gCAAgCA;gBAChClM,OAAOwC,IAAIxC,KAAK;gBAElB;gBACEwG,SAASmP;gBACTmB,WAAW,CAAC5X;oBACVA,QAAQ4L,OAAO,CAAC,CAACzJ,OAAOF;wBACtB2U,aAAa3U,KAAKE;oBACpB;gBACF;gBACA0V,kBAAkBhT,WAAWiT,qBAAqB;gBAClDC,kBAAkB;oBAAC9B;iBAAgB;YACrC;YAEF,MAAMsB,wBAAwBvb,0BAA0B;gBACtDsZ;gBACAL;gBACAuC,sBAAsBhB;gBACtBiB,UAAU5S,WAAW4S,QAAQ;gBAC7BtC,iBAAiBA;YACnB;YAEA,+FAA+F;YAC/F,8FAA8F;YAC9F,6EAA6E;YAC7E,MAAMnR,aAAa,MAAM5K,eAAesd,kBAAkBuF,QAAQ;YAElE,IAAIR,+BAA+B9W,YAAY;gBAC7C6L,SAASxM,UAAU,GAAGA;gBACtBwM,SAASkM,WAAW,GAAG,MAAMC,mBAC3B3Y,YACAsZ,mBACA3O,cACA9J,YACAhD;YAEJ;YAEA;;;;;;;;;;;;;OAaC,GACD,oEAAoE;YACpE,IAAI5E,oBAAoBuM,gBAAgB+T,eAAe,GAAG;gBACxD,IAAI5R,aAAa,MAAM;oBACrB,qBAAqB;oBACrB6E,SAAS7E,SAAS,GAAG,MAAMlP,6BACzBkP,WACA9J,qBACAgH;gBAEJ,OAAO;oBACL,qBAAqB;oBACrB2H,SAAS7E,SAAS,GAAG,MAAMnP,6BACzBqM;gBAEJ;gBACA,mGAAmG;gBACnG,8GAA8G;gBAC9G,uHAAuH;gBACvH,sDAAsD;gBACtD6N,kBAAkBiB,OAAO;gBACzB,OAAO;oBACL/F,iBAAiBsE;oBACjBjE,WAAWuE;oBACXxC,QAAQ,MAAM/a,yBAAyBwjB,SAAS;wBAC9ClF;wBACArC;oBACF;oBACAzD,eAAejI,gBAAgB+T,eAAe;oBAC9C,0CAA0C;oBAC1C9J,qBAAqB4J,0BAA0B5T,UAAU;oBACzDkK,iBAAiB0J,0BAA0B3T,MAAM;oBACjD4J,gBAAgB+J,0BAA0B1T,KAAK;oBAC/CuJ,eAAemK,0BAA0BzT,IAAI;gBAC/C;YACF,OAAO,IAAI/H,uBAAuBA,oBAAoBgQ,IAAI,GAAG,GAAG;gBAC9D,+BAA+B;gBAC/BrB,SAAS7E,SAAS,GAAG,MAAMnP,6BACzBqM;gBAGF,OAAO;oBACL+I,iBAAiBsE;oBACjBjE,WAAWuE;oBACXxC,QAAQ,MAAM/a,yBAAyBwjB,SAAS;wBAC9ClF;wBACArC;oBACF;oBACAzD,eAAejI,gBAAgB+T,eAAe;oBAC9C,0CAA0C;oBAC1C9J,qBAAqB4J,0BAA0B5T,UAAU;oBACzDkK,iBAAiB0J,0BAA0B3T,MAAM;oBACjD4J,gBAAgB+J,0BAA0B1T,KAAK;oBAC/CuJ,eAAemK,0BAA0BzT,IAAI;gBAC/C;YACF,OAAO;gBACL,cAAc;gBACd,8GAA8G;gBAC9G,IAAIjF,UAAU0P,YAAY,EAAE;oBAC1B,MAAM,qBAEL,CAFK,IAAIvX,sBACR,qHADI,qBAAA;+BAAA;oCAAA;sCAAA;oBAEN;gBACF;gBAEA,IAAIwa,aAAamF;gBACjB,IAAI9Q,aAAa,MAAM;oBACrB,+FAA+F;oBAC/F,qGAAqG;oBACrG,MAAM0L,SAASzJ,QAAQ,yBACpByJ,MAAM;oBAET,qEAAqE;oBACrE,4EAA4E;oBAC5E,MAAMuF,gBAAgB,IAAIC;oBAE1B,MAAMC,eAAe,MAAMzF,qBACzB,KAACzK;wBACCC,mBAAmB+P;wBACnB9P,gBAAgB,KAAO;wBACvB1E,yBAAyBA;wBACzB2E,4BAA4BA;wBAC5BC,gCAAgCA;wBAChClM,OAAOwC,IAAIxC,KAAK;wBAElBic,KAAKC,KAAK,CAACD,KAAKE,SAAS,CAACtR,aAC1B;wBACErC,QAAQpM,2BAA2B;wBACnCoK,SAASmP;wBACT3V,OAAOwC,IAAIxC,KAAK;oBAClB;oBAGF,wGAAwG;oBACxGwW,aAAaze,aAAa4jB,SAASK;gBACrC;gBAEA,OAAO;oBACLlL,iBAAiBsE;oBACjBjE,WAAWuE;oBACXxC,QAAQ,MAAM9a,wBAAwBoe,YAAY;wBAChDI,mBAAmB7a,gCACjB6Z,kBAAkBwG,eAAe,IACjC5Z,IAAIxC,KAAK,EACT0T;wBAEF+C;wBACArC;oBACF;oBACAzD,eAAejI,gBAAgB+T,eAAe;oBAC9C,0CAA0C;oBAC1C9J,qBAAqB4J,0BAA0B5T,UAAU;oBACzDkK,iBAAiB0J,0BAA0B3T,MAAM;oBACjD4J,gBAAgB+J,0BAA0B1T,KAAK;oBAC/CuJ,eAAemK,0BAA0BzT,IAAI;gBAC/C;YACF;QACF,OAAO;YACL,MAAM4T,uBAAwCtU,iBAAiB;gBAC7DtG,MAAM;gBACNuG,OAAO;gBACPP;gBACAQ,cAAcA;gBACdK,YAAYnK;gBACZoK,QAAQpK;gBACRqK,OAAOrK;gBACPsK,MAAM;uBAAIR;iBAAa;YACzB;YACA,uFAAuF;YACvF,yEAAyE;YACzE,MAAM5B,aAAa,MAAMtI,qBAAqBuI,GAAG,CAC/C+V,sBACArT,eACAjG,MACAZ,KACAG,IAAIC,UAAU,KAAK;YAErB,MAAMgT,oBAAqBgF,6BACzB,MAAM/c,2CACJO,qBAAqBuI,GAAG,CACtB+V,sBACA7O,aAAapG,sBAAsB,EACnCf,YACAY,wBAAwBI,aAAa,EACrC;gBACElB,SAAS+O;YACX;YAIN,MAAM9N,yBAAyBqF,QAAQ,yBACpCrF,sBAAsB;YAEzB,MAAM+O,aAAa,MAAMpY,qBAAqBuI,GAAG,CAC/C+V,sBACAjV,sCACA,KAACqE;gBACCC,mBAAmB6J,kBAAkBwF,iBAAiB;gBACtDpP,gBAAgBA;gBAChB1E,yBAAyBA;gBACzB2E,4BAA4BA;gBAC5BC,gCAAgCA;gBAChClM,OAAOwC,IAAIxC,KAAK;gBAElB;gBACEwG,SAASmP;gBACT3V,OAAOwC,IAAIxC,KAAK;gBAChBiX,kBAAkB;oBAAC9B;iBAAgB;YACrC;YAGF,IAAIwF,+BAA+B9W,YAAY;gBAC7C,MAAMX,aAAa,MAAM5K,eAAesd,kBAAkBuF,QAAQ;gBAClEzL,SAASxM,UAAU,GAAGA;gBACtBwM,SAASkM,WAAW,GAAG,MAAMC,mBAC3B3Y,YACAwZ,sBACA7O,cACA9J,YACAhD;YAEJ;YAEA,MAAM0V,wBAAwBvb,0BAA0B;gBACtDsZ;gBACAL;gBACAuC,sBAAsBhB;gBACtBiB,UAAU5S,WAAW4S,QAAQ;gBAC7BtC,iBAAiBA;YACnB;YACA,OAAO;gBACLvD,iBAAiBsE;gBACjBjE,WAAWuE;gBACXxC,QAAQ,MAAMhb,mBAAmBse,YAAY;oBAC3CI,mBAAmB7a,gCACjB6Z,kBAAkBwG,eAAe,IACjC5Z,IAAIxC,KAAK,EACT0T;oBAEF/N,oBAAoB;oBACpB8Q;oBACArC;gBACF;gBACA,0CAA0C;gBAC1CzB,qBAAqB+J,qBAAqB/T,UAAU;gBACpDkK,iBAAiB6J,qBAAqB9T,MAAM;gBAC5C4J,gBAAgBkK,qBAAqB7T,KAAK;gBAC1CuJ,eAAesK,qBAAqB5T,IAAI;YAC1C;QACF;IACF,EAAE,OAAOxC,KAAK;QACZ,IACErK,wBAAwBqK,QACvB,OAAOA,QAAQ,YACdA,QAAQ,QACR,aAAaA,OACb,OAAOA,IAAImF,OAAO,KAAK,YACvBnF,IAAImF,OAAO,CAACzB,QAAQ,CAClB,iEAEJ;YACA,sDAAsD;YACtD,MAAM1D;QACR;QAEA,uEAAuE;QACvE,mEAAmE;QACnE,IAAIzK,qBAAqByK,MAAM;YAC7B,MAAMA;QACR;QAEA,wEAAwE;QACxE,uBAAuB;QACvB,MAAMgR,qBAAqB3c,oBAAoB2L;QAC/C,IAAIgR,oBAAoB;YACtB,MAAMzL,QAAQ3P,4BAA4BoK;YAC1CzL,MACE,GAAGyL,IAAIiR,MAAM,CAAC,mDAAmD,EAAE/U,IAAI1B,QAAQ,CAAC,kFAAkF,EAAE+K,OAAO;YAG7K,MAAMvF;QACR;QAEA,yEAAyE;QACzE,mDAAmD;QACnD,IAAIsU,+BAA+B,MAAM;YACvC,MAAMtU;QACR;QAEA,IAAIoD;QAEJ,IAAIpQ,0BAA0BgN,MAAM;YAClC3D,IAAIC,UAAU,GAAGvJ,4BAA4BiN;YAC7CoD,YAAYtQ,mCAAmCuJ,IAAIC,UAAU;QAC/D,OAAO,IAAInJ,gBAAgB6M,MAAM;YAC/BoD,YAAY;YACZ/G,IAAIC,UAAU,GAAGpJ,+BAA+B8M;YAEhD,MAAMkR,cAAcvc,cAClB1B,wBAAwB+M,MACxBvC,WAAW4S,QAAQ;YAGrBlE,UAAU,YAAY+E;QACxB,OAAO,IAAI,CAACF,oBAAoB;YAC9B3U,IAAIC,UAAU,GAAG;QACnB;QAEA,MAAM,CAACgV,qBAAqBC,qBAAqB,GAAG7c,mBAClD+I,WAAW0Q,aAAa,EACxBjS,IAAIgI,WAAW,EACfzG,WAAWkR,WAAW,EACtBlR,WAAWiR,4BAA4B,EACvC1Z,oBAAoBkH,KAAK,QACzBA,IAAIxC,KAAK,EACT;QAGF,MAAM0c,uBAAwCtU,iBAAiB;YAC7DtG,MAAM;YACNuG,OAAO;YACPP;YACAQ,cAAcA;YACdK,YACE,QAAOP,kCAAAA,eAAgBO,UAAU,MAAK,cAClCP,eAAeO,UAAU,GACzBnK;YACNoK,QACE,QAAOR,kCAAAA,eAAgBQ,MAAM,MAAK,cAC9BR,eAAeQ,MAAM,GACrBpK;YACNqK,OACE,QAAOT,kCAAAA,eAAgBS,KAAK,MAAK,cAC7BT,eAAeS,KAAK,GACpBrK;YACNsK,MAAM;mBAAKV,CAAAA,kCAAAA,eAAgBU,IAAI,KAAIR;aAAc;QACnD;QACA,MAAMwP,kBAAkB,MAAM1Z,qBAAqBuI,GAAG,CACpD+V,sBACA1R,oBACA5H,MACAZ,KACA4S,0BAA0B9T,GAAG,CAAC,AAACgF,IAAYqF,MAAM,IAAInM,YAAY8G,KACjEoD;QAGF,MAAMqO,oBAAoB3Z,qBAAqBuI,GAAG,CAChD+V,sBACA7O,aAAapG,sBAAsB,EACnCqQ,iBACAxQ,wBAAwBI,aAAa,EACrC;YACElB,SAAS+O;QACX;QAGF,IAAI;YACF,MAAMyC,aAAa,MAAMhgB,0BAA0B;gBACjDigB,gBAAgBnL,QAAQ;gBACxBoL,uBACE,KAAChL;oBACCnB,mBAAmBgM;oBACnB/L,gBAAgB4L;oBAChBtQ,yBAAyBA;oBACzBtH,OAAOwC,IAAIxC,KAAK;;gBAGpBmY,eAAe;oBACbnY,OAAOwC,IAAIxC,KAAK;oBAChB,wCAAwC;oBACxCiX,kBAAkB;wBAACY;qBAAqB;oBACxCnE;gBACF;YACF;YAEA,IAAIiH,+BAA+B9W,YAAY;gBAC7C,MAAMX,aAAa,MAAM5K,eACvBsiB,2BAA2BO,QAAQ;gBAErCzL,SAASxM,UAAU,GAAGA;gBACtBwM,SAASkM,WAAW,GAAG,MAAMC,mBAC3B3Y,YACAwZ,sBACA7O,cACA9J,YACAhD;YAEJ;YAEA,MAAMsW,qBAAqBtT,WAAW0C,GAAG;YAEzC,oEAAoE;YACpE,gEAAgE;YAChE,MAAMkW,eACJ/B,sCAAsCld,8BAClCkd,2BAA2BO,QAAQ,KACnCP,2BAA2BwB,eAAe;YAEhD,OAAO;gBACL,kEAAkE;gBAClE,8BAA8B;gBAC9BtL,iBAAiBsE;gBACjBjE,WAAWuE;gBACXxC,QAAQ,MAAMhb,mBAAmB8f,YAAY;oBAC3CpB,mBAAmB7a,gCACjB4gB,cACAna,IAAIxC,KAAK,EACT0T;oBAEF/N,oBAAoB;oBACpB8Q,uBAAuBvb,0BAA0B;wBAC/CsZ;wBACAL;wBACAuC,sBAAsB,EAAE;wBACxBC,UAAU5S,WAAW4S,QAAQ;wBAC7BtC,iBAAiBA;oBACnB;oBACAD;oBACAiD;gBACF;gBACA1G,eAAe;gBACfgC,qBACEvK,mBAAmB,OAAOA,eAAeO,UAAU,GAAGnK;gBACxDqU,iBACEzK,mBAAmB,OAAOA,eAAeQ,MAAM,GAAGpK;gBACpDgU,gBACEpK,mBAAmB,OAAOA,eAAeS,KAAK,GAAGrK;gBACnD4T,eAAehK,mBAAmB,OAAOA,eAAeU,IAAI,GAAG;YACjE;QACF,EAAE,OAAOsP,UAAe;YACtB,IACExR,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzBxN,0BAA0B8e,WAC1B;gBACA,MAAM,EAAEC,kBAAkB,EAAE,GAC1BvL,QAAQ;gBACVuL;YACF;YACA,MAAMD;QACR;IACF;AACF;AAEA,MAAMwE,gBAAuC,IAAI7X;AACjD,MAAM8X,iBAA+C,EAAE;AAEvD,SAASpO,kBAAkBqO,IAAsB;IAC/CF,cAAcG,GAAG,CAACD;IAClBA,KAAK/K,OAAO,CAAC;QACX,IAAI6K,cAActb,GAAG,CAACwb,OAAO;YAC3BF,cAAcI,MAAM,CAACF;YACrB,IAAIF,cAAc7L,IAAI,KAAK,GAAG;gBAC5B,uEAAuE;gBACvE,IAAK,IAAIpP,IAAI,GAAGA,IAAIkb,eAAezL,MAAM,EAAEzP,IAAK;oBAC9Ckb,cAAc,CAAClb,EAAE;gBACnB;gBACAkb,eAAezL,MAAM,GAAG;YAC1B;QACF;IACF;AACF;AAEA,OAAO,eAAeiI,mBACpBsD,YAAwC,EACxCrV,uBAA8D;IAE9D,IAAI2V;IACJ,IAAIrW,QAAQC,GAAG,CAACqW,SAAS,EAAE;QACzBD,2BACE,6DAA6D;QAC7DnQ,QAAQ,0CAA0CmQ,wBAAwB;IAC9E,OAAO;QACLA,2BACE,6DAA6D;QAC7DnQ,QAAQ,wCAAwCmQ,wBAAwB;IAC5E;IAEA,IAAI;QACFA,yBAAyBN,cAAc;YACrCQ,wBAAwB;gBACtBC,eAAe9V,wBAAwB8V,aAAa;gBACpDC,WAAW/V,wBAAwBgW,gBAAgB;gBACnD1N,iBAAiB;YACnB;QACF;IACF,EAAE,OAAM;IACN,8DAA8D;IAC9D,gEAAgE;IAChE,oCAAoC;IACtC;IAEA,0EAA0E;IAC1E,2EAA2E;IAC3EnB,kBAAkBtQ;IAClB,OAAO,IAAIuT,QAAQ,CAAC6L;QAClBV,eAAetC,IAAI,CAACgD;IACtB;AACF;AAEA,MAAMpT,uBAAuB,OAC3B/G,MACAZ;IAEA,MAAM,EACJgb,SAAS,EAAE,gBAAgBC,iBAAiB,EAAE,EAC/C,GAAG/e,gBAAgB0E;IAEpB,IAAI8G;IACJ,IAAIuT,mBAAmB;QACrB,MAAM,GAAGC,OAAO,GAAG,MAAMjf,gCAAgC;YACvD+D;YACAmb,UAAUF,iBAAiB,CAAC,EAAE;YAC9BG,cAAcH,iBAAiB,CAAC,EAAE;YAClC3Y,aAAa,IAAIC;YACjBC,YAAY,IAAID;QAClB;QACAmF,oBAAoBwT;IACtB;IAEA,OAAOxT;AACT;AAEA,eAAe2R,mBACbgC,kBAA0B,EAC1BzV,cAA8B,EAC9ByF,YAA2B,EAC3B9J,UAAsB,EACtBhD,mBAA+C;IAE/C,4BAA4B;IAC5B,EAAE;IACF,yEAAyE;IACzE,oEAAoE;IACpE,0EAA0E;IAC1E,2EAA2E;IAC3E,2EAA2E;IAC3E,wCAAwC;IACxC,EAAE;IACF,oEAAoE;IACpE,4EAA4E;IAC5E,iDAAiD;IAEjD,MAAMuG,0BAA0BvD,WAAWuD,uBAAuB;IAClE,IAAI,CAACA,2BAA2B,CAACvD,WAAWgD,YAAY,CAAC+W,kBAAkB,EAAE;QAC3E;IACF;IAEA,wEAAwE;IACxE,0DAA0D;IAC1D,MAAMC,gBAAgBnX,QAAQC,GAAG,CAAC+H,YAAY,KAAK;IACnD,MAAMuO,yBAAyB;QAC7B,2FAA2F;QAC3F,yFAAyF;QACzF,+CAA+C;QAC/CC,eAAe;QACfC,WAAWU,gBACPzW,wBAAwB0W,oBAAoB,GAC5C1W,wBAAwB2W,gBAAgB;QAC5CrO,iBAAiB;IACnB;IAEA,8EAA8E;IAC9E,0EAA0E;IAC1E,2EAA2E;IAC3E,sBAAsB;IACtB,EAAE;IACF,6EAA6E;IAC7E,mCAAmC;IACnC,EAAE;IACF,2EAA2E;IAC3E,6EAA6E;IAC7E,uEAAuE;IACvE,2EAA2E;IAC3E,6EAA6E;IAC7E,kBAAkB;IAClB,MAAMsO,0BACJna,WAAWgD,YAAY,CAACnH,iBAAiB,KAAK,QAAQ,iBAAiB;IACvE,CAACmE,WAAWgD,YAAY,CAACC,SAAS,CAAC,wBAAwB;;IAE7D,MAAMmX,YAAY/V,eAAeS,KAAK;IACtC,OAAO,MAAMgF,aAAagO,kBAAkB,CAC1CqC,yBACAL,oBACAM,WACA7W,wBAAwBI,aAAa,EACrCyV,wBACApc;AAEJ"}