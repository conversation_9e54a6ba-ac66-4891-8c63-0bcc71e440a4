{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "sourcesContent": ["import type { RequestD<PERSON>, FetchEventResult } from './types'\nimport type { RequestInit } from './spec-extension/request'\nimport { PageSignatureError } from './error'\nimport { fromNodeOutgoingHttpHeaders, normalizeNextQueryParam } from './utils'\nimport {\n  NextFetchEvent,\n  getWaitUntilPromiseFromEvent,\n} from './spec-extension/fetch-event'\nimport { NextRequest } from './spec-extension/request'\nimport { NextResponse } from './spec-extension/response'\nimport {\n  parseRelativeURL,\n  getRelativeURL,\n} from '../../shared/lib/router/utils/relativize-url'\nimport { NextURL } from './next-url'\nimport { stripInternalSearchParams } from '../internal-utils'\nimport { normalizeRscURL } from '../../shared/lib/router/utils/app-paths'\nimport {\n  FLIGHT_HEADERS,\n  NEXT_REWRITTEN_PATH_HEADER,\n  NEXT_REWRITTEN_QUERY_HEADER,\n  RSC_HEADER,\n} from '../../client/components/app-router-headers'\nimport { ensureInstrumentationRegistered } from './globals'\nimport { createRequestStoreForAPI } from '../async-storage/request-store'\nimport { workUnitAsyncStorage } from '../app-render/work-unit-async-storage.external'\nimport { createWorkStore } from '../async-storage/work-store'\nimport { workAsyncStorage } from '../app-render/work-async-storage.external'\nimport { NEXT_ROUTER_PREFETCH_HEADER } from '../../client/components/app-router-headers'\nimport { getTracer } from '../lib/trace/tracer'\nimport type { TextMapGetter } from 'next/dist/compiled/@opentelemetry/api'\nimport { MiddlewareSpan } from '../lib/trace/constants'\nimport { CloseController } from './web-on-close'\nimport { getEdgePreviewProps } from './get-edge-preview-props'\nimport { getBuiltinRequestContext } from '../after/builtin-request-context'\n\nexport class NextRequestHint extends NextRequest {\n  sourcePage: string\n  fetchMetrics: FetchEventResult['fetchMetrics'] | undefined\n\n  constructor(params: {\n    init: RequestInit\n    input: Request | string\n    page: string\n  }) {\n    super(params.input, params.init)\n    this.sourcePage = params.page\n  }\n\n  get request() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  respondWith() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n\n  waitUntil() {\n    throw new PageSignatureError({ page: this.sourcePage })\n  }\n}\n\nconst headersGetter: TextMapGetter<Headers> = {\n  keys: (headers) => Array.from(headers.keys()),\n  get: (headers, key) => headers.get(key) ?? undefined,\n}\n\nexport type AdapterOptions = {\n  handler: (req: NextRequestHint, event: NextFetchEvent) => Promise<Response>\n  page: string\n  request: RequestData\n  IncrementalCache?: typeof import('../lib/incremental-cache').IncrementalCache\n}\n\nlet propagator: <T>(request: NextRequestHint, fn: () => T) => T = (\n  request,\n  fn\n) => {\n  const tracer = getTracer()\n  return tracer.withPropagatedContext(request.headers, fn, headersGetter)\n}\n\nlet testApisIntercepted = false\n\nfunction ensureTestApisIntercepted() {\n  if (!testApisIntercepted) {\n    testApisIntercepted = true\n    if (process.env.NEXT_PRIVATE_TEST_PROXY === 'true') {\n      const {\n        interceptTestApis,\n        wrapRequestHandler,\n      } = require('next/dist/experimental/testmode/server-edge')\n      interceptTestApis()\n      propagator = wrapRequestHandler(propagator)\n    }\n  }\n}\n\nexport async function adapter(\n  params: AdapterOptions\n): Promise<FetchEventResult> {\n  ensureTestApisIntercepted()\n  await ensureInstrumentationRegistered()\n\n  // TODO-APP: use explicit marker for this\n  const isEdgeRendering =\n    typeof (globalThis as any).__BUILD_MANIFEST !== 'undefined'\n\n  params.request.url = normalizeRscURL(params.request.url)\n\n  const requestURL = new NextURL(params.request.url, {\n    headers: params.request.headers,\n    nextConfig: params.request.nextConfig,\n  })\n\n  // Iterator uses an index to keep track of the current iteration. Because of deleting and appending below we can't just use the iterator.\n  // Instead we use the keys before iteration.\n  const keys = [...requestURL.searchParams.keys()]\n  for (const key of keys) {\n    const value = requestURL.searchParams.getAll(key)\n\n    const normalizedKey = normalizeNextQueryParam(key)\n    if (normalizedKey) {\n      requestURL.searchParams.delete(normalizedKey)\n      for (const val of value) {\n        requestURL.searchParams.append(normalizedKey, val)\n      }\n      requestURL.searchParams.delete(key)\n    }\n  }\n\n  // Ensure users only see page requests, never data requests.\n  const buildId = requestURL.buildId\n  requestURL.buildId = ''\n\n  const requestHeaders = fromNodeOutgoingHttpHeaders(params.request.headers)\n  const isNextDataRequest = requestHeaders.has('x-nextjs-data')\n  const isRSCRequest = requestHeaders.get(RSC_HEADER) === '1'\n\n  if (isNextDataRequest && requestURL.pathname === '/index') {\n    requestURL.pathname = '/'\n  }\n\n  const flightHeaders = new Map()\n\n  // Headers should only be stripped for middleware\n  if (!isEdgeRendering) {\n    for (const header of FLIGHT_HEADERS) {\n      const key = header.toLowerCase()\n      const value = requestHeaders.get(key)\n      if (value !== null) {\n        flightHeaders.set(key, value)\n        requestHeaders.delete(key)\n      }\n    }\n  }\n\n  const normalizeURL = process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE\n    ? new URL(params.request.url)\n    : requestURL\n\n  const request = new NextRequestHint({\n    page: params.page,\n    // Strip internal query parameters off the request.\n    input: stripInternalSearchParams(normalizeURL).toString(),\n    init: {\n      body: params.request.body,\n      headers: requestHeaders,\n      method: params.request.method,\n      nextConfig: params.request.nextConfig,\n      signal: params.request.signal,\n    },\n  })\n\n  /**\n   * This allows to identify the request as a data request. The user doesn't\n   * need to know about this property neither use it. We add it for testing\n   * purposes.\n   */\n  if (isNextDataRequest) {\n    Object.defineProperty(request, '__isData', {\n      enumerable: false,\n      value: true,\n    })\n  }\n\n  if (\n    !(globalThis as any).__incrementalCache &&\n    (params as any).IncrementalCache\n  ) {\n    ;(globalThis as any).__incrementalCache = new (\n      params as any\n    ).IncrementalCache({\n      appDir: true,\n      fetchCache: true,\n      minimalMode: process.env.NODE_ENV !== 'development',\n      fetchCacheKeyPrefix: process.env.__NEXT_FETCH_CACHE_KEY_PREFIX,\n      dev: process.env.NODE_ENV === 'development',\n      requestHeaders: params.request.headers as any,\n      requestProtocol: 'https',\n      getPrerenderManifest: () => {\n        return {\n          version: -1 as any, // letting us know this doesn't conform to spec\n          routes: {},\n          dynamicRoutes: {},\n          notFoundRoutes: [],\n          preview: getEdgePreviewProps(),\n        }\n      },\n    })\n  }\n\n  // if we're in an edge runtime sandbox, we should use the waitUntil\n  // that we receive from the enclosing NextServer\n  const outerWaitUntil =\n    params.request.waitUntil ?? getBuiltinRequestContext()?.waitUntil\n\n  const event = new NextFetchEvent({\n    request,\n    page: params.page,\n    context: outerWaitUntil ? { waitUntil: outerWaitUntil } : undefined,\n  })\n  let response\n  let cookiesFromResponse\n\n  response = await propagator(request, () => {\n    // we only care to make async storage available for middleware\n    const isMiddleware =\n      params.page === '/middleware' || params.page === '/src/middleware'\n\n    if (isMiddleware) {\n      // if we're in an edge function, we only get a subset of `nextConfig` (no `experimental`),\n      // so we have to inject it via DefinePlugin.\n      // in `next start` this will be passed normally (see `NextNodeServer.runMiddleware`).\n\n      const waitUntil = event.waitUntil.bind(event)\n      const closeController = new CloseController()\n\n      return getTracer().trace(\n        MiddlewareSpan.execute,\n        {\n          spanName: `middleware ${request.method} ${request.nextUrl.pathname}`,\n          attributes: {\n            'http.target': request.nextUrl.pathname,\n            'http.method': request.method,\n          },\n        },\n        async () => {\n          try {\n            const onUpdateCookies = (cookies: Array<string>) => {\n              cookiesFromResponse = cookies\n            }\n            const previewProps = getEdgePreviewProps()\n\n            const requestStore = createRequestStoreForAPI(\n              request,\n              request.nextUrl,\n              undefined,\n              onUpdateCookies,\n              previewProps\n            )\n\n            const workStore = createWorkStore({\n              page: '/', // Fake Work\n              fallbackRouteParams: null,\n              renderOpts: {\n                cacheLifeProfiles:\n                  params.request.nextConfig?.experimental?.cacheLife,\n                experimental: {\n                  isRoutePPREnabled: false,\n                  dynamicIO: false,\n                  authInterrupts:\n                    !!params.request.nextConfig?.experimental?.authInterrupts,\n                },\n                supportsDynamicResponse: true,\n                waitUntil,\n                onClose: closeController.onClose.bind(closeController),\n                onAfterTaskError: undefined,\n              },\n              requestEndedState: { ended: false },\n              isPrefetchRequest: request.headers.has(\n                NEXT_ROUTER_PREFETCH_HEADER\n              ),\n              buildId: buildId ?? '',\n            })\n\n            return await workAsyncStorage.run(workStore, () =>\n              workUnitAsyncStorage.run(\n                requestStore,\n                params.handler,\n                request,\n                event\n              )\n            )\n          } finally {\n            // middleware cannot stream, so we can consider the response closed\n            // as soon as the handler returns.\n            // we can delay running it until a bit later --\n            // if it's needed, we'll have a `waitUntil` lock anyway.\n            setTimeout(() => {\n              closeController.dispatchClose()\n            }, 0)\n          }\n        }\n      )\n    }\n    return params.handler(request, event)\n  })\n\n  // check if response is a Response object\n  if (response && !(response instanceof Response)) {\n    throw new TypeError('Expected an instance of Response to be returned')\n  }\n\n  if (response && cookiesFromResponse) {\n    response.headers.set('set-cookie', cookiesFromResponse)\n  }\n\n  /**\n   * For rewrites we must always include the locale in the final pathname\n   * so we re-create the NextURL forcing it to include it when the it is\n   * an internal rewrite. Also we make sure the outgoing rewrite URL is\n   * a data URL if the request was a data request.\n   */\n  const rewrite = response?.headers.get('x-middleware-rewrite')\n  if (response && rewrite && (isRSCRequest || !isEdgeRendering)) {\n    const destination = new NextURL(rewrite, {\n      forceLocale: true,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE && !isEdgeRendering) {\n      if (destination.host === request.nextUrl.host) {\n        destination.buildId = buildId || destination.buildId\n        response.headers.set('x-middleware-rewrite', String(destination))\n      }\n    }\n\n    /**\n     * When the request is a data request we must show if there was a rewrite\n     * with an internal header so the client knows which component to load\n     * from the data request.\n     */\n    const { url: relativeDestination, isRelative } = parseRelativeURL(\n      destination.toString(),\n      requestURL.toString()\n    )\n\n    if (\n      !isEdgeRendering &&\n      isNextDataRequest &&\n      // if the rewrite is external and external rewrite\n      // resolving config is enabled don't add this header\n      // so the upstream app can set it instead\n      !(\n        process.env.__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE &&\n        relativeDestination.match(/http(s)?:\\/\\//)\n      )\n    ) {\n      response.headers.set('x-nextjs-rewrite', relativeDestination)\n    }\n\n    // If this is an RSC request, and the pathname or search has changed, and\n    // this isn't an external rewrite, we need to set the rewritten pathname and\n    // query headers.\n    if (isRSCRequest && isRelative) {\n      if (requestURL.pathname !== destination.pathname) {\n        response.headers.set(NEXT_REWRITTEN_PATH_HEADER, destination.pathname)\n      }\n      if (requestURL.search !== destination.search) {\n        response.headers.set(\n          NEXT_REWRITTEN_QUERY_HEADER,\n          // remove the leading ? from the search string\n          destination.search.slice(1)\n        )\n      }\n    }\n  }\n\n  /**\n   * For redirects we will not include the locale in case when it is the\n   * default and we must also make sure the outgoing URL is a data one if\n   * the incoming request was a data request.\n   */\n  const redirect = response?.headers.get('Location')\n  if (response && redirect && !isEdgeRendering) {\n    const redirectURL = new NextURL(redirect, {\n      forceLocale: false,\n      headers: params.request.headers,\n      nextConfig: params.request.nextConfig,\n    })\n\n    /**\n     * Responses created from redirects have immutable headers so we have\n     * to clone the response to be able to modify it.\n     */\n    response = new Response(response.body, response)\n\n    if (!process.env.__NEXT_NO_MIDDLEWARE_URL_NORMALIZE) {\n      if (redirectURL.host === requestURL.host) {\n        redirectURL.buildId = buildId || redirectURL.buildId\n        response.headers.set('Location', redirectURL.toString())\n      }\n    }\n\n    /**\n     * When the request is a data request we can't use the location header as\n     * it may end up with CORS error. Instead we map to an internal header so\n     * the client knows the destination.\n     */\n    if (isNextDataRequest) {\n      response.headers.delete('Location')\n      response.headers.set(\n        'x-nextjs-redirect',\n        getRelativeURL(redirectURL.toString(), requestURL.toString())\n      )\n    }\n  }\n\n  const finalResponse = response ? response : NextResponse.next()\n\n  // Flight headers are not overridable / removable so they are applied at the end.\n  const middlewareOverrideHeaders = finalResponse.headers.get(\n    'x-middleware-override-headers'\n  )\n  const overwrittenHeaders: string[] = []\n  if (middlewareOverrideHeaders) {\n    for (const [key, value] of flightHeaders) {\n      finalResponse.headers.set(`x-middleware-request-${key}`, value)\n      overwrittenHeaders.push(key)\n    }\n\n    if (overwrittenHeaders.length > 0) {\n      finalResponse.headers.set(\n        'x-middleware-override-headers',\n        middlewareOverrideHeaders + ',' + overwrittenHeaders.join(',')\n      )\n    }\n  }\n\n  return {\n    response: finalResponse,\n    waitUntil: getWaitUntilPromiseFromEvent(event) ?? Promise.resolve(),\n    fetchMetrics: request.fetchMetrics,\n  }\n}\n"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "normalizeNextQueryParam", "NextFetchEvent", "getWaitUntilPromiseFromEvent", "NextRequest", "NextResponse", "parseRelativeURL", "getRelativeURL", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "FLIGHT_HEADERS", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "RSC_HEADER", "ensureInstrumentationRegistered", "createRequestStoreForAPI", "workUnitAsyncStorage", "createWorkStore", "workAsyncStorage", "NEXT_ROUTER_PREFETCH_HEADER", "getTracer", "MiddlewareSpan", "CloseController", "getEdgePreviewProps", "getBuiltinRequestContext", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "adapter", "isEdgeRendering", "globalThis", "__BUILD_MANIFEST", "url", "requestURL", "nextConfig", "searchParams", "value", "getAll", "normalizedKey", "delete", "val", "append", "buildId", "requestHeaders", "isNextDataRequest", "has", "isRSCRequest", "pathname", "flightHeaders", "Map", "header", "toLowerCase", "set", "normalizeURL", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "toString", "body", "method", "signal", "Object", "defineProperty", "enumerable", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "outerWaitUntil", "event", "context", "response", "cookiesFromResponse", "isMiddleware", "bind", "closeController", "trace", "execute", "spanName", "nextUrl", "attributes", "onUpdateCookies", "cookies", "previewProps", "requestStore", "workStore", "fallbackRouteParams", "renderOpts", "cacheLifeProfiles", "experimental", "cacheLife", "isRoutePPREnabled", "dynamicIO", "authInterrupts", "supportsDynamicResponse", "onClose", "onAfterTaskError", "requestEndedState", "ended", "isPrefetchRequest", "run", "handler", "setTimeout", "dispatchClose", "Response", "TypeError", "rewrite", "destination", "forceLocale", "host", "String", "relativeDestination", "isRelative", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "search", "slice", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "length", "join", "Promise", "resolve", "fetchMetrics"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,EAAEC,uBAAuB,QAAQ,UAAS;AAC9E,SACEC,cAAc,EACdC,4BAA4B,QACvB,+BAA8B;AACrC,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SACEC,gBAAgB,EAChBC,cAAc,QACT,+CAA8C;AACrD,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SACEC,cAAc,EACdC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,UAAU,QACL,6CAA4C;AACnD,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,wBAAwB,QAAQ,iCAAgC;AACzE,SAASC,oBAAoB,QAAQ,iDAAgD;AACrF,SAASC,eAAe,QAAQ,8BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,4CAA2C;AAC5E,SAASC,2BAA2B,QAAQ,6CAA4C;AACxF,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,eAAe,QAAQ,iBAAgB;AAChD,SAASC,mBAAmB,QAAQ,2BAA0B;AAC9D,SAASC,wBAAwB,QAAQ,mCAAkC;AAE3E,OAAO,MAAMC,wBAAwBtB;IAInCuB,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,qBAAiD,CAAjD,IAAIlC,mBAAmB;YAAEiC,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAG,cAAc;QACZ,MAAM,qBAAiD,CAAjD,IAAInC,mBAAmB;YAAEiC,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;IAEAI,YAAY;QACV,MAAM,qBAAiD,CAAjD,IAAIpC,mBAAmB;YAAEiC,MAAM,IAAI,CAACD,UAAU;QAAC,IAA/C,qBAAA;mBAAA;wBAAA;0BAAA;QAAgD;IACxD;AACF;AAEA,MAAMK,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEX,SACAY;IAEA,MAAMC,SAASzB;IACf,OAAOyB,OAAOC,qBAAqB,CAACd,QAAQK,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIY,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAT,aAAaU,mBAAmBV;QAClC;IACF;AACF;AAEA,OAAO,eAAeY,QACpB5B,MAAsB;QAoHQH;IAlH9BwB;IACA,MAAMlC;IAEN,yCAAyC;IACzC,MAAM0C,kBACJ,OAAO,AAACC,WAAmBC,gBAAgB,KAAK;IAElD/B,OAAOK,OAAO,CAAC2B,GAAG,GAAGlD,gBAAgBkB,OAAOK,OAAO,CAAC2B,GAAG;IAEvD,MAAMC,aAAa,IAAIrD,QAAQoB,OAAOK,OAAO,CAAC2B,GAAG,EAAE;QACjDtB,SAASV,OAAOK,OAAO,CAACK,OAAO;QAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAMzB,OAAO;WAAIwB,WAAWE,YAAY,CAAC1B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM2B,QAAQH,WAAWE,YAAY,CAACE,MAAM,CAACvB;QAE7C,MAAMwB,gBAAgBjE,wBAAwByC;QAC9C,IAAIwB,eAAe;YACjBL,WAAWE,YAAY,CAACI,MAAM,CAACD;YAC/B,KAAK,MAAME,OAAOJ,MAAO;gBACvBH,WAAWE,YAAY,CAACM,MAAM,CAACH,eAAeE;YAChD;YACAP,WAAWE,YAAY,CAACI,MAAM,CAACzB;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAM4B,UAAUT,WAAWS,OAAO;IAClCT,WAAWS,OAAO,GAAG;IAErB,MAAMC,iBAAiBvE,4BAA4B4B,OAAOK,OAAO,CAACK,OAAO;IACzE,MAAMkC,oBAAoBD,eAAeE,GAAG,CAAC;IAC7C,MAAMC,eAAeH,eAAe9B,GAAG,CAAC3B,gBAAgB;IAExD,IAAI0D,qBAAqBX,WAAWc,QAAQ,KAAK,UAAU;QACzDd,WAAWc,QAAQ,GAAG;IACxB;IAEA,MAAMC,gBAAgB,IAAIC;IAE1B,iDAAiD;IACjD,IAAI,CAACpB,iBAAiB;QACpB,KAAK,MAAMqB,UAAUnE,eAAgB;YACnC,MAAM+B,MAAMoC,OAAOC,WAAW;YAC9B,MAAMf,QAAQO,eAAe9B,GAAG,CAACC;YACjC,IAAIsB,UAAU,MAAM;gBAClBY,cAAcI,GAAG,CAACtC,KAAKsB;gBACvBO,eAAeJ,MAAM,CAACzB;YACxB;QACF;IACF;IAEA,MAAMuC,eAAe/B,QAAQC,GAAG,CAAC+B,kCAAkC,GAC/D,IAAIC,IAAIvD,OAAOK,OAAO,CAAC2B,GAAG,IAC1BC;IAEJ,MAAM5B,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOpB,0BAA0BwE,cAAcG,QAAQ;QACvDtD,MAAM;YACJuD,MAAMzD,OAAOK,OAAO,CAACoD,IAAI;YACzB/C,SAASiC;YACTe,QAAQ1D,OAAOK,OAAO,CAACqD,MAAM;YAC7BxB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;YACrCyB,QAAQ3D,OAAOK,OAAO,CAACsD,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIf,mBAAmB;QACrBgB,OAAOC,cAAc,CAACxD,SAAS,YAAY;YACzCyD,YAAY;YACZ1B,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAACN,WAAmBiC,kBAAkB,IACvC,AAAC/D,OAAegE,gBAAgB,EAChC;;QACElC,WAAmBiC,kBAAkB,GAAG,IAAI,AAC5C/D,OACAgE,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAa7C,QAAQC,GAAG,CAAC6C,QAAQ,KAAK;YACtCC,qBAAqB/C,QAAQC,GAAG,CAAC+C,6BAA6B;YAC9DC,KAAKjD,QAAQC,GAAG,CAAC6C,QAAQ,KAAK;YAC9BzB,gBAAgB3C,OAAOK,OAAO,CAACK,OAAO;YACtC8D,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASlF;gBACX;YACF;QACF;IACF;IAEA,mEAAmE;IACnE,gDAAgD;IAChD,MAAMmF,iBACJ/E,OAAOK,OAAO,CAACE,SAAS,MAAIV,4BAAAA,+CAAAA,0BAA4BU,SAAS;IAEnE,MAAMyE,QAAQ,IAAI1G,eAAe;QAC/B+B;QACAD,MAAMJ,OAAOI,IAAI;QACjB6E,SAASF,iBAAiB;YAAExE,WAAWwE;QAAe,IAAIhE;IAC5D;IACA,IAAImE;IACJ,IAAIC;IAEJD,WAAW,MAAMlE,WAAWX,SAAS;QACnC,8DAA8D;QAC9D,MAAM+E,eACJpF,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QAEnD,IAAIgF,cAAc;YAChB,0FAA0F;YAC1F,4CAA4C;YAC5C,qFAAqF;YAErF,MAAM7E,YAAYyE,MAAMzE,SAAS,CAAC8E,IAAI,CAACL;YACvC,MAAMM,kBAAkB,IAAI3F;YAE5B,OAAOF,YAAY8F,KAAK,CACtB7F,eAAe8F,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAEpF,QAAQqD,MAAM,CAAC,CAAC,EAAErD,QAAQqF,OAAO,CAAC3C,QAAQ,EAAE;gBACpE4C,YAAY;oBACV,eAAetF,QAAQqF,OAAO,CAAC3C,QAAQ;oBACvC,eAAe1C,QAAQqD,MAAM;gBAC/B;YACF,GACA;gBACE,IAAI;wBAmBI1D,yCAAAA,4BAKIA,0CAAAA;oBAvBV,MAAM4F,kBAAkB,CAACC;wBACvBV,sBAAsBU;oBACxB;oBACA,MAAMC,eAAelG;oBAErB,MAAMmG,eAAe3G,yBACnBiB,SACAA,QAAQqF,OAAO,EACf3E,WACA6E,iBACAE;oBAGF,MAAME,YAAY1G,gBAAgB;wBAChCc,MAAM;wBACN6F,qBAAqB;wBACrBC,YAAY;4BACVC,iBAAiB,GACfnG,6BAAAA,OAAOK,OAAO,CAAC6B,UAAU,sBAAzBlC,0CAAAA,2BAA2BoG,YAAY,qBAAvCpG,wCAAyCqG,SAAS;4BACpDD,cAAc;gCACZE,mBAAmB;gCACnBC,WAAW;gCACXC,gBACE,CAAC,GAACxG,8BAAAA,OAAOK,OAAO,CAAC6B,UAAU,sBAAzBlC,2CAAAA,4BAA2BoG,YAAY,qBAAvCpG,yCAAyCwG,cAAc;4BAC7D;4BACAC,yBAAyB;4BACzBlG;4BACAmG,SAASpB,gBAAgBoB,OAAO,CAACrB,IAAI,CAACC;4BACtCqB,kBAAkB5F;wBACpB;wBACA6F,mBAAmB;4BAAEC,OAAO;wBAAM;wBAClCC,mBAAmBzG,QAAQK,OAAO,CAACmC,GAAG,CACpCrD;wBAEFkD,SAASA,WAAW;oBACtB;oBAEA,OAAO,MAAMnD,iBAAiBwH,GAAG,CAACf,WAAW,IAC3C3G,qBAAqB0H,GAAG,CACtBhB,cACA/F,OAAOgH,OAAO,EACd3G,SACA2E;gBAGN,SAAU;oBACR,mEAAmE;oBACnE,kCAAkC;oBAClC,+CAA+C;oBAC/C,wDAAwD;oBACxDiC,WAAW;wBACT3B,gBAAgB4B,aAAa;oBAC/B,GAAG;gBACL;YACF;QAEJ;QACA,OAAOlH,OAAOgH,OAAO,CAAC3G,SAAS2E;IACjC;IAEA,yCAAyC;IACzC,IAAIE,YAAY,CAAEA,CAAAA,oBAAoBiC,QAAO,GAAI;QAC/C,MAAM,qBAAgE,CAAhE,IAAIC,UAAU,oDAAd,qBAAA;mBAAA;wBAAA;0BAAA;QAA+D;IACvE;IAEA,IAAIlC,YAAYC,qBAAqB;QACnCD,SAASxE,OAAO,CAAC0C,GAAG,CAAC,cAAc+B;IACrC;IAEA;;;;;GAKC,GACD,MAAMkC,UAAUnC,4BAAAA,SAAUxE,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIqE,YAAYmC,WAAYvE,CAAAA,gBAAgB,CAACjB,eAAc,GAAI;QAC7D,MAAMyF,cAAc,IAAI1I,QAAQyI,SAAS;YACvCE,aAAa;YACb7G,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;QACvC;QAEA,IAAI,CAACZ,QAAQC,GAAG,CAAC+B,kCAAkC,IAAI,CAACzB,iBAAiB;YACvE,IAAIyF,YAAYE,IAAI,KAAKnH,QAAQqF,OAAO,CAAC8B,IAAI,EAAE;gBAC7CF,YAAY5E,OAAO,GAAGA,WAAW4E,YAAY5E,OAAO;gBACpDwC,SAASxE,OAAO,CAAC0C,GAAG,CAAC,wBAAwBqE,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAM,EAAEtF,KAAK0F,mBAAmB,EAAEC,UAAU,EAAE,GAAGjJ,iBAC/C4I,YAAY9D,QAAQ,IACpBvB,WAAWuB,QAAQ;QAGrB,IACE,CAAC3B,mBACDe,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACEtB,CAAAA,QAAQC,GAAG,CAACqG,0CAA0C,IACtDF,oBAAoBG,KAAK,CAAC,gBAAe,GAE3C;YACA3C,SAASxE,OAAO,CAAC0C,GAAG,CAAC,oBAAoBsE;QAC3C;QAEA,yEAAyE;QACzE,4EAA4E;QAC5E,iBAAiB;QACjB,IAAI5E,gBAAgB6E,YAAY;YAC9B,IAAI1F,WAAWc,QAAQ,KAAKuE,YAAYvE,QAAQ,EAAE;gBAChDmC,SAASxE,OAAO,CAAC0C,GAAG,CAACpE,4BAA4BsI,YAAYvE,QAAQ;YACvE;YACA,IAAId,WAAW6F,MAAM,KAAKR,YAAYQ,MAAM,EAAE;gBAC5C5C,SAASxE,OAAO,CAAC0C,GAAG,CAClBnE,6BACA,8CAA8C;gBAC9CqI,YAAYQ,MAAM,CAACC,KAAK,CAAC;YAE7B;QACF;IACF;IAEA;;;;GAIC,GACD,MAAMC,WAAW9C,4BAAAA,SAAUxE,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIqE,YAAY8C,YAAY,CAACnG,iBAAiB;QAC5C,MAAMoG,cAAc,IAAIrJ,QAAQoJ,UAAU;YACxCT,aAAa;YACb7G,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;QACvC;QAEA;;;KAGC,GACDgD,WAAW,IAAIiC,SAASjC,SAASzB,IAAI,EAAEyB;QAEvC,IAAI,CAAC5D,QAAQC,GAAG,CAAC+B,kCAAkC,EAAE;YACnD,IAAI2E,YAAYT,IAAI,KAAKvF,WAAWuF,IAAI,EAAE;gBACxCS,YAAYvF,OAAO,GAAGA,WAAWuF,YAAYvF,OAAO;gBACpDwC,SAASxE,OAAO,CAAC0C,GAAG,CAAC,YAAY6E,YAAYzE,QAAQ;YACvD;QACF;QAEA;;;;KAIC,GACD,IAAIZ,mBAAmB;YACrBsC,SAASxE,OAAO,CAAC6B,MAAM,CAAC;YACxB2C,SAASxE,OAAO,CAAC0C,GAAG,CAClB,qBACAzE,eAAesJ,YAAYzE,QAAQ,IAAIvB,WAAWuB,QAAQ;QAE9D;IACF;IAEA,MAAM0E,gBAAgBhD,WAAWA,WAAWzG,aAAa0J,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAcxH,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMwH,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACtH,KAAKsB,MAAM,IAAIY,cAAe;YACxCkF,cAAcxH,OAAO,CAAC0C,GAAG,CAAC,CAAC,qBAAqB,EAAEtC,KAAK,EAAEsB;YACzDiG,mBAAmBC,IAAI,CAACxH;QAC1B;QAEA,IAAIuH,mBAAmBE,MAAM,GAAG,GAAG;YACjCL,cAAcxH,OAAO,CAAC0C,GAAG,CACvB,iCACAgF,4BAA4B,MAAMC,mBAAmBG,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLtD,UAAUgD;QACV3H,WAAWhC,6BAA6ByG,UAAUyD,QAAQC,OAAO;QACjEC,cAActI,QAAQsI,YAAY;IACpC;AACF"}