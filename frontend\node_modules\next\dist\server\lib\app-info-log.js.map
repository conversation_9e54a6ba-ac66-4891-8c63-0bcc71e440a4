{"version": 3, "sources": ["../../../src/server/lib/app-info-log.ts"], "sourcesContent": ["import { loadEnvConfig } from '@next/env'\nimport * as Log from '../../build/output/log'\nimport { bold, purple } from '../../lib/picocolors'\nimport {\n  PHASE_DEVELOPMENT_SERVER,\n  PHASE_PRODUCTION_BUILD,\n} from '../../shared/lib/constants'\nimport loadConfig, {\n  getConfiguredExperimentalFeatures,\n  type ConfiguredExperimentalFeature,\n} from '../config'\n\nexport function logStartInfo({\n  networkUrl,\n  appUrl,\n  envInfo,\n  experimentalFeatures,\n  maxExperimentalFeatures = Infinity,\n}: {\n  networkUrl: string | null\n  appUrl: string | null\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n  maxExperimentalFeatures?: number\n}) {\n  Log.bootstrap(\n    `${bold(\n      purple(`${Log.prefixes.ready} Next.js ${process.env.__NEXT_VERSION}`)\n    )}${process.env.TURBOPACK ? ' (Turbopack)' : ''}`\n  )\n  if (appUrl) {\n    Log.bootstrap(`- Local:        ${appUrl}`)\n  }\n  if (networkUrl) {\n    Log.bootstrap(`- Network:      ${networkUrl}`)\n  }\n  if (envInfo?.length) Log.bootstrap(`- Environments: ${envInfo.join(', ')}`)\n\n  if (experimentalFeatures?.length) {\n    Log.bootstrap(`- Experiments (use with caution):`)\n    // only show a maximum number of flags\n    for (const exp of experimentalFeatures.slice(0, maxExperimentalFeatures)) {\n      const symbol =\n        exp.type === 'boolean'\n          ? exp.value === true\n            ? bold('✓')\n            : bold('⨯')\n          : '·'\n\n      const suffix = exp.type === 'number' ? `: ${exp.value}` : ''\n\n      Log.bootstrap(`  ${symbol} ${exp.name}${suffix}`)\n    }\n    /* indicate if there are more than the maximum shown no. flags */\n    if (experimentalFeatures.length > maxExperimentalFeatures) {\n      Log.bootstrap(`  · ...`)\n    }\n  }\n\n  // New line after the bootstrap info\n  Log.info('')\n}\n\nexport async function getStartServerInfo(\n  dir: string,\n  dev: boolean\n): Promise<{\n  envInfo?: string[]\n  experimentalFeatures?: ConfiguredExperimentalFeature[]\n}> {\n  let experimentalFeatures: ConfiguredExperimentalFeature[] = []\n  await loadConfig(\n    dev ? PHASE_DEVELOPMENT_SERVER : PHASE_PRODUCTION_BUILD,\n    dir,\n    {\n      onLoadUserConfig(userConfig) {\n        const configuredExperimentalFeatures =\n          getConfiguredExperimentalFeatures(userConfig.experimental)\n\n        experimentalFeatures = configuredExperimentalFeatures.sort(\n          ({ name: a }, { name: b }) => a.length - b.length\n        )\n      },\n    }\n  )\n\n  // we need to reset env if we are going to create\n  // the worker process with the esm loader so that the\n  // initial env state is correct\n  let envInfo: string[] = []\n  const { loadedEnvFiles } = loadEnvConfig(dir, true, console, false)\n  if (loadedEnvFiles.length > 0) {\n    envInfo = loadedEnvFiles.map((f) => f.path)\n  }\n\n  return {\n    envInfo,\n    experimentalFeatures,\n  }\n}\n"], "names": ["getStartServerInfo", "logStartInfo", "networkUrl", "appUrl", "envInfo", "experimentalFeatures", "maxExperimentalFeatures", "Infinity", "Log", "bootstrap", "bold", "purple", "prefixes", "ready", "process", "env", "__NEXT_VERSION", "TURBOPACK", "length", "join", "exp", "slice", "symbol", "type", "value", "suffix", "name", "info", "dir", "dev", "loadConfig", "PHASE_DEVELOPMENT_SERVER", "PHASE_PRODUCTION_BUILD", "onLoadUserConfig", "userConfig", "configuredExperimentalFeatures", "getConfiguredExperimentalFeatures", "experimental", "sort", "a", "b", "loadedEnvFiles", "loadEnvConfig", "console", "map", "f", "path"], "mappings": ";;;;;;;;;;;;;;;IA+DsBA,kBAAkB;eAAlBA;;IAnDNC,YAAY;eAAZA;;;qBAZc;6DACT;4BACQ;2BAItB;gEAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,SAASA,aAAa,EAC3BC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,oBAAoB,EACpBC,0BAA0BC,QAAQ,EAOnC;IACCC,KAAIC,SAAS,CACX,GAAGC,IAAAA,gBAAI,EACLC,IAAAA,kBAAM,EAAC,GAAGH,KAAII,QAAQ,CAACC,KAAK,CAAC,SAAS,EAAEC,QAAQC,GAAG,CAACC,cAAc,EAAE,KAClEF,QAAQC,GAAG,CAACE,SAAS,GAAG,iBAAiB,IAAI;IAEnD,IAAId,QAAQ;QACVK,KAAIC,SAAS,CAAC,CAAC,gBAAgB,EAAEN,QAAQ;IAC3C;IACA,IAAID,YAAY;QACdM,KAAIC,SAAS,CAAC,CAAC,gBAAgB,EAAEP,YAAY;IAC/C;IACA,IAAIE,2BAAAA,QAASc,MAAM,EAAEV,KAAIC,SAAS,CAAC,CAAC,gBAAgB,EAAEL,QAAQe,IAAI,CAAC,OAAO;IAE1E,IAAId,wCAAAA,qBAAsBa,MAAM,EAAE;QAChCV,KAAIC,SAAS,CAAC,CAAC,iCAAiC,CAAC;QACjD,sCAAsC;QACtC,KAAK,MAAMW,OAAOf,qBAAqBgB,KAAK,CAAC,GAAGf,yBAA0B;YACxE,MAAMgB,SACJF,IAAIG,IAAI,KAAK,YACTH,IAAII,KAAK,KAAK,OACZd,IAAAA,gBAAI,EAAC,OACLA,IAAAA,gBAAI,EAAC,OACP;YAEN,MAAMe,SAASL,IAAIG,IAAI,KAAK,WAAW,CAAC,EAAE,EAAEH,IAAII,KAAK,EAAE,GAAG;YAE1DhB,KAAIC,SAAS,CAAC,CAAC,EAAE,EAAEa,OAAO,CAAC,EAAEF,IAAIM,IAAI,GAAGD,QAAQ;QAClD;QACA,+DAA+D,GAC/D,IAAIpB,qBAAqBa,MAAM,GAAGZ,yBAAyB;YACzDE,KAAIC,SAAS,CAAC,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,oCAAoC;IACpCD,KAAImB,IAAI,CAAC;AACX;AAEO,eAAe3B,mBACpB4B,GAAW,EACXC,GAAY;IAKZ,IAAIxB,uBAAwD,EAAE;IAC9D,MAAMyB,IAAAA,eAAU,EACdD,MAAME,mCAAwB,GAAGC,iCAAsB,EACvDJ,KACA;QACEK,kBAAiBC,UAAU;YACzB,MAAMC,iCACJC,IAAAA,yCAAiC,EAACF,WAAWG,YAAY;YAE3DhC,uBAAuB8B,+BAA+BG,IAAI,CACxD,CAAC,EAAEZ,MAAMa,CAAC,EAAE,EAAE,EAAEb,MAAMc,CAAC,EAAE,GAAKD,EAAErB,MAAM,GAAGsB,EAAEtB,MAAM;QAErD;IACF;IAGF,iDAAiD;IACjD,qDAAqD;IACrD,+BAA+B;IAC/B,IAAId,UAAoB,EAAE;IAC1B,MAAM,EAAEqC,cAAc,EAAE,GAAGC,IAAAA,kBAAa,EAACd,KAAK,MAAMe,SAAS;IAC7D,IAAIF,eAAevB,MAAM,GAAG,GAAG;QAC7Bd,UAAUqC,eAAeG,GAAG,CAAC,CAACC,IAAMA,EAAEC,IAAI;IAC5C;IAEA,OAAO;QACL1C;QACAC;IACF;AACF"}