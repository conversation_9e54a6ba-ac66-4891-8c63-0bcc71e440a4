{"version": 3, "sources": ["../../../src/server/route-matcher-providers/app-route-route-matcher-provider.ts"], "sourcesContent": ["import { isAppRouteRoute } from '../../lib/is-app-route-route'\nimport { APP_PATHS_MANIFEST } from '../../shared/lib/constants'\nimport { RouteKind } from '../route-kind'\nimport { AppRouteRouteMatcher } from '../route-matchers/app-route-route-matcher'\nimport type {\n  Manifest,\n  ManifestLoader,\n} from './helpers/manifest-loaders/manifest-loader'\nimport { ManifestRouteMatcherProvider } from './manifest-route-matcher-provider'\nimport { AppNormalizers } from '../normalizers/built/app'\n\nexport class AppRouteRouteMatcherProvider extends ManifestRouteMatcherProvider<AppRouteRouteMatcher> {\n  private readonly normalizers: AppNormalizers\n\n  constructor(distDir: string, manifestLoader: ManifestLoader) {\n    super(APP_PATHS_MANIFEST, manifestLoader)\n\n    this.normalizers = new AppNormalizers(distDir)\n  }\n\n  protected async transform(\n    manifest: Manifest\n  ): Promise<ReadonlyArray<AppRouteRouteMatcher>> {\n    // This matcher only matches app routes.\n    const pages = Object.keys(manifest).filter((page) => isAppRouteRoute(page))\n\n    // Format the routes.\n    const matchers: Array<AppRouteRouteMatcher> = []\n    for (const page of pages) {\n      const filename = this.normalizers.filename.normalize(manifest[page])\n      const pathname = this.normalizers.pathname.normalize(page)\n      const bundlePath = this.normalizers.bundlePath.normalize(page)\n\n      matchers.push(\n        new AppRouteRouteMatcher({\n          kind: RouteKind.APP_ROUTE,\n          pathname,\n          page,\n          bundlePath,\n          filename,\n        })\n      )\n    }\n\n    return matchers\n  }\n}\n"], "names": ["isAppRouteRoute", "APP_PATHS_MANIFEST", "RouteKind", "AppRouteRouteMatcher", "ManifestRouteMatcherProvider", "AppNormalizers", "AppRouteRouteMatcherProvider", "constructor", "distDir", "manifest<PERSON><PERSON>der", "normalizers", "transform", "manifest", "pages", "Object", "keys", "filter", "page", "matchers", "filename", "normalize", "pathname", "bundlePath", "push", "kind", "APP_ROUTE"], "mappings": "AAAA,SAASA,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,kBAAkB,QAAQ,6BAA4B;AAC/D,SAASC,SAAS,QAAQ,gBAAe;AACzC,SAASC,oBAAoB,QAAQ,4CAA2C;AAKhF,SAASC,4BAA4B,QAAQ,oCAAmC;AAChF,SAASC,cAAc,QAAQ,2BAA0B;AAEzD,OAAO,MAAMC,qCAAqCF;IAGhDG,YAAYC,OAAe,EAAEC,cAA8B,CAAE;QAC3D,KAAK,CAACR,oBAAoBQ;QAE1B,IAAI,CAACC,WAAW,GAAG,IAAIL,eAAeG;IACxC;IAEA,MAAgBG,UACdC,QAAkB,EAC4B;QAC9C,wCAAwC;QACxC,MAAMC,QAAQC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,OAASjB,gBAAgBiB;QAErE,qBAAqB;QACrB,MAAMC,WAAwC,EAAE;QAChD,KAAK,MAAMD,QAAQJ,MAAO;YACxB,MAAMM,WAAW,IAAI,CAACT,WAAW,CAACS,QAAQ,CAACC,SAAS,CAACR,QAAQ,CAACK,KAAK;YACnE,MAAMI,WAAW,IAAI,CAACX,WAAW,CAACW,QAAQ,CAACD,SAAS,CAACH;YACrD,MAAMK,aAAa,IAAI,CAACZ,WAAW,CAACY,UAAU,CAACF,SAAS,CAACH;YAEzDC,SAASK,IAAI,CACX,IAAIpB,qBAAqB;gBACvBqB,MAAMtB,UAAUuB,SAAS;gBACzBJ;gBACAJ;gBACAK;gBACAH;YACF;QAEJ;QAEA,OAAOD;IACT;AACF"}