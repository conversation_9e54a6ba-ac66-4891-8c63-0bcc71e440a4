"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(home)/layout",{

/***/ "(app-pages-browser)/./node_modules/@nextui-org/ripple/dist/chunk-MRKSP4U5.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@nextui-org/ripple/dist/chunk-MRKSP4U5.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ripple_default: () => (/* binding */ ripple_default)\n/* harmony export */ });\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/LazyMotion/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/m/proxy.mjs\");\n/* harmony import */ var _nextui_org_shared_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @nextui-org/shared-utils */ \"(app-pages-browser)/./node_modules/@nextui-org/shared-utils/dist/chunk-MNNRULGA.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ripple_default auto */ // src/ripple.tsx\n\n\n\nvar domAnimation = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_nextui-org_dom-animation_dist_index_mjs-_3c370\").then(__webpack_require__.bind(__webpack_require__, /*! @nextui-org/dom-animation */ \"(app-pages-browser)/./node_modules/@nextui-org/dom-animation/dist/index.mjs\")).then((res)=>res.default);\nvar Ripple = (props)=>{\n    const { ripples = [], motionProps, color = \"currentColor\", style, onClear } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: ripples.map((ripple)=>{\n            const duration = (0,_nextui_org_shared_utils__WEBPACK_IMPORTED_MODULE_1__.clamp)(0.01 * ripple.size, 0.2, ripple.size > 100 ? 0.75 : 0.5);\n            return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_2__.LazyMotion, {\n                features: domAnimation,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_3__.AnimatePresence, {\n                    mode: \"popLayout\",\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(framer_motion__WEBPACK_IMPORTED_MODULE_4__.m.span, {\n                        animate: {\n                            transform: \"scale(2)\",\n                            opacity: 0\n                        },\n                        className: \"nextui-ripple\",\n                        exit: {\n                            opacity: 0\n                        },\n                        initial: {\n                            transform: \"scale(0)\",\n                            opacity: 0.35\n                        },\n                        style: {\n                            position: \"absolute\",\n                            backgroundColor: color,\n                            borderRadius: \"100%\",\n                            transformOrigin: \"center\",\n                            pointerEvents: \"none\",\n                            overflow: \"hidden\",\n                            inset: 0,\n                            zIndex: 0,\n                            top: ripple.y,\n                            left: ripple.x,\n                            width: \"\".concat(ripple.size, \"px\"),\n                            height: \"\".concat(ripple.size, \"px\"),\n                            ...style\n                        },\n                        transition: {\n                            duration\n                        },\n                        onAnimationComplete: ()=>{\n                            onClear(ripple.key);\n                        },\n                        ...motionProps\n                    })\n                })\n            }, ripple.key);\n        })\n    });\n};\n_c = Ripple;\nRipple.displayName = \"NextUI.Ripple\";\nvar ripple_default = Ripple;\n\nvar _c;\n$RefreshReg$(_c, \"Ripple\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@nextui-org/ripple/dist/chunk-MRKSP4U5.mjs\n"));

/***/ })

});