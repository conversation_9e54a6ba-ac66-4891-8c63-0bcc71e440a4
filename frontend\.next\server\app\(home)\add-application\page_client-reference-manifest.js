globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/(home)/add-application/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/ClientProviders.tsx":{"*":{"id":"(ssr)/./src/components/global/ClientProviders.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/ErrorBoundary.tsx":{"*":{"id":"(ssr)/./src/components/global/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(ssr)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@nextui-org/button/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/@nextui-org/button/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(home)/layout.tsx":{"*":{"id":"(ssr)/./src/app/(home)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specific/AlertSection/index.tsx":{"*":{"id":"(ssr)/./src/components/specific/AlertSection/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specific/CallUs/index.tsx":{"*":{"id":"(ssr)/./src/components/specific/CallUs/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specific/EmergencyForm/index.tsx":{"*":{"id":"(ssr)/./src/components/specific/EmergencyForm/index.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\font\\local\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/Tajawal-Regular.ttf\",\"weight\":\"400\"},{\"path\":\"./fonts/Tajawal-Bold.ttf\",\"weight\":\"700\"},{\"path\":\"./fonts/Tajawal-ExtraBold.ttf\",\"weight\":\"800\"},{\"path\":\"./fonts/Tajawal-Black.ttf\",\"weight\":\"900\"}],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}":{"id":"(app-pages-browser)/./node_modules/next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/Tajawal-Regular.ttf\",\"weight\":\"400\"},{\"path\":\"./fonts/Tajawal-Bold.ttf\",\"weight\":\"700\"},{\"path\":\"./fonts/Tajawal-ExtraBold.ttf\",\"weight\":\"800\"},{\"path\":\"./fonts/Tajawal-Black.ttf\",\"weight\":\"900\"}],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\sonner\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx":{"id":"(app-pages-browser)/./src/components/global/ClientProviders.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ErrorBoundary.tsx":{"id":"(app-pages-browser)/./src/components/global/ErrorBoundary.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx":{"id":"(app-pages-browser)/./src/app/error.tsx","name":"*","chunks":["app/error","static/chunks/app/error.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\@nextui-org\\button\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/@nextui-org/button/dist/index.mjs","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout.tsx":{"id":"(app-pages-browser)/./src/app/(home)/layout.tsx","name":"*","chunks":["app/(home)/layout","static/chunks/app/(home)/layout.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\AlertSection\\index.tsx":{"id":"(app-pages-browser)/./src/components/specific/AlertSection/index.tsx","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\CallUs\\index.tsx":{"id":"(app-pages-browser)/./src/components/specific/CallUs/index.tsx","name":"*","chunks":["app/(home)/page","static/chunks/app/(home)/page.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\specific\\EmergencyForm\\index.tsx":{"id":"(app-pages-browser)/./src/components/specific/EmergencyForm/index.tsx","name":"*","chunks":["app/(home)/add-application/page","static/chunks/app/(home)/add-application/page.js"],"async":false}},"entryCSSFiles":{"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\":[],"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error":[],"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\not-found":[],"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\layout":[],"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\page":[],"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\(home)\\add-application\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/sonner/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/sonner/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/ClientProviders.tsx":{"*":{"id":"(rsc)/./src/components/global/ClientProviders.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/global/ErrorBoundary.tsx":{"*":{"id":"(rsc)/./src/components/global/ErrorBoundary.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/error.tsx":{"*":{"id":"(rsc)/./src/app/error.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/@nextui-org/button/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/(home)/layout.tsx":{"*":{"id":"(rsc)/./src/app/(home)/layout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specific/AlertSection/index.tsx":{"*":{"id":"(rsc)/./src/components/specific/AlertSection/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specific/CallUs/index.tsx":{"*":{"id":"(rsc)/./src/components/specific/CallUs/index.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/specific/EmergencyForm/index.tsx":{"*":{"id":"(rsc)/./src/components/specific/EmergencyForm/index.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}