"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/googlecallback/page",{

/***/ "(app-pages-browser)/./src/app/googlecallback/page.tsx":
/*!*****************************************!*\
  !*** ./src/app/googlecallback/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GoogleCallbackPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-cookie */ \"(app-pages-browser)/./node_modules/react-cookie/esm/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Spinner!=!@nextui-org/react */ \"(app-pages-browser)/./node_modules/@nextui-org/spinner/dist/chunk-TDOFO53L.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction GoogleCallbackPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const [cookies, setCookie] = (0,react_cookie__WEBPACK_IMPORTED_MODULE_4__.useCookies)();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('loading');\n    const [errorMessage, setErrorMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GoogleCallbackPage.useEffect\": ()=>{\n            const handleGoogleCallback = {\n                \"GoogleCallbackPage.useEffect.handleGoogleCallback\": async ()=>{\n                    try {\n                        // Get the authorization code from URL parameters\n                        const code = searchParams.get('code');\n                        const error = searchParams.get('error');\n                        if (error) {\n                            setStatus('error');\n                            setErrorMessage('تم إلغاء تسجيل الدخول بواسطة Google');\n                            return;\n                        }\n                        if (!code) {\n                            setStatus('error');\n                            setErrorMessage('لم يتم الحصول على رمز التفويض من Google');\n                            return;\n                        }\n                        // Send the authorization code to the backend\n                        const response = await (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.fetcher)(\"/auth/google/\", {\n                            code\n                        }, \"POST\");\n                        if (response.ok) {\n                            const data = await response.json();\n                            // Set the JWT tokens in cookies\n                            setCookie(\"access\", data.access_token, {\n                                path: \"/\"\n                            });\n                            setCookie(\"refresh\", data.refresh_token, {\n                                path: \"/\"\n                            });\n                            setStatus('success');\n                            // Redirect to home page after successful authentication\n                            setTimeout({\n                                \"GoogleCallbackPage.useEffect.handleGoogleCallback\": ()=>{\n                                    router.push(\"/\");\n                                }\n                            }[\"GoogleCallbackPage.useEffect.handleGoogleCallback\"], 1500);\n                        } else {\n                            const errorData = await response.json();\n                            setStatus('error');\n                            setErrorMessage(errorData.detail || 'فشل في تسجيل الدخول بواسطة Google');\n                        }\n                    } catch (error) {\n                        console.error('Google OAuth callback error:', error);\n                        setStatus('error');\n                        setErrorMessage('حدث خطأ أثناء تسجيل الدخول');\n                    }\n                }\n            }[\"GoogleCallbackPage.useEffect.handleGoogleCallback\"];\n            handleGoogleCallback();\n        }\n    }[\"GoogleCallbackPage.useEffect\"], [\n        searchParams,\n        setCookie,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white rounded-lg shadow-md p-8 text-center\",\n            children: [\n                status === 'loading' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Spinner_nextui_org_react__WEBPACK_IMPORTED_MODULE_5__.spinner_default, {\n                            size: \"lg\",\n                            color: \"primary\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-4 text-xl font-semibold text-gray-900\",\n                            children: \"جاري تسجيل الدخول...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"يرجى الانتظار بينما نقوم بتسجيل دخولك بواسطة Google\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'success' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 mx-auto bg-green-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-green-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M5 13l4 4L19 7\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-4 text-xl font-semibold text-green-900\",\n                            children: \"تم تسجيل الدخول بنجاح!\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: \"سيتم توجيهك إلى الصفحة الرئيسية...\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true),\n                status === 'error' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-600\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                                lineNumber: 100,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"mt-4 text-xl font-semibold text-red-900\",\n                            children: \"فشل تسجيل الدخول\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-2 text-gray-600\",\n                            children: errorMessage\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>router.push('/auth'),\n                            className: \"mt-4 w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary/90 transition-colors\",\n                            children: \"العودة إلى صفحة تسجيل الدخول\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\googlecallback\\\\page.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(GoogleCallbackPage, \"64mjAsOUHvxcDHkKPqvd1k9qroc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        react_cookie__WEBPACK_IMPORTED_MODULE_4__.useCookies\n    ];\n});\n_c = GoogleCallbackPage;\nvar _c;\n$RefreshReg$(_c, \"GoogleCallbackPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/googlecallback/page.tsx\n"));

/***/ })

});