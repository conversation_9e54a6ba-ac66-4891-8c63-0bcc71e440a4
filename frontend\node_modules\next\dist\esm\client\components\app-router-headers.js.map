{"version": 3, "sources": ["../../../src/client/components/app-router-headers.ts"], "sourcesContent": ["export const RSC_HEADER = 'RSC' as const\nexport const ACTION_HEADER = 'Next-Action' as const\n// TODO: Instead of sending the full router state, we only need to send the\n// segment path. Saves bytes. Then we could also use this field for segment\n// prefetches, which also need to specify a particular segment.\nexport const NEXT_ROUTER_STATE_TREE_HEADER = 'Next-Router-State-Tree' as const\nexport const NEXT_ROUTER_PREFETCH_HEADER = 'Next-Router-Prefetch' as const\n// This contains the path to the segment being prefetched.\n// TODO: If we change Next-Router-State-Tree to be a segment path, we can use\n// that instead. Then Next-Router-Prefetch and Next-Router-Segment-Prefetch can\n// be merged into a single enum.\nexport const NEXT_ROUTER_SEGMENT_PREFETCH_HEADER =\n  'Next-Router-Segment-Prefetch' as const\nexport const NEXT_HMR_REFRESH_HEADER = 'Next-HMR-Refresh' as const\nexport const NEXT_URL = 'Next-Url' as const\nexport const RSC_CONTENT_TYPE_HEADER = 'text/x-component' as const\n\nexport const FLIGHT_HEADERS = [\n  RSC_HEADER,\n  NEXT_ROUTER_STATE_TREE_HEADER,\n  NEXT_ROUTER_PREFETCH_HEADER,\n  NEXT_HMR_REFRESH_HEADER,\n  NEXT_ROUTER_SEGMENT_PREFETCH_HEADER,\n] as const\n\nexport const NEXT_RSC_UNION_QUERY = '_rsc' as const\n\nexport const NEXT_ROUTER_STALE_TIME_HEADER = 'x-nextjs-stale-time' as const\nexport const NEXT_DID_POSTPONE_HEADER = 'x-nextjs-postponed' as const\nexport const NEXT_REWRITTEN_PATH_HEADER = 'x-nextjs-rewritten-path' as const\nexport const NEXT_REWRITTEN_QUERY_HEADER = 'x-nextjs-rewritten-query' as const\nexport const NEXT_IS_PRERENDER_HEADER = 'x-nextjs-prerender' as const\n"], "names": ["RSC_HEADER", "ACTION_HEADER", "NEXT_ROUTER_STATE_TREE_HEADER", "NEXT_ROUTER_PREFETCH_HEADER", "NEXT_ROUTER_SEGMENT_PREFETCH_HEADER", "NEXT_HMR_REFRESH_HEADER", "NEXT_URL", "RSC_CONTENT_TYPE_HEADER", "FLIGHT_HEADERS", "NEXT_RSC_UNION_QUERY", "NEXT_ROUTER_STALE_TIME_HEADER", "NEXT_DID_POSTPONE_HEADER", "NEXT_REWRITTEN_PATH_HEADER", "NEXT_REWRITTEN_QUERY_HEADER", "NEXT_IS_PRERENDER_HEADER"], "mappings": "AAAA,OAAO,MAAMA,aAAa,MAAc;AACxC,OAAO,MAAMC,gBAAgB,cAAsB;AACnD,2EAA2E;AAC3E,2EAA2E;AAC3E,+DAA+D;AAC/D,OAAO,MAAMC,gCAAgC,yBAAiC;AAC9E,OAAO,MAAMC,8BAA8B,uBAA+B;AAC1E,0DAA0D;AAC1D,6EAA6E;AAC7E,+EAA+E;AAC/E,gCAAgC;AAChC,OAAO,MAAMC,sCACX,+BAAuC;AACzC,OAAO,MAAMC,0BAA0B,mBAA2B;AAClE,OAAO,MAAMC,WAAW,WAAmB;AAC3C,OAAO,MAAMC,0BAA0B,mBAA2B;AAElE,OAAO,MAAMC,iBAAiB;IAC5BR;IACAE;IACAC;IACAE;IACAD;CACD,CAAS;AAEV,OAAO,MAAMK,uBAAuB,OAAe;AAEnD,OAAO,MAAMC,gCAAgC,sBAA8B;AAC3E,OAAO,MAAMC,2BAA2B,qBAA6B;AACrE,OAAO,MAAMC,6BAA6B,0BAAkC;AAC5E,OAAO,MAAMC,8BAA8B,2BAAmC;AAC9E,OAAO,MAAMC,2BAA2B,qBAA6B"}