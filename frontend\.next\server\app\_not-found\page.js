/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/module.compiled.js?cc4a\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst notFound0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/not-found.tsx */ \"(rsc)/./src/app/not-found.tsx\"));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/forbidden-error */ \"(rsc)/./node_modules/next/dist/client/components/forbidden-error.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/unauthorized-error */ \"(rsc)/./node_modules/next/dist/client/components/unauthorized-error.js\", 23));\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                notFound0,\n                \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [module1, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'error': [module2, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\"],\n'not-found': [module3, \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\"],\n'forbidden': [module4, \"next/dist/client/components/forbidden-error\"],\n'unauthorized': [module5, \"next/dist/client/components/unauthorized-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@nextui-org/button/dist/index.mjs */ \"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MG5leHR1aS1vcmclNUMlNUNidXR0b24lNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE1BQXdLO0FBQ3hLO0FBQ0EsZ05BQXNMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQG5leHR1aS1vcmdcXFxcYnV0dG9uXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRzpcXFxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXFxcYXBwICgyKVxcXFxhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(rsc)/./node_modules/sonner/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ClientProviders.tsx */ \"(rsc)/./src/components/global/ClientProviders.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ErrorBoundary.tsx */ \"(rsc)/./src/components/global/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(rsc)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQTZHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", await props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIkc6XFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcYXBwICgyKVxcYXBwXFxmcm9udGVuZFxcc3JjXFxhcHBcXGZhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIl0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IGFzeW5jIChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBhd2FpdCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ }),

/***/ "(rsc)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\app\\error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"090f88dbe170\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxnbG9iYWxzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjA5MGY4OGRiZTE3MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/local/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"\",\"arguments\":[{\"src\":[{\"path\":\"./fonts/Tajawal-Regular.ttf\",\"weight\":\"400\"},{\"path\":\"./fonts/Tajawal-Bold.ttf\",\"weight\":\"700\"},{\"path\":\"./fonts/Tajawal-ExtraBold.ttf\",\"weight\":\"800\"},{\"path\":\"./fonts/Tajawal-Black.ttf\",\"weight\":\"900\"}],\"variable\":\"--font-tajawal\",\"display\":\"swap\"}],\"variableName\":\"tajawal\"} */ \"(rsc)/./node_modules/next/font/local/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"\\\",\\\"arguments\\\":[{\\\"src\\\":[{\\\"path\\\":\\\"./fonts/Tajawal-Regular.ttf\\\",\\\"weight\\\":\\\"400\\\"},{\\\"path\\\":\\\"./fonts/Tajawal-Bold.ttf\\\",\\\"weight\\\":\\\"700\\\"},{\\\"path\\\":\\\"./fonts/Tajawal-ExtraBold.ttf\\\",\\\"weight\\\":\\\"800\\\"},{\\\"path\\\":\\\"./fonts/Tajawal-Black.ttf\\\",\\\"weight\\\":\\\"900\\\"}],\\\"variable\\\":\\\"--font-tajawal\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"tajawal\\\"}\");\n/* harmony import */ var next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _components_global_ClientProviders__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/global/ClientProviders */ \"(rsc)/./src/components/global/ClientProviders.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(rsc)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_global_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/global/ErrorBoundary */ \"(rsc)/./src/components/global/ErrorBoundary.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"Palastine Emergency\",\n    description: \"Comming for help when you need us\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"ar\",\n        dir: \"rtl\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_local_target_css_path_src_app_layout_tsx_import_arguments_src_path_fonts_Tajawal_Regular_ttf_weight_400_path_fonts_Tajawal_Bold_ttf_weight_700_path_fonts_Tajawal_ExtraBold_ttf_weight_800_path_fonts_Tajawal_Black_ttf_weight_900_variable_font_tajawal_display_swap_variableName_tajawal___WEBPACK_IMPORTED_MODULE_5___default().variable)} antialiased`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_ClientProviders__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_global_ErrorBoundary__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_3__.Toaster, {\n                    richColors: true,\n                    position: \"top-right\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFRTUE7QUFMZ0I7QUFDMkM7QUFDakM7QUFDNkI7QUFhdEQsTUFBTUksV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtBQUNmLEVBQUM7QUFFYyxTQUFTQyxXQUFXLEVBQ2pDQyxRQUFRLEVBR1I7SUFDQSxxQkFDRSw4REFBQ0M7UUFBS0MsTUFBSztRQUFLQyxLQUFJO2tCQUNsQiw0RUFBQ0M7WUFBS0MsV0FBVyxHQUFHYix1VkFBZ0IsQ0FBQyxZQUFZLENBQUM7OzhCQUNoRCw4REFBQ0MsMEVBQWVBOzhCQUNkLDRFQUFDRSx3RUFBYUE7a0NBQUVLOzs7Ozs7Ozs7Ozs4QkFFbEIsOERBQUNOLDJDQUFPQTtvQkFBQ2EsVUFBVTtvQkFBQ0MsVUFBUzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJckMiLCJzb3VyY2VzIjpbIkc6XFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcYXBwICgyKVxcYXBwXFxmcm9udGVuZFxcc3JjXFxhcHBcXGxheW91dC50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB0eXBlIHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiXG5pbXBvcnQgbG9jYWxGb250IGZyb20gXCJuZXh0L2ZvbnQvbG9jYWxcIlxuaW1wb3J0IFwiLi9nbG9iYWxzLmNzc1wiXG5pbXBvcnQgQ2xpZW50UHJvdmlkZXJzIGZyb20gXCJAL2NvbXBvbmVudHMvZ2xvYmFsL0NsaWVudFByb3ZpZGVyc1wiXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSBcInNvbm5lclwiXG5pbXBvcnQgRXJyb3JCb3VuZGFyeSBmcm9tIFwiQC9jb21wb25lbnRzL2dsb2JhbC9FcnJvckJvdW5kYXJ5XCJcblxuY29uc3QgdGFqYXdhbCA9IGxvY2FsRm9udCh7XG4gIHNyYzogW1xuICAgIHsgcGF0aDogXCIuL2ZvbnRzL1RhamF3YWwtUmVndWxhci50dGZcIiwgd2VpZ2h0OiBcIjQwMFwiIH0sXG4gICAgeyBwYXRoOiBcIi4vZm9udHMvVGFqYXdhbC1Cb2xkLnR0ZlwiLCB3ZWlnaHQ6IFwiNzAwXCIgfSxcbiAgICB7IHBhdGg6IFwiLi9mb250cy9UYWphd2FsLUV4dHJhQm9sZC50dGZcIiwgd2VpZ2h0OiBcIjgwMFwiIH0sXG4gICAgeyBwYXRoOiBcIi4vZm9udHMvVGFqYXdhbC1CbGFjay50dGZcIiwgd2VpZ2h0OiBcIjkwMFwiIH0sXG4gIF0sXG4gIHZhcmlhYmxlOiBcIi0tZm9udC10YWphd2FsXCIsXG4gIGRpc3BsYXk6IFwic3dhcFwiLFxufSlcblxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcbiAgdGl0bGU6IFwiUGFsYXN0aW5lIEVtZXJnZW5jeVwiLFxuICBkZXNjcmlwdGlvbjogXCJDb21taW5nIGZvciBoZWxwIHdoZW4geW91IG5lZWQgdXNcIixcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG59Pikge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJhclwiIGRpcj1cInJ0bFwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPXtgJHt0YWphd2FsLnZhcmlhYmxlfSBhbnRpYWxpYXNlZGB9PlxuICAgICAgICA8Q2xpZW50UHJvdmlkZXJzPlxuICAgICAgICAgIDxFcnJvckJvdW5kYXJ5PntjaGlsZHJlbn08L0Vycm9yQm91bmRhcnk+XG4gICAgICAgIDwvQ2xpZW50UHJvdmlkZXJzPlxuICAgICAgICA8VG9hc3RlciByaWNoQ29sb3JzIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCIgLz5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ0YWphd2FsIiwiQ2xpZW50UHJvdmlkZXJzIiwiVG9hc3RlciIsIkVycm9yQm91bmRhcnkiLCJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImRpciIsImJvZHkiLCJjbGFzc05hbWUiLCJ2YXJpYWJsZSIsInJpY2hDb2xvcnMiLCJwb3NpdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/not-found.tsx":
/*!*******************************!*\
  !*** ./src/app/not-found.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NotFound)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextui-org/button */ \"(rsc)/./node_modules/@nextui-org/button/dist/index.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n\n\n\nfunction NotFound() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-6xl font-bold mb-4\",\n                children: \"404\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-semibold mb-6\",\n                children: \"الصفحة غير موجودة\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 8,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-8 text-gray-600 max-w-md\",\n                children: \"عذراً، الصفحة التي تبحث عنها غير موجودة أو تم نقلها أو حذفها.\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 9,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                as: (next_link__WEBPACK_IMPORTED_MODULE_1___default()),\n                href: \"/\",\n                color: \"primary\",\n                children: \"العودة للصفحة الرئيسية\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\not-found.tsx\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL25vdC1mb3VuZC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUEyQztBQUNmO0FBRWIsU0FBU0U7SUFDdEIscUJBQ0UsOERBQUNDO1FBQUlDLFdBQVU7OzBCQUNiLDhEQUFDQztnQkFBR0QsV0FBVTswQkFBMEI7Ozs7OzswQkFDeEMsOERBQUNFO2dCQUFHRixXQUFVOzBCQUE4Qjs7Ozs7OzBCQUM1Qyw4REFBQ0c7Z0JBQUVILFdBQVU7MEJBQThCOzs7Ozs7MEJBQzNDLDhEQUFDSixzREFBTUE7Z0JBQUNRLElBQUlQLGtEQUFJQTtnQkFBRVEsTUFBSztnQkFBSUMsT0FBTTswQkFBVTs7Ozs7Ozs7Ozs7O0FBS2pEIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcYXBwXFxub3QtZm91bmQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJ1dHRvbiB9IGZyb20gXCJAbmV4dHVpLW9yZy9idXR0b25cIlxuaW1wb3J0IExpbmsgZnJvbSBcIm5leHQvbGlua1wiXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vdEZvdW5kKCkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgbWluLWgtc2NyZWVuIHAtNiB0ZXh0LWNlbnRlclwiPlxuICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtNnhsIGZvbnQtYm9sZCBtYi00XCI+NDA0PC9oMT5cbiAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LXNlbWlib2xkIG1iLTZcIj7Yp9mE2LXZgdit2Kkg2LrZitixINmF2YjYrNmI2K/YqTwvaDI+XG4gICAgICA8cCBjbGFzc05hbWU9XCJtYi04IHRleHQtZ3JheS02MDAgbWF4LXctbWRcIj7Yudiw2LHYp9mL2Iwg2KfZhNi12YHYrdipINin2YTYqtmKINiq2KjYrdirINi52YbZh9inINi62YrYsSDZhdmI2KzZiNiv2Kkg2KPZiCDYqtmFINmG2YLZhNmH2Kcg2KPZiCDYrdiw2YHZh9inLjwvcD5cbiAgICAgIDxCdXR0b24gYXM9e0xpbmt9IGhyZWY9XCIvXCIgY29sb3I9XCJwcmltYXJ5XCI+XG4gICAgICAgINin2YTYudmI2K/YqSDZhNmE2LXZgdit2Kkg2KfZhNix2KbZitiz2YrYqVxuICAgICAgPC9CdXR0b24+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJCdXR0b24iLCJMaW5rIiwiTm90Rm91bmQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMSIsImgyIiwicCIsImFzIiwiaHJlZiIsImNvbG9yIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/not-found.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/global/ClientProviders.tsx":
/*!***************************************************!*\
  !*** ./src/components/global/ClientProviders.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ClientProviders.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./src/components/global/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/global/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"G:\\Graduation project 2025\\app (2)\\app\\frontend\\src\\components\\global\\ErrorBoundary.tsx",
"default",
));


/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/@nextui-org/button/dist/index.mjs */ \"(ssr)/./node_modules/@nextui-org/button/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/app-dir/link.js */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1QyU0MG5leHR1aS1vcmclNUMlNUNidXR0b24lNUMlNUNkaXN0JTVDJTVDaW5kZXgubWpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQnV0dG9uJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDYXBwLWRpciU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJfX2VzTW9kdWxlJTIyJTJDJTIyZGVmYXVsdCUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNE1BQXdLO0FBQ3hLO0FBQ0EsZ05BQXNMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCdXR0b25cIl0gKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxub2RlX21vZHVsZXNcXFxcQG5leHR1aS1vcmdcXFxcYnV0dG9uXFxcXGRpc3RcXFxcaW5kZXgubWpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJfX2VzTW9kdWxlXCIsXCJkZWZhdWx0XCJdICovIFwiRzpcXFxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXFxcYXBwICgyKVxcXFxhcHBcXFxcZnJvbnRlbmRcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcYXBwLWRpclxcXFxsaW5rLmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5C%40nextui-org%5C%5Cbutton%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Button%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Chttp-access-fallback%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Casync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cmetadata%5C%5Cmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./node_modules/sonner/dist/index.mjs */ \"(ssr)/./node_modules/sonner/dist/index.mjs\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ClientProviders.tsx */ \"(ssr)/./src/components/global/ClientProviders.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/global/ErrorBoundary.tsx */ \"(ssr)/./src/components/global/ErrorBoundary.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Clocal%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22src%5C%22%3A%5B%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Regular.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22400%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Bold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22700%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-ExtraBold.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22800%5C%22%7D%2C%7B%5C%22path%5C%22%3A%5C%22.%2Ffonts%2FTajawal-Black.ttf%5C%22%2C%5C%22weight%5C%22%3A%5C%22900%5C%22%7D%5D%2C%5C%22variable%5C%22%3A%5C%22--font-tajawal%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22tajawal%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Cnode_modules%5C%5Csonner%5C%5Cdist%5C%5Cindex.mjs%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CClientProviders.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5Cglobal%5C%5CErrorBoundary.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/error.tsx */ \"(ssr)/./src/app/error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkclM0ElNUMlNUNHcmFkdWF0aW9uJTIwcHJvamVjdCUyMDIwMjUlNUMlNUNhcHAlMjAoMiklNUMlNUNhcHAlNUMlNUNmcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2Vycm9yLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa0pBQTZHIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJHOlxcXFxHcmFkdWF0aW9uIHByb2plY3QgMjAyNVxcXFxhcHAgKDIpXFxcXGFwcFxcXFxmcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGVycm9yLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22G%3A%5C%5CGraduation%20project%202025%5C%5Capp%20(2)%5C%5Capp%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cerror.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/error.tsx":
/*!***************************!*\
  !*** ./src/app/error.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Error)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextui-org/button */ \"(ssr)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction Error({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Error.useEffect\": ()=>{\n            console.error(\"Application error:\", error);\n        }\n    }[\"Error.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center justify-center min-h-screen p-6 text-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"حدث خطأ غير متوقع\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mb-6 text-gray-600\",\n                children: \"نعتذر عن هذا الخطأ. يرجى المحاولة مرة أخرى أو العودة إلى الصفحة الرئيسية.\"\n            }, void 0, false, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                        color: \"primary\",\n                        onClick: ()=>reset(),\n                        children: \"إعادة المحاولة\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                        variant: \"bordered\",\n                        onClick: ()=>window.location.href = \"/\",\n                        children: \"العودة للصفحة الرئيسية\"\n                    }, void 0, false, {\n                        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                        lineNumber: 25,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n                lineNumber: 21,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\app\\\\error.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/global/ClientProviders.tsx":
/*!***************************************************!*\
  !*** ./src/components/global/ClientProviders.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientProviders)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=NextUIProvider!=!@nextui-org/react */ \"(ssr)/./node_modules/@nextui-org/system/dist/chunk-MNMJVVXA.mjs\");\n/* harmony import */ var react_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-cookie */ \"(ssr)/./node_modules/react-cookie/esm/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ClientProviders({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_cookie__WEBPACK_IMPORTED_MODULE_1__.CookiesProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_NextUIProvider_nextui_org_react__WEBPACK_IMPORTED_MODULE_2__.NextUIProvider, {\n            children: children\n        }, void 0, false, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ClientProviders.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9nbG9iYWwvQ2xpZW50UHJvdmlkZXJzLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFFbUQ7QUFDSjtBQU9oQyxTQUFTRSxnQkFBZ0IsRUFBRUMsUUFBUSxFQUFTO0lBQ3pELHFCQUNFLDhEQUFDRix5REFBZUE7a0JBQ2QsNEVBQUNELGtHQUFjQTtzQkFBRUc7Ozs7Ozs7Ozs7O0FBR3ZCIiwic291cmNlcyI6WyJHOlxcR3JhZHVhdGlvbiBwcm9qZWN0IDIwMjVcXGFwcCAoMilcXGFwcFxcZnJvbnRlbmRcXHNyY1xcY29tcG9uZW50c1xcZ2xvYmFsXFxDbGllbnRQcm92aWRlcnMudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBOZXh0VUlQcm92aWRlciB9IGZyb20gXCJAbmV4dHVpLW9yZy9yZWFjdFwiO1xuaW1wb3J0IHsgQ29va2llc1Byb3ZpZGVyIH0gZnJvbSBcInJlYWN0LWNvb2tpZVwiO1xuaW1wb3J0IHR5cGUgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbnR5cGUgUHJvcHMgPSB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBDbGllbnRQcm92aWRlcnMoeyBjaGlsZHJlbiB9OiBQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxDb29raWVzUHJvdmlkZXI+XG4gICAgICA8TmV4dFVJUHJvdmlkZXI+e2NoaWxkcmVufTwvTmV4dFVJUHJvdmlkZXI+XG4gICAgPC9Db29raWVzUHJvdmlkZXI+XG4gICk7XG59XG4iXSwibmFtZXMiOlsiTmV4dFVJUHJvdmlkZXIiLCJDb29raWVzUHJvdmlkZXIiLCJDbGllbnRQcm92aWRlcnMiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/global/ClientProviders.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/global/ErrorBoundary.tsx":
/*!*************************************************!*\
  !*** ./src/components/global/ErrorBoundary.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _nextui_org_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @nextui-org/button */ \"(ssr)/./node_modules/@nextui-org/button/dist/chunk-G5TSEPD3.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ErrorBoundary({ children }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ErrorBoundary.useEffect\": ()=>{\n            const handleError = {\n                \"ErrorBoundary.useEffect.handleError\": (event)=>{\n                    console.error(\"Error caught by error boundary:\", event.error);\n                    setHasError(true);\n                    // Prevent the error from propagating\n                    event.preventDefault();\n                }\n            }[\"ErrorBoundary.useEffect.handleError\"];\n            window.addEventListener(\"error\", handleError);\n            return ({\n                \"ErrorBoundary.useEffect\": ()=>{\n                    window.removeEventListener(\"error\", handleError);\n                }\n            })[\"ErrorBoundary.useEffect\"];\n        }\n    }[\"ErrorBoundary.useEffect\"], []);\n    if (hasError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-[50vh] p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"حدث خطأ غير متوقع\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                    lineNumber: 33,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6 text-gray-600\",\n                    children: \"نعتذر عن هذا الخطأ. يرجى تحديث الصفحة أو العودة إلى الصفحة الرئيسية.\"\n                }, void 0, false, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                            color: \"primary\",\n                            onClick: ()=>window.location.reload(),\n                            children: \"تحديث الصفحة\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_nextui_org_button__WEBPACK_IMPORTED_MODULE_2__.button_default, {\n                            variant: \"bordered\",\n                            onClick: ()=>window.location.href = \"/\",\n                            children: \"العودة للصفحة الرئيسية\"\n                        }, void 0, false, {\n                            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"G:\\\\Graduation project 2025\\\\app (2)\\\\app\\\\frontend\\\\src\\\\components\\\\global\\\\ErrorBoundary.tsx\",\n            lineNumber: 32,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/global/ErrorBoundary.tsx\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@nextui-org","vendor-chunks/framer-motion","vendor-chunks/next","vendor-chunks/motion-dom","vendor-chunks/motion-utils","vendor-chunks/@react-aria","vendor-chunks/tailwind-variants","vendor-chunks/sonner","vendor-chunks/react-cookie","vendor-chunks/universal-cookie","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=private-next-app-dir%2Fnot-found.tsx&appDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=G%3A%5CGraduation%20project%202025%5Capp%20(2)%5Capp%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();