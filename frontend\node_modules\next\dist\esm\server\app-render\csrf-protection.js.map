{"version": 3, "sources": ["../../../src/server/app-render/csrf-protection.ts"], "sourcesContent": ["// micromatch is only available at node runtime, so it cannot be used here since the code path that calls this function\n// can be run from edge. This is a simple implementation that safely achieves the required functionality.\n// the goal is to match the functionality for remotePatterns as defined here -\n// https://nextjs.org/docs/app/api-reference/components/image#remotepatterns\n// TODO - retrofit micromatch to work in edge and use that instead\nfunction matchWildcardDomain(domain: string, pattern: string) {\n  const domainParts = domain.split('.')\n  const patternParts = pattern.split('.')\n\n  if (patternParts.length < 1) {\n    // pattern is empty and therefore invalid to match against\n    return false\n  }\n\n  if (domainParts.length < patternParts.length) {\n    // domain has too few segments and thus cannot match\n    return false\n  }\n\n  let depth = 0\n  while (patternParts.length && depth++ < 2) {\n    const patternPart = patternParts.pop()\n    const domainPart = domainParts.pop()\n\n    switch (patternPart) {\n      case '':\n      case '*':\n      case '**': {\n        // invalid pattern. pattern segments must be non empty\n        // Additionally wildcards are only supported below the domain level\n        return false\n      }\n      default: {\n        if (domainPart !== patternPart) {\n          return false\n        }\n      }\n    }\n  }\n\n  while (patternParts.length) {\n    const patternPart = patternParts.pop()\n    const domainPart = domainParts.pop()\n\n    switch (patternPart) {\n      case '': {\n        // invalid pattern. pattern segments must be non empty\n        return false\n      }\n      case '*': {\n        // wildcard matches anything so we continue if the domain part is non-empty\n        if (domainPart) {\n          continue\n        } else {\n          return false\n        }\n      }\n      case '**': {\n        // if this is not the last item in the pattern the pattern is invalid\n        if (patternParts.length > 0) {\n          return false\n        }\n        // recursive wildcard matches anything so we terminate here if the domain part is non empty\n        return domainPart !== undefined\n      }\n      default: {\n        if (domainPart !== patternPart) {\n          return false\n        }\n      }\n    }\n  }\n\n  // We exhausted the pattern. If we also exhausted the domain we have a match\n  return domainParts.length === 0\n}\n\nexport const isCsrfOriginAllowed = (\n  originDomain: string,\n  allowedOrigins: string[] = []\n): boolean => {\n  return allowedOrigins.some(\n    (allowedOrigin) =>\n      allowedOrigin &&\n      (allowedOrigin === originDomain ||\n        matchWildcardDomain(originDomain, allowedOrigin))\n  )\n}\n"], "names": ["matchWildcardDomain", "domain", "pattern", "domainParts", "split", "patternParts", "length", "depth", "patternPart", "pop", "domainPart", "undefined", "isCsrfOriginAllowed", "originDomain", "<PERSON><PERSON><PERSON><PERSON>", "some", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": "AAAA,uHAAuH;AACvH,yGAAyG;AACzG,8EAA8E;AAC9E,4EAA4E;AAC5E,kEAAkE;AAClE,SAASA,oBAAoBC,MAAc,EAAEC,OAAe;IAC1D,MAAMC,cAAcF,OAAOG,KAAK,CAAC;IACjC,MAAMC,eAAeH,QAAQE,KAAK,CAAC;IAEnC,IAAIC,aAAaC,MAAM,GAAG,GAAG;QAC3B,0DAA0D;QAC1D,OAAO;IACT;IAEA,IAAIH,YAAYG,MAAM,GAAGD,aAAaC,MAAM,EAAE;QAC5C,oDAAoD;QACpD,OAAO;IACT;IAEA,IAAIC,QAAQ;IACZ,MAAOF,aAAaC,MAAM,IAAIC,UAAU,EAAG;QACzC,MAAMC,cAAcH,aAAaI,GAAG;QACpC,MAAMC,aAAaP,YAAYM,GAAG;QAElC,OAAQD;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBAAM;oBACT,sDAAsD;oBACtD,mEAAmE;oBACnE,OAAO;gBACT;YACA;gBAAS;oBACP,IAAIE,eAAeF,aAAa;wBAC9B,OAAO;oBACT;gBACF;QACF;IACF;IAEA,MAAOH,aAAaC,MAAM,CAAE;QAC1B,MAAME,cAAcH,aAAaI,GAAG;QACpC,MAAMC,aAAaP,YAAYM,GAAG;QAElC,OAAQD;YACN,KAAK;gBAAI;oBACP,sDAAsD;oBACtD,OAAO;gBACT;YACA,KAAK;gBAAK;oBACR,2EAA2E;oBAC3E,IAAIE,YAAY;wBACd;oBACF,OAAO;wBACL,OAAO;oBACT;gBACF;YACA,KAAK;gBAAM;oBACT,qEAAqE;oBACrE,IAAIL,aAAaC,MAAM,GAAG,GAAG;wBAC3B,OAAO;oBACT;oBACA,2FAA2F;oBAC3F,OAAOI,eAAeC;gBACxB;YACA;gBAAS;oBACP,IAAID,eAAeF,aAAa;wBAC9B,OAAO;oBACT;gBACF;QACF;IACF;IAEA,4EAA4E;IAC5E,OAAOL,YAAYG,MAAM,KAAK;AAChC;AAEA,OAAO,MAAMM,sBAAsB,CACjCC,cACAC,iBAA2B,EAAE;IAE7B,OAAOA,eAAeC,IAAI,CACxB,CAACC,gBACCA,iBACCA,CAAAA,kBAAkBH,gBACjBb,oBAAoBa,cAAcG,cAAa;AAEvD,EAAC"}